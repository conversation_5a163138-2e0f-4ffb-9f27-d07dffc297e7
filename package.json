{"name": "georedv3", "version": "3.0.28-RC5", "private": true, "description": "[![coverage report](http://git.squirrel.run/simpliciti-frontend/geored-v3/badges/dev/coverage.svg)](http://git.squirrel.run/simpliciti-frontend/geored-v3/-/commits/dev)", "author": "", "scripts": {"serve": "vite --host", "build": "NODE_OPTIONS='--max-old-space-size=4096' vite build", "custom-install": "npm ci --legacy-peer-deps", "jsdoc": "npx jsdoc -c jsdoc.js", "lint:watch": "npx nodemon --exec 'npm run lint' -e js,vue", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src", "format": "prettier .  --write", "dev": "npx vite", "dev:dev": "vite --mode=cidev", "dev:isoprod": "vite --mode=isoprod", "vite:preview": "vite preview", "test:unit": "jest --testPathPattern=unit", "test:e2e": "jest --testPathPattern=e2e", "test:unit:watch": "npx jest --silent --coverage=false --verbose --watch", "print-coverage": "node scripts/print-coverage/print-coverage.js"}, "main": ".eslintrc.js", "dependencies": {"@ag-grid-community/locale": "^33.3.2", "@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/preset-env": "^7.26.9", "@csstools/normalize.css": "^11.0.1", "@geoman-io/leaflet-geoman-free": "^2.14.2", "@googlemaps/js-api-loader": "^1.14.1", "@iconify/vue2": "^1.2.1", "@popperjs/core": "^2.0.6", "@sentry/tracing": "^5.29.2", "@sentry/vue": "^5.29.2", "@sentry/webpack-plugin": "^1.18.9", "@testing-library/jest-dom": "^5.11.2", "@types/jest": "^27.4.1", "@types/query-string": "^6.3.0", "@typescript-eslint/parser": "^6.18.1", "@volar/vue-language-plugin-pug": "^1.0.3", "@vue/compiler-sfc": "^3.2.31", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "^1.3.0", "@vue/vue2-jest": "^27.0.0", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/motion": "1.6.0", "ag-grid-community": "^33.3.1", "ag-grid-vue": "^31.3.4", "ajv": "^8.12.0", "animejs": "^3.2.1", "autoprefixer": "^10.3.6", "axios": "^1.8.3", "babel-eslint": "^10.1.0", "babel-plugin-module-resolver": "^4.1.0", "bootstrap": "4.5.3", "bootstrap-vue": "^2.4.2", "codeceptjs": "^3.7.3", "compression-webpack-plugin": "^11.1.0", "concurrently": "^7.3.0", "datatables.net-autofill-dt": "^2.3.4", "datatables.net-buttons-dt": "^1.6.1", "datatables.net-colreorder-dt": "^1.5.2", "datatables.net-dt": "^1.11.3", "datatables.net-fixedcolumns-dt": "^3.3.0", "datatables.net-fixedheader-dt": "^3.2.0", "datatables.net-keytable-dt": "^2.5.1", "datatables.net-responsive-dt": "^2.2.3", "datatables.net-rowgroup-dt": "^1.1.1", "datatables.net-rowreorder-dt": "^1.2.6", "datatables.net-scroller-dt": "^2.0.1", "datatables.net-searchpanes-dt": "^1.0.1", "datatables.net-select-dt": "^1.3.1", "dotenv": "^10.0.0", "draggabilly": "^2.3.0", "echarts": "^5.2.2", "eslint": "^8.31.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-json": "^3.1.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^9.3.0", "flush-promises": "^1.0.2", "hex-color-opacity": "^0.4.2", "hex-rgb": "^4.1.0", "html2canvas": "^1.4.1", "husky": "^4.2.5", "iconify-icon": "^2.3.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.5.1", "jquery": "^3.4.1", "jspdf": "3.0.1", "jspdf-autotable": "3.5.23", "jszip": "^3.4.0", "jwt-decode": "^3.1.2", "leaflet": "^1.9.4", "leaflet-arrowheads": "^1.2.3", "leaflet-defaulticon-compatibility": "^0.1.1", "leaflet-lasso": "^2.2.13", "leaflet-spin": "^1.1.2", "leaflet.gridlayer.googlemutant": "^0.13.5", "leaflet.marker.slideto": "^0.3.0", "leaflet.markercluster": "^1.5.0", "lint-staged": "^10.2.11", "localforage": "^1.7.3", "md5": "^2.3.0", "mini-css-extract-plugin": "^2.9.2", "mitt": "^3.0.0", "moment": "^2.24.0", "moment-timezone": "^0.5.37", "nprogress": "^0.2.0", "object-hash": "^2.1.1", "packery": "^2.1.2", "playwright": "^1.24.2", "popper.js": "^1.16.0", "portal-vue": "^2.1.7", "prettier": "^2.7.1", "promise-sequential": "^1.1.1", "pug": "^3.0.0", "pug-plain-loader": "^1.1.0", "qs": "^6.9.4", "query-string": "^6.13.0", "ramda": "^0.27.0", "sander": "^0.6.0", "sass": "^1.26.3", "sass-loader": "^8.0.2", "screen-size-detector": "^1.0.4", "serve": "^13.0.2", "socket.io-client": "^4.7.4", "style-loader": "^2.0.0", "terser-webpack-plugin": "^5.3.10", "ts-jest": "^27.1.4", "typescript": "~4.5.5", "unplugin-auto-import": "^0.15.0", "unplugin-vue-components": "^0.25.1", "v-click-outside": "^3.2.0", "vue": "^2.7.13", "vue-class-component": "^7.2.6", "vue-cookie": "^1.1.4", "vue-css-donut-chart": "^1.3.2", "vue-daterangepicker-component": "^1.0.0", "vue-echarts": "^6.2.3", "vue-flatpickr-component": "^8.1.6", "vue-i18n": "^8.26.1", "vue-infinite-loading": "^2.4.5", "vue-json-excel": "^0.2.98", "vue-material-design-icons": "^4.13.0", "vue-meta": "^2.3.2", "vue-multiselect": "^2.1.6", "vue-property-decorator": "^9.1.2", "vue-rangedate-picker": "^1.0.0", "vue-router": "^3.1.5", "vue-scrollbar-directive": "0.0.4", "vue-select": "^3.11.2", "vue-simple-svg": "^2.0.2", "vue-template-compiler": "^2.7.13", "vue-tinybox": "^1.3.0", "vue2-datepicker": "^3.11.1", "vue2-leaflet": "^2.5.2", "vue2-leaflet-markercluster": "^3.1.0", "vue2-teleport": "^1.0.1", "vuex": "^3.1.2", "vuex-router-sync": "^5.0.0", "write-excel-file": "^2.3.2"}, "devDependencies": {"@types/jquery": "^3.5.32", "@types/leaflet.markercluster": "^1.5.5", "@types/md5": "^2.3.5", "@types/object-hash": "^3.0.6", "@types/promise-sequential": "^1.1.2", "@types/ramda": "^0.30.2", "@vitejs/plugin-vue2": "^2.3.3", "@vue/vue2-jest": "^27.0.0", "babel-plugin-transform-import-meta": "^2.3.2", "better-docs": "^2.7.2", "jsdoc": "^3.6.10", "jsdoc-vuejs": "^3.0.9", "minami": "^1.2.3", "rollup-plugin-visualizer": "^5.14.0", "terser": "^5.39.0", "vite": "^6.0.3", "vite-plugin-compression": "^0.5.1"}, "directories": {"doc": "docs", "test": "tests"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "keywords": [], "license": "ISC", "lint-staged": {"*.{js,ts,json}": "npm run lint", "*.{js,ts,css,json,md*}": "npm run format", "*.{js,jsx,vue}": ["jest --testPathPattern=unit"]}, "majestic": {"args": ["--config=./majestic.config.js"]}, "overrides": {"dompurify": "^3.1.3"}, "resolutions": {"dompurify": "^3.1.3"}}