<template lang="pug">
.settings_account
    .section_title {{$t('settings.account_section.current_session.title')}}
    SectionField(:title="$t('settings.account.login_label')" :value="loginName")
    SectionField(:title="$t('settings.account.client_label')" :value="clientName")
    SectionField(:title="$t('i18n.selector_label.title')" style="max-width: 200px;align-items: center;")
      LanguageSelector
    .section_title {{ $t('settings.cache.title') }}
    ButtonWrapper(type="secondary" @click="clearCache"
    font='normal normal normal 14px/21px Open Sans'
    ) {{ $t('settings.cache.clear_cache.btn_text') }}
    
</template>
<script>
import SectionField from '@c/shared/SectionField.vue'
import { mapGetters } from 'vuex'
import LanguageSelector from './LanguageSelector.vue'
import cache from '@/utils/cache'
import ButtonWrapper from '@c/shared/ButtonWrapper.vue'
export default {
  components: {
    <PERSON><PERSON><PERSON>,
    LanguageSelector,
    ButtonWrapper,
  },
  computed: {
    ...mapGetters({
      loginName: 'auth/loginName',
      clientName: 'auth/clientName',
    }),
  },
  methods: {
    async clearCache() {
      if (
        window.confirm(this.$t('settings.cache.clear_cache.confirm_message'))
      ) {
        this.$loader.show()
        await cache.clear()
        await this.$routerPlugin.logoutFromApplication()
        this.$loader.hide()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.section_title {
  font-size: 18px;
  font-weight: bold;
  color: var(--color-tundora);
}
</style>
