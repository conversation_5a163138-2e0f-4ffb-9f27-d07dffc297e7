<template>
  <SearchWrapper
    class="pb-4"
    @search-input="onSearchInput"
    @search-view-change="() => {}"
    @search-clear="onSearchClear"
  >
    <template #search-module-filters>
      <div
        class="container mt-4"
        style="max-height: calc(42vh); overflow-y: auto"
      >
        <div>
          <div class="filter-item">
            <label class="filter-label">
              <span>{{ $t('operationalStatistics.filters.circuits') }}</span>
            </label>
            <BetterSelect
              v-model="selectedCircuits"
              :options="availableCircuits"
              :multiple="true"
              :style-overrides="getBetterSelectStyleOverrides"
            />
          </div>
          <div class="d-flex justify-content-center">
            <ButtonWrapper
              :transparent="true"
              icon="mdi:checkbox-marked"
              :custom-icon-style="{ fontSize: '20px' }"
              @click="selectAllCircuits"
            >
              {{ $t('common.all') }}
              <em
                v-b-tooltip.hover.top
                class="fas fa-info info-tooltip"
                :title="$t('searchModule.select_all')"
              ></em>
            </ButtonWrapper>
            <ButtonWrapper
              :transparent="true"
              icon="mdi:checkbox-blank-outline"
              :custom-icon-style="{ fontSize: '20px' }"
              @click="deselectAllCircuits"
            >
              {{ $t('common.none') }}
              <em
                v-b-tooltip.hover.top
                class="fas fa-info info-tooltip"
                :title="$t('searchModule.deselect_all')"
              ></em>
            </ButtonWrapper>
          </div>
        </div>
        <div>
          <div class="filter-item mt-2">
            <label class="filter-label">
              <span>{{
                $t('operationalStatistics.filters.circuitCategories')
              }}</span>
            </label>
            <BetterSelect
              v-model="selectedCircuitCategories"
              :options="availableCircuitCategories"
              :multiple="true"
              :style-overrides="getBetterSelectStyleOverrides"
            />
          </div>
          <div class="d-flex justify-content-center">
            <ButtonWrapper
              :transparent="true"
              icon="mdi:checkbox-marked"
              :custom-icon-style="{ fontSize: '20px' }"
              @click="selectAllCircuitCategories"
            >
              {{ $t('common.all') }}
              <em
                v-b-tooltip.hover.top
                class="fas fa-info info-tooltip"
                :title="$t('searchModule.select_all')"
              ></em>
            </ButtonWrapper>
            <ButtonWrapper
              :transparent="true"
              icon="mdi:checkbox-blank-outline"
              :custom-icon-style="{ fontSize: '20px' }"
              @click="deselectAllCircuitCategories"
            >
              {{ $t('common.none') }}
              <em
                v-b-tooltip.hover.top
                class="fas fa-info info-tooltip"
                :title="$t('searchModule.deselect_all')"
              ></em>
            </ButtonWrapper>
          </div>
        </div>
      </div>
    </template>
  </SearchWrapper>
</template>

<script setup>
import { computed } from 'vue'
import store from '@/store'
import SearchWrapper from '@/components/shared/SearchModule/SearchWrapper/SearchWrapper.vue'
import ButtonWrapper from '@/components/shared/ButtonWrapper.vue'
import useOperationalStatisticsFilters from '@/composables/operationalStatistics/useOperationalStatisticsFilters'

const emit = defineEmits(['search-input', 'search-clear'])

const { selectedCircuits, selectedCircuitCategories } =
  useOperationalStatisticsFilters()

const availableCircuits = computed(
  () => store.getters['search_module/getCircuits']
)
const availableCircuitCategories = computed(
  () => store.getters['search_module/getCircuitCategories']
)

const getBetterSelectStyleOverrides = computed(() => {
  return store.getters['search_module/getBetterSelectStyleOverrides']
})

const selectAllCircuits = () => {
  const newCircuits = availableCircuits.value.filter((circuit) => {
    return !selectedCircuits.value.includes(circuit)
  })

  selectedCircuits.value = [...selectedCircuits.value, ...newCircuits]
}

const deselectAllCircuits = () => {
  selectedCircuits.value = []
}

const selectAllCircuitCategories = () => {
  const newCategories = availableCircuitCategories.value.filter(
    (category) => !selectedCircuitCategories.value.includes(category)
  )

  selectedCircuitCategories.value = [
    ...selectedCircuitCategories.value,
    ...newCategories,
  ]
}

const deselectAllCircuitCategories = () => {
  selectedCircuitCategories.value = []
}

const onSearchInput = () => {
  emit('search-input')
}

const onSearchClear = () => {
  emit('search-clear')
}
</script>

<style scoped lang="scss">
.container {
  padding: 0 20px 0 23px;
}

.filter-item {
  color: var(--color-dark-blue);
  font-size: 13px;
  white-space: nowrap;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  column-gap: 10px;
}

.filter-item span {
  font: normal normal normal 11px/14px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
  margin: 0px;
  margin-bottom: 10px;
}
</style>
