<template>
  <div class="grid-container">
    <div class="grid">
      <ag-grid-vue
        style="width: 100%; height: 100%"
        class="ag-theme-alpine"
        :column-defs="columnDefs"
        :row-data="rowData"
        :default-col-def="defaultColDef"
        :pagination="true"
        :pagination-page-size="100"
        :locale-text="localeText"
        :status-bar="statusBarConfig"
        :auto-size-strategy="autoSizeStrategy"
        @grid-ready="onGridReady"
      >
      </ag-grid-vue>
      <div
        v-if="gridApi && columnApi && hasResults"
        class="export-buttons-fallback mt-3"
        :class="{ disabled: loading }"
      >
        <div class="d-flex justify-content-end">
          <div class="grid-export-footer">
            <em
              v-b-tooltip.hover.top
              class="fas fa-calculator"
              :title="$t('operationalStatistics.exports.csvTooltip')"
              @click="exportCSV"
            ></em>
            <em
              v-b-tooltip.hover.top
              class="far fa-file-excel"
              :title="$t('operationalStatistics.exports.excelTooltip')"
              @click="exportExcel"
            ></em>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { AgGridVue } from 'ag-grid-vue'
import useOperationalStatisticsExporter from '@/composables/operationalStatistics/useOperationalStatisticsExporter'
import i18n from '@/i18n'
import store from '@/store'
import { AG_GRID_LOCALE_FR } from '@/configs/constants'
import { AG_GRID_LOCALE_EN } from '@ag-grid-community/locale'
import { AG_GRID_LOCALE_ES } from '@ag-grid-community/locale'
import { AG_GRID_LOCALE_NL } from '@ag-grid-community/locale'
import { useAutoSizeAgGridColumns } from '@/composables/useAutoSizeAgGridColumns'

const props = defineProps({
  rowData: {
    type: Array,
    required: true,
  },
  columnDefs: {
    type: Array,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  exportTableConfig: {
    type: Object,
    required: true,
  },
  hasResults: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['grid-ready', 'export'])

const gridApi = ref(null)
const columnApi = ref(null)
const currentDomLayout = ref('normal')

const hasResults = computed(() => {
  return store.getters['operationalStatistics/hasResults']
})

const activeTab = computed(() => {
  return store.state.operationalStatistics.activeTab
})

const { autoSizeAllColumns } = useAutoSizeAgGridColumns(
  gridApi,
  columnApi,
  currentDomLayout
)

const onGridReady = (params) => {
  gridApi.value = params.api
  gridApi.value.refreshCells()
  columnApi.value = params.columnApi
  autoSizeAllColumns() // Auto-size columns based on header content
  emit('grid-ready', params)
}

/**
 * If hasResults, auto-size columns based on cells content
 */
watch(
  () => hasResults.value,
  () => {
    if (hasResults.value) {
      autoSizeAllColumns()
    }
  }
)

/**
 * Resize columns when the active tab changes
 */
watch(
  () => activeTab.value,
  async () => {
    await nextTick()
    autoSizeAllColumns()
  }
)

const autoSizeStrategy = {
  type: 'fitCellContents',
  defaultMaxWidth: 250,
  defaultMinWidth: 250,
}

const defaultColDef = ref({
  resizable: true,
  sortable: true,
  filter: true,
  menuTabs: ['generalMenuTab'], //Hide filters next to column headers
  floatingFilter: true, //Show floating filters below column headers
  flex: 1,
  headerComponent: 'AgGridCustomHeader',
  headerComponentParams: {
    displayName: '',
    hasTooltip: false,
    tooltipText: '',
    enableSorting: false,
  },
  // minWidth: 150,
})

const localeMap = {
  en: AG_GRID_LOCALE_EN,
  fr: AG_GRID_LOCALE_FR,
  es: AG_GRID_LOCALE_ES,
  nl: AG_GRID_LOCALE_NL,
}

const localeText = computed(() => {
  const locale = store.state.settings.applicationLanguage
  return localeMap[locale] || AG_GRID_LOCALE_FR
})

const statusBarConfig = computed(() => ({
  statusPanels: [
    {
      statusPanel: 'agTotalAndFilteredRowCountComponent',
      align: 'left',
    },
    {
      statusPanel: 'GridExportFooter',
      align: 'right',
      statusPanelParams: {
        tableCode: props.exportTableConfig.tableCode,
      },
    },
  ],
}))

const { exportGridData } = useOperationalStatisticsExporter()

const exportCSV = () => {
  if (gridApi.value && columnApi.value) {
    window.vue.$loader.showWhile(() => {
      exportGridData({
        gridApi: gridApi.value,
        columnApi: columnApi.value,
        tableCode: props.exportTableConfig.tableCode,
        format: 'csv',
      })
    })
  }
}

const exportExcel = () => {
  if (gridApi.value && columnApi.value) {
    window.vue.$loader.showWhile(() => {
      exportGridData({
        gridApi: gridApi.value,
        columnApi: columnApi.value,
        tableCode: props.exportTableConfig.tableCode,
        format: 'xlsx',
      })
    })
  }
}

watch(
  () => props.loading,
  (newValue) => {
    if (newValue) {
      gridApi.value?.showLoadingOverlay()
    } else {
      gridApi.value?.hideOverlay()
    }
  }
)
</script>

<style scoped lang="scss">
.grid-container {
  height: 100%;
  .grid {
    height: 100%;
    .export-buttons-fallback {
      .grid-export-footer {
        display: inline-flex;
        box-shadow: 0px 2px 4px rgb(0 0 0 / 8%);
        margin-left: 10px;
        background-color: white;
        border-radius: 4px;

        em {
          padding: 4px 10px;
          cursor: pointer;
          color: var(--color-dark-blue);

          &:hover {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }
      }
    }

    .export-buttons-fallback.disabled {
      pointer-events: none;
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
</style>
