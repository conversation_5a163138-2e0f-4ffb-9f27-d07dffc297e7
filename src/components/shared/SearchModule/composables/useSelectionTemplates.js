import store from '@/store'
import mitt from '@/plugins/mitt.js'
import router from '@/router'
import * as selectionTemplateService from '@/services/selection-template-service'

export function useSelectionTemplates() {
  const getStorageKey = () => {
    const userKey = store.getters['auth/loginNameClientIdEncoded']
    // Remove module-specific key to make templates available across modules
    return `vehicle-selection-templates-${userKey}`
  }

  /**
   * Get all saved selection templates without filtering
   * @returns {Promise<Array>} Array of all saved templates
   */
  const getAllTemplates = async () => {
    try {
      console.debug(9,
        '[useSelectionTemplates] Loading all templates from localStorage...'
      )
      const templates =
        await selectionTemplateService.fetchUserSearchTemplates()

      let parsedTemplates = []

      if (templates) {
        try {
          // Ensure we have an array
          parsedTemplates = Array.isArray(templates) ? templates : [templates]
          console.debug(9,
            '[useSelectionTemplates] Final parsed templates:',
            parsedTemplates
          )
        } catch (parseError) {
          console.error(
            '[useSelectionTemplates] Error parsing templates:',
            parseError
          )
          return []
        }
      }

      return parsedTemplates
    } catch (error) {
      console.error('[useSelectionTemplates] Error loading templates:', error)
      return []
    }
  }

  /**
   * Get templates filtered for the current module
   * @returns {Promise<Array>} Array of filtered templates
   */
  const getTemplates = async () => {
    const parsedTemplates = await getAllTemplates()

    console.debug(9,
      '[useSelectionTemplates] Templates before filtering:',
      JSON.stringify(parsedTemplates, null, 2)
    )

    // Filter templates based on current module's available lists
    const currentModule = router.currentRoute.name
    console.debug(9,'[useSelectionTemplates] Current module:', currentModule)

    if (!currentModule) {
      console.debug(9,
        '[useSelectionTemplates] No current module found, using location-module'
      )
    }

    // Define which lists are available in each module
    const moduleConfig = {
      location_module: { vehicles: true, drivers: true, circuits: true },
      sensors_module: { vehicles: true, drivers: false, circuits: true },
      events_module: { vehicles: true, drivers: false, circuits: false },
      diagnostics_module: { vehicles: true, drivers: false, circuits: false },
    }

    // Get current module's configuration
    const config =
      moduleConfig[currentModule] || moduleConfig['location_module']
    console.debug(9,'[useSelectionTemplates] Module config:', {
      requestedModule: currentModule,
      usingConfig: config,
      availableModules: Object.keys(moduleConfig),
      routeName: router.currentRoute.name,
    })

    const validTemplates = parsedTemplates.filter((template) => {
      if (!template || !template.label || !template.data) {
        console.debug(9,
          '[useSelectionTemplates] Invalid template structure:',
          template
        )
        return false
      }

      console.debug(9,
        '\n[useSelectionTemplates] ---- Checking template ----:',
        template.label
      )

      // Check which selections are present
      const hasVehicles =
        Array.isArray(template.data.selectedVehiclesIds) &&
        template.data.selectedVehiclesIds.length > 0
      const hasDrivers =
        Array.isArray(template.data.selectedDriversIds) &&
        template.data.selectedDriversIds.length > 0
      const hasCircuits =
        Array.isArray(template.data.selectedCircuitsIds) &&
        template.data.selectedCircuitsIds.length > 0

      console.debug(9,'[useSelectionTemplates] Selection state:', {
        name: template.label,
        hasVehicles,
        hasDrivers,
        hasCircuits,
        vehiclesCount: template.data.selectedVehiclesIds?.length || 0,
        driversCount: template.data.selectedDriversIds?.length || 0,
        circuitsCount: template.data.selectedCircuitsIds?.length || 0,
      })

      console.debug(9,'[useSelectionTemplates] Module permissions:', {
        module: currentModule,
        config,
        canHaveVehicles: config.vehicles,
        canHaveDrivers: config.drivers,
        canHaveCircuits: config.circuits,
      })

      // First check: Template must not have selections that aren't allowed
      if (hasDrivers && !config.drivers) {
        console.debug(9,
          '[useSelectionTemplates] Template invalid - has drivers but module does not support drivers'
        )
        return false
      }
      if (hasCircuits && !config.circuits) {
        console.debug(9,
          '[useSelectionTemplates] Template invalid - has circuits but module does not support circuits'
        )
        return false
      }
      if (hasVehicles && !config.vehicles) {
        console.debug(9,
          '[useSelectionTemplates] Template invalid - has vehicles but module does not support vehicles'
        )
        return false
      }

      // Second check: Template must have at least one valid selection for non-location modules
      if (currentModule !== 'location_module') {
        const hasValidSelection =
          (hasVehicles && config.vehicles) ||
          (hasDrivers && config.drivers) ||
          (hasCircuits && config.circuits)
        if (!hasValidSelection) {
          console.debug(9,
            '[useSelectionTemplates] Template invalid - no valid selections for current module'
          )
          return false
        }
      }

      console.debug(9,
        '[useSelectionTemplates] Template is valid for current module'
      )
      return true
    })

    console.debug(9,
      '[useSelectionTemplates] Templates after filtering:',
      JSON.stringify(validTemplates, null, 2)
    )
    return validTemplates
  }

  /**
   * Save a new selection template
   * @param {string} name - Template name
   * @param {Object} selection - Current selection state
   * @param shouldSkipEmit - Allow to avoid emitting event, used when transferring localStorage templates to db
   * @returns {Promise<void>}
   */
  const saveTemplate = async (name, selection, shouldSkipEmit = false) => {
    try {
      console.debug(9,'Saving template with name:', name)
      console.debug(9,'Selection state:', selection)

      if (!selection) {
        throw new Error('No selection state provided')
      }

      // Format selection data to match API expected type
      const data = Object.entries(selection)
        .filter(([, values]) => values.length > 0)
        .map(([key, values]) => `${key}: ${JSON.stringify(values)}`)

      // Get ALL templates, not just filtered ones
      const templates = store.getters['selection_templates/getTemplates']

      // const templates = await getAllTemplates()
      const newTemplate = {
        label: name,
        data: data,
        functionId: router.currentRoute.meta.searchTemplateFunctionId,
      }

      // Find index of existing template with same name
      const existingIndex = templates.findIndex((t) => t.label === name)

      if (existingIndex >= 0) {
        // Replace existing template
        await selectionTemplateService.updateUserSearch(
          templates[existingIndex].id,
          newTemplate
        )
        templates[existingIndex] = newTemplate

        if (!shouldSkipEmit) {
          mitt.emit('selection-templates/updated', newTemplate.label)
        }
      } else {
        await selectionTemplateService.createUserSearch(newTemplate)
        // Add new template
        templates.push(newTemplate)

        if (!shouldSkipEmit) {
          mitt.emit('selection-templates/created', newTemplate.label)
        }
      }

      console.debug(9,'Saving updated templates:', templates)
      console.debug(9,'Template saved successfully')
    } catch (error) {
      console.error('Error saving selection template:', error)
      mitt.emit('selection-templates/save_error', name)
      throw error // Re-throw to handle in component
    }
  }

  /**
   * Delete a selection template
   * @param {string} templateId - Name of the template to delete
   * @returns {Promise<Array>} Updated templates array
   */
  const deleteTemplate = async (templateId) => {
    try {
      console.debug(9,'Deleting template:', templateId)

      // Get current templates
      const currentTemplates = await getAllTemplates()

      // Filter out the template to delete
      const updatedTemplates = currentTemplates.filter(
        (t) => t.id !== templateId
      )

      await selectionTemplateService.deleteUserSearch(templateId)

      console.debug(9,'Template deleted successfully')
      return updatedTemplates
    } catch (error) {
      console.error('Error deleting selection template:', error)
      throw error
    }
  }

  /**
   * Apply a saved template to the current selection
   * @param {Object} template - Template to apply
   * @returns {Promise<void>}
   */
  const applyTemplate = async (template) => {
    try {
      console.debug(9,'[useSelectionTemplates] Applying template:', template)

      if (!template || !template.data) {
        throw new Error('Invalid template or missing selection')
      }

      // Clear existing selection first
      console.debug(9,'[useSelectionTemplates] Clearing existing selection')
      store.commit('search_module/clearSelection')

      // Apply vehicles
      console.debug(9,
        '[useSelectionTemplates] Applying vehicles:',
        template.data?.selectedVehiclesIds
      )
      template.data?.selectedVehiclesIds?.forEach((id) => {
        store.commit('search_module/selectVehicle', id)
      })

      // Apply drivers
      console.debug(9,
        '[useSelectionTemplates] Applying drivers:',
        template.data?.selectedDriversIds
      )
      template.data?.selectedDriversIds?.forEach((id) => {
        store.commit('search_module/selectDriver', id)
      })

      // Apply circuits
      console.debug(9,
        '[useSelectionTemplates] Applying circuits:',
        template.data?.selectedCircuitsIds
      )
      template.data?.selectedCircuitsIds?.forEach((id) => {
        store.commit('search_module/selectCircuit', id)
      })

      // Apply date ranges
      console.debug(9,
        '[useSelectionTemplates] Applying date ranges:',
        template.data?.selectedDateRanges
      )
      template.data?.selectedDateRanges?.forEach((range) => {
        store.commit('search_module/selectDateRange', range)
      })

      console.debug(9,'[useSelectionTemplates] Template applied successfully')

      // Notify components to update
      mitt.emit('search_module/refresh')
      mitt.emit('selection-templates/applied', template)
    } catch (error) {
      console.error('Error applying selection template:', error)
      throw error
    }
  }

  const transferTemplatesFromLocalStorageToDb = async () => {
    try {
      //Get templates from local storage
      const localStorageTemplates = await getTemplatesFromLocalStorage()
      //Return if no templates found
      if (!localStorageTemplates || localStorageTemplates.length === 0) {
        console.debug(9,'No templates found in local storage')
        return
      }
      //Filter out templates that have already been migrated
      const filteredTemplates = localStorageTemplates.filter(
        (template) => template?.searchSelectionTemplatesMigrated !== 1
      )

      //Save each template to the database and update local storage
      for (const template of filteredTemplates) {
        try {
          await saveTemplate(template.name, template.selection, true)

          const existingIndex = localStorageTemplates.findIndex(
            (t) => t.name === template.name
          )

          const newTemplate = {
            name: template.name,
            selection: template.selection,
            createdAt: template.createdAt,
            searchSelectionTemplatesMigrated: 1,
          }

          if (existingIndex >= 0) {
            localStorageTemplates[existingIndex] = newTemplate
          }

          console.debug(9,'Saving updated templates:', localStorageTemplates)

          const STORAGE_KEY = getStorageKey()
          localStorage.setItem(
            STORAGE_KEY,
            JSON.stringify(localStorageTemplates)
          )
        } catch (saveError) {
          console.error('Error transferring templates:', saveError)
        }
      }
    } catch (error) {
      console.error('Error transferring templates:', error)
      throw new Error('Error transferring templates:', error)
    }
  }

  const getTemplatesFromLocalStorage = async () => {
    const STORAGE_KEY = getStorageKey()

    const localStorageTemplates = localStorage.getItem(STORAGE_KEY)

    console.debug(
      9,
      '[useSelectionTemplates] Raw templates from storage:',
      localStorageTemplates
    )

    let parsedTemplates = []

    if (localStorageTemplates) {
      try {
        let parsed = JSON.parse(localStorageTemplates)

        // Check if result is a string (double-stringified)
        if (typeof parsed === 'string') {
          parsed = JSON.parse(parsed)
          console.debug(9,'[useSelectionTemplates] Second parse result:', parsed)
        }

        // Ensure we have an array
        parsedTemplates = Array.isArray(parsed) ? parsed : [parsed]
        console.debug(9,
          '[useSelectionTemplates] Final parsed templates:',
          parsedTemplates
        )
      } catch (parseError) {
        console.error(
          '[useSelectionTemplates] Error parsing templates:',
          parseError
        )
        return []
      }
    }

    return parsedTemplates
  }

  return {
    getStorageKey,
    getTemplates,
    saveTemplate,
    applyTemplate,
    deleteTemplate,
    transferTemplatesFromLocalStorageToDb,
    getTemplatesFromLocalStorage,
  }
}
