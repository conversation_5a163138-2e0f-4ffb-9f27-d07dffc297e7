<template>
  <div v-if="shouldRender" class="search_module" :class="mode">
    <!-- Button shown when the menu is collapsed -->
    <em
      v-show="isAdvancedSearchWithLayoutCollapsed"
      class="fas fa-search collapsed_search_button"
      @click="switchToSearch"
    />
    <div
      v-show="!isAdvancedSearchWithLayoutCollapsed"
      class="search_module_inner"
    >
      <!-- When results are displayed, user can come back to search selection -->
      <BackTitleButton
        v-show="showBackToSearch"
        :title="
          !backText
            ? $t('search_module.results_view.switch_back_to_search')
            : backText
        "
        style="margin-left: 10px"
        @click="() => (!backText ? inputClick() : backButtonPress())"
      />

      <SMSelection
        v-show="!showBackToSearch"
        ref="selectionRef"
        :show-selected="willShowSelectedItems"
        :results="isViewResults"
        :visible="isSelectionView || isFormMode"
        :suggestions="showSuggestions"
        :fixed-input="isFormMode"
        :back-arrow="showBackToResultsArrow"
        @onQuery="onQuery"
      >
        <template #input-left>
          <!-- User can switch back to existing results -->
          <em
            v-show="showBackToResultsArrow"
            v-b-tooltip.hover.bottom
            class="fas fa-arrow-left self-center back-arrow"
            :title="$t('search_module.search_view.last_results_button_tooltip')"
            @click="switchToResults"
          />
        </template>
      </SMSelection>

      <div class="actions">
        <!-- CLOSE BUTTON (X) -->
        <em
          v-if="!isFormMode"
          v-show="hasSelection"
          class="fas fa-times p-1"
          aria-hidden="true"
          @click="resetSearch"
        />

        <!-- Preselection/History menu button -->
        <em v-if="false" v-show="isFormMode" class="fas fa-history p-1" />
        <em
          v-if="false"
          v-show="!isFormMode"
          class="fas fa-history p-1"
          @click="setView('history_menu')"
        />

        <div v-show="!isViewResults">
          <slot name="search-input-right" :vm="this"> </slot>
        </div>
      </div>
      <SMHistoryMenu v-show="showHistoryMenu" />
    </div>

    <!-- FREE MODE -->
    <!-- Advanced search button -->
    <div
      v-if="!isFormMode && !isViewResults"
      class="search_bar__switch_mode_button_wrapper"
    >
      <b-button
        squared
        block
        class="search_bar__switch_mode_button"
        @click="() => $emit('clickFormModeButton')"
      >
        {{ $t('searchModule.buttons.advanced_search') }}
      </b-button>

      <slot name="search-validate-button-free" :vm="this">
        <button
          v-if="!isViewResults"
          class="btn btn-block btn-primary mt-2 float-right"
          @click="clickSearch"
        >
          <span v-show="!$store.state.search_module.isSearchInProgress">
            {{ $t('common.Valider') }}
          </span>
          <span v-show="$store.state.search_module.isSearchInProgress">
            <b-spinner
              class="loader"
              variant="info"
              style="width: 1rem; height: 1rem; margin: 0 auto; display: block"
            />
          </span>
        </button>
      </slot>
    </div>

    <!-- SelectionTemplatesSection goes here -->
    <SelectionTemplatesSection
      v-if="isFormMode && !isViewResults && shouldShowSelectionTemplatesSection"
      class="mt-2"
    />

    <!-- FORM MODE -->
    <!-- Search form (Sidebar menu) -->
    <SMForm
      v-if="isFormMode"
      v-show="shouldShowSearchForm"
      ref="form"
      :results="isViewResults"
      :highlight-vehicles-tab="highlightVehiclesTab"
      :highlight-drivers-tab="highlightDriversTab"
      :highlight-circuits-tab="highlightCircuitsTab"
      :tabs="computedFormTabs"
      :reset-button="hasSelection"
      @validate="clickSearch"
      @reset="resetSearch"
    >
      <template #toolbar-right>
        <SaveSelectionButton v-if="isFormMode && !isViewResults" />
      </template>

      <template #search-filters>
        <slot name="search-filters" />
      </template>

      <template #date-picker="slotProps">
        <slot name="date-picker" :form-actions="slotProps.formActions" />
      </template>

      <template #search-validate-button>
        <slot
          name="search-validate-button-form"
          :click-search="() => clickSearch()"
          :is-view-results="isViewResults"
        />
      </template>

      <div v-show="!isViewResults">
        <slot name="form-bottom"> </slot>
      </div>
    </SMForm>

    <!-- Free search button -->
    <div
      v-if="computedToggleFreesearch && isFormMode && !isViewResults"
      v-show="!isAdvancedSearchWithLayoutCollapsed"
      class="search_bar__switch_mode_button_wrapper"
    >
      <b-button
        variant="outline-primary"
        squared
        block
        class="search_bar__switch_mode_button"
        @click="() => $emit('clickFreeModeButton')"
      >
        {{ $t('searchModule.buttons.free_search') }}
      </b-button>
    </div>

    <!-- FREE/FORM MODE -->
    <!-- Results slot -->
    <div v-show="isViewResults" class="results">
      <slot name="results" />
    </div>
  </div>

  
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, inject, provide } from 'vue'
import SMSelection from './SMSelection.vue'
import SMSuggestion from './SMSuggestion.vue'
import SMForm from './SMForm.vue'
import SMHistoryMenu from './SMHistoryMenu.vue'
import BackTitleButton from '@/components/shared/BackTitleButton.vue'
import store from '@/store'
import mitt from '@/plugins/mitt.js'
import useCurrentRoute from '@/composables/useCurrentRoute'
import SaveSelectionButton from '@/components/shared/SearchModule/components/SaveSelectionButton.vue'
import SelectionTemplatesSection from '@/components/shared/SearchModule/components/SelectionTemplatesSection.vue'
const currentRoute = ref(null)

// Get current route
;(async () => {
  currentRoute.value = await useCurrentRoute()
})()

const selectionRef = ref(null)

//Deprecated
const searchModuleCanToggleFreesearch = ref(false)

const props = defineProps({
  value: {
    type: Object,
    default: () => null,
  },
  mode: {
    type: String,
    validator: (value) =>
      ['', 'advanced', 'form', 'free', 'map'].includes(value),
    default: '',
  },
  highlightVehiclesTab: {
    type: Boolean,
    default: false,
  },
  highlightDriversTab: {
    type: Boolean,
    default: false,
  },
  highlightCircuitsTab: {
    type: Boolean,
    default: false,
  },
  formTabs: {
    type: Array,
    default: () => [],
  },
  canToggleFreesearch: {
    type: Boolean,
    default: true,
  },
  backText: {
    type: String,
    default: null,
  },
})

const emit = defineEmits([
  'clickFormModeButton',
  'clear',
  'inputClick',
  'backButtonPress',
  'viewChange',
  'input',
])

const searchFormTabs = inject('searchFormTabs', [])

const SMFormTreeShowLoader = ref(false)

provide('SMFormTreeShowLoader', {
  get value() {
    return SMFormTreeShowLoader.value
  },
})

const actionsSelectorCSSRightRule = computed(() =>
  !isViewResults.value ? 'calc(20px + 1.5rem)' : 'calc(20px + 0.5rem)'
)

const view = computed({
  get() {
    return store.state.search_module.view
  },
  set(v) {
    store.state.search_module.view = v
  },
})

const computedToggleFreesearch = computed(() =>
  searchModuleCanToggleFreesearch.value !== null
    ? searchModuleCanToggleFreesearch.value
    : props.canToggleFreesearch
)

const computedFormTabs = computed(() =>
  props.formTabs.length !== 0 ? props.formTabs : searchFormTabs
)

const hasResults = computed(() => store.getters['search_module/hasResults'])

const showBackToSearch = computed(() => isFormMode.value && isViewResults.value)

const showBackToResultsArrow = computed(
  () =>
    isFormMode.value &&
    !isViewResults.value &&
    store.getters['search_module/hasResults']
)

const isAdvancedSearchWithLayoutCollapsed = computed(() => {
  if (store.state.app.layoutType === 'tlayout') {
    return isFormMode.value && store.getters['app/layout'].isMenuCollapsed
  } else {
    return false
  }
})

const shouldShowSearchForm = computed(
  () => !isAdvancedSearchWithLayoutCollapsed.value
)

const shouldRender = computed(() => {
  if (props.mode === 'free') {
    return true
  }

  if (isFormMode.value) {
    return true
  } else {
    return (
      store.getters['app/layout'].isMenuCollapsed &&
      store.getters['app/layout'].willMenuCollapseCompletely
    )
  }
})

const isViewResults = computed(
  () => isFormMode.value && view.value == 'results'
)

const isFormMode = computed(() => ['form', 'advanced'].includes(props.mode))

const willShowSelectedItems = computed(() => !isFormMode.value)

const showSuggestions = computed(() => !isFormMode.value)

const isSelectionView = computed(() => view.value === 'selection')

const showHistoryMenu = computed(() => view.value === 'history_menu')

const hasSelection = computed(() => store.getters['search_module/hasSelection'])

// Define modules in which selection templates section should be shown
const shouldShowSelectionTemplatesSection = computed(() => {
  if (!currentRoute.value || !currentRoute.value.name) return false

  const modulesWithSelectionTemplatesSection = [
    'location',
    'events',
    'sensors',
    'alerts',
  ]

  const moduleName = currentRoute.value.name.replace('_module', '')

  return modulesWithSelectionTemplatesSection.includes(moduleName)
})

watch(hasResults, () => {
  if (hasResults.value) {
    switchToResults()
  }
})

onMounted(async () => {
  await store.dispatch('search_module/initialize')
})

const onQuery = (query) => {
  SMFormTreeShowLoader.value = true
  store.state.search_module.searchString = query
  nextTick(() => {
    setTimeout(() => {
      SMFormTreeShowLoader.value = false
    }, 200)
  })
}

const switchToResults = (e) => {
  setView('results')
  store.dispatch('app/changeLayout', {
    origin: 'SearchModule.vue::switchToResults',
    menu_full_collapse: false,
  })
  emit('clickFormModeButton')
  e && e.stopPropagation()
}

const switchToSearch = (e) => {
  console.debugVerbose(8, 'switchToSearch')
  store.dispatch('app/changeLayout', {
    origin: 'SearchModule.vue::switchToSearch',
    menu: true,
    menu_collapsed: false,
    menu_full_collapse: true,
    sub_menu: false,
  })
  setViewSelection()
  e && e.stopPropagation()
}

const resetSearch = () => {
  clearSelection()
  emit('clear')
  switchToSearch()
  mitt.emit('search_module/refresh')
  store.dispatch('selection_templates/setSelectedTemplate', null)
}

const inputClick = () => {
  console.log('Input clicked!')
  setViewSelection()
  emit('inputClick')
}

const backButtonPress = () => {
  console.log('backButtonPress called')
  emit('backButtonPress')
}

const clearSelection = () => {
  store.dispatch('search_module/clearSelection')
  store.dispatch('search_module/clearDateSelection')
  selectionRef.value?.hideSuggestions()
}

const setViewSelection = () => {
  setView('selection')
  selectionRef.value?.showSuggestions()
}

const setView = (viewString) => {
  view.value = viewString
  emit('viewChange', viewString)
}

const clickSearch = () => {
  if (!store.getters['search_module/isValidSelection']) {
    return
  }

  emit('input', store.getters['search_module/getSelection'])
  store.dispatch('search_module/validateSearch')
}
</script>

<style lang="scss" scoped>
.search_module {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.search_module.advanced {
  position: initial;
}

.search_module_inner {
  display: grid;
  grid-template-columns: 1fr;
  position: relative;
  background-color: white;
  min-height: 44px;
  width: 100%;

  :deep {
    .search_module_suggestions_search.with_back_arrow {
      position: relative;

      input {
        width: 100%;
        padding-left: 35px;
      }

      em.back-arrow {
        position: absolute;
        top: 0;
        left: 12px;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: inherit;
      }
    }
  }
}

.actions {
  position: absolute;
  right: v-bind(actionsSelectorCSSRightRule);
  top: 0px;
  font-size: 25px;
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 0.1rem;
}

em {
  color: var(--color-denim);
  cursor: pointer;
  font-size: 16px;
}

em:hover {
  opacity: 0.9;
}

em.fa-times {
  font-size: 24px;
  position: relative;
  top: 2px;
}

.btn {
  background-color: var(--color-search-module-button);
  color: #484848;
}

.btn.btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.search_bar__switch_mode_button {
  max-width: 570px;
  border: 0px;
  outline: 0px;
  box-shadow: none;
  font: normal normal normal 12px/14px Open Sans;
  letter-spacing: 0px;
}

.search_bar__switch_mode_button_wrapper {
  max-width: 570px;
  width: inherit;
  position: relative;
  z-index: 1000;
}

.collapsed_search_button {
  color: var(--color-dark-blue);
  font-size: 25px;
  margin-top: 20px;
  margin-bottom: 20px;
}

.results {
  width: inherit;
}
</style>
