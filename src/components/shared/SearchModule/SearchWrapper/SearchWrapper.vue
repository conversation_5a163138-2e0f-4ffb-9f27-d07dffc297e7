<template lang="pug">
.search-wrapper
  
  
  //.search-wrapper-header(v-show="showHeader")
    SearchTypeSelector(v-model="searchType")
    .search-type-title {{ title }}
    slot(name="header-right", v-bind:type="searchType")
      GeocodingMapToolbar(
        v-show="searchType==='map'",
        v-model="geocodingSelectedItem"
      )
  

  .search-wrapper-inner.mt-2
    slot(name="inner", v-bind:type="searchType")
      SearchModule(
        v-show="searchType==='free'||searchType==='form'",
        :back-text="backText"
        :mode="searchType",
        :highlight-vehicles-tab="$store.getters['location_module/getResultsFromType']('vehicle').length > 0",
        :highlight-drivers-tab="$store.getters['location_module/getResultsFromType']('driver').length > 0",
        :highlight-circuits-tab="$store.getters['location_module/getResultsFromType']('circuit').length > 0",
        @backButtonPress="onBackButtonPressed"
        @inputClick="onSearchModuleFocus",
        @clickFormModeButton="() => (searchType = 'form')",
        @clickFreeModeButton="() => (searchType = 'free')",
        @input="onSearchModuleInput",
        @viewChange="(view) => $emit('search-view-change', view)",
        @clear="() => $emit('search-clear')"
      )
        template(v-slot:search-input-right)
          SearchTypeSelector(v-model="searchType")
        
        template(v-slot:form-bottom)
          slot(name="search-module-form-bottom")

        template(v-slot:search-filters)
          slot(name="search-module-filters")
        
        template(v-slot:results)
          slot(name="search-module-results")
        
        template(v-slot:search-validate-button-form="slotProps")
          slot(
            name="search-module-validate-button-form",
            v-bind:clickSearch="slotProps.clickSearch",
            v-bind:isViewResults="slotProps.isViewResults"
          )
        template(v-slot:date-picker="slotProps")
          slot(name="search-module-date-picker" :form-actions="slotProps.formActions")
      
      LocateAddressFormFeature(
        v-if="searchType === 'map'",
        v-show="geocodingSelectedItem==='LocateAddressFormFeature'"
      )
      LocateAddressNatural(
        v-if="searchType === 'map'",
        v-show="geocodingSelectedItem==='LocateAddressNatural'"
      )
      LocateAddressLatLng(
        v-if="searchType === 'map'",
        v-show="geocodingSelectedItem==='LocateAddressLatLng'"
      )
      RoutingFeature(
        v-if="searchType === 'map' && geocodingSelectedItem === 'Routing'"
      )
</template>
<script>
import SearchTypeSelector from '@c/shared/SearchModule/SearchWrapper/SearchTypeSelector.vue'
import SearchModule from '@c/shared/SearchModule/SearchModule.vue'
import GeocodingMapToolbar from '@c/shared/geocoding/GeocodingMapToolbar.vue'
import LocateAddressFormFeature from '@c/shared/geocoding/LocateAddressFormFeature/LocateAddressFeature.vue'
import LocateAddressNatural from '@c/shared/geocoding/LocateAddressNatural.vue'
import LocateAddressLatLng from '@c/shared/geocoding/LocateAddressLatLng.vue'
import RoutingFeature from '@c/shared/geocoding/RoutingFeature/RoutingFeature.vue'
import Vue from 'vue'

/**
 * Wrapper on top of SearchModule, which includes geocoding tools (locate address form/natural/gps and routing)
 * @todo Fix selection binding (v-model doesn't work)
 */
export default {
  components: {
    SearchTypeSelector,
    SearchModule,
    GeocodingMapToolbar,
    LocateAddressFormFeature,
    LocateAddressNatural,
    LocateAddressLatLng,
    RoutingFeature,
  },
  mixins: [Vue.$mixins.userRightsMixin],
  provide() {
    //Map tools access check
    let whitelistSearchTypes = [
      'free',
      'form',
      this.hasFeatureRight('geocoding_access') ? 'map' : '',
    ].filter((code) => !!code)

    //Map tools check
    let whitelistGeocodingToolbarItems = [
      this.hasFeatureRight('geocoding_address')
        ? 'LocateAddressFormFeature'
        : '',
      this.hasFeatureRight('geocoding_address_natural')
        ? 'LocateAddressNatural'
        : '',
      this.hasFeatureRight('geocoding_address_reverse')
        ? 'LocateAddressLatLng'
        : '',
      this.hasFeatureRight('geocoding_routing') ? 'Routing' : '',
    ].filter((code) => !!code)

    return {
      whitelistGeocodingToolbarItems,
      whitelistSearchTypes,
    }
  },
  props: {
    defaultSearchType: {
      type: String,
      default: 'form',
      enum: ['free', 'form', 'map'],
    },
    backText: {
      type: String,
      default: null,
    },
    buttonPressedEventKey: {
      type: String,
      default: 'default',
    },
  },
  data() {
    return {
      searchType: this.defaultSearchType,
      geocodingSelectedItem: 'LocateAddressFormFeature',
    }
  },
  computed: {
    title() {
      return this.$t(`search_module.search_type_title.${this.searchType}`)
    },
    showHeader() {
      if (!this.$store.state.app.layout.willMenuCollapseCompletely) {
        return false
      }
      return false
    },
  },
  watch: {
    searchType() {
      //
      //Only for location module. Refactor?
      //Ensure that while inside map section (geocoding tools), menu (if dynamic layout) collapses completely

      if (this.searchType === 'map' || this.searchType === 'free') {
        this.$store.dispatch('app/changeLayout', {
          origin: 'SearchModule.vue::searchType(watch)',
          willMenuCollapseCompletely: true,
        })
      }
      if (this.searchType === 'form') {
        this.$store.dispatch('app/changeLayout', {
          origin: 'SearchModule.vue::searchType(watch)',
          willMenuCollapseCompletely: false, //Results in location module use a reduced menu mode
        })
      }
    },
  },
  methods: {
    onSearchModuleInput(value) {
      this.$emit('search-input', value)
      this.$emit('input', value)
    },
    /**
     * @todo: Refactor/Remove: Unselecting an item and closing the details windows only has sense in the location module context. Move away?
     */
    onSearchModuleFocus() {
      this.$store.dispatch('location_module/selectItem', {})
      this.$store.dispatch('app/changeLayout', {
        origin: 'SearchModule.vue::onSearchModuleFocus',
        sub_menu: false,
        menu_full_collapse: true,
      })
    },
    onBackButtonPressed() {
      if (this.backText !== null && this.buttonPressedEventKey !== '') {
        this.$root.$emit(
          `search-wrapper:${this.buttonPressedEventKey}:back-button-pressed`
        )
      }
    },
  },
}
</script>
<style lang="scss">
.search-wrapper-header {
  display: grid;
  grid-template-columns: 50px 1fr 20px;
  margin: 5px 20px;
}
.search-type-title {
  font: normal normal normal 14px/29px Open Sans;
  letter-spacing: 0px;
  color: var(--color-metal-rock);
}
</style>
