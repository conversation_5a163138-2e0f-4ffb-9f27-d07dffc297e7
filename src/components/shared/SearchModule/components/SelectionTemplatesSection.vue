<template>
  <div class="selection-templates-section">
    <div class="custom-select-container">
      <button
        ref="toggleButton"
        class="btn w-100 d-flex justify-content-between align-items-center toggle-button search-module-input"
        @click="isOpen = !isOpen"
      >
        <span>{{
          selectedTemplate
            ? selectedTemplate.label
            : $t('searchModule.templates.no_template_selected')
        }}</span>
        <i
          class="fas icon"
          :class="isOpen ? 'fa-caret-up' : 'fa-caret-down'"
        ></i>
      </button>

      <div v-if="isOpen" class="custom-select-menu templates-dropdown">
        <b-list-group>
          <b-list-group-item
            button
            class="template-item"
            @click="onTemplateSelect(null)"
          >
            {{ $t('searchModule.templates.no_selection') }}
          </b-list-group-item>

          <b-list-group-item
            v-for="template in templates"
            :key="template.label"
            button
            class="d-flex justify-content-between align-items-center template-item"
            @click="onTemplateSelect(template)"
          >
            <span class="template-name">{{ template.label }}</span>
            <b-button
              v-b-tooltip.hover
              :title="$t('search_module.templates.delete_tooltip')"
              variant="link"
              class="p-0 ml-2 delete-button"
              @click.stop="showDeletionConfirmationPopup(template)"
            >
              <i class="fas fa-trash-alt text-danger"></i>
            </b-button>
            <b-modal
              v-model="showDeletePopup"
              :title="$t('searchModule.modal.delete_template_title')"
              class="high-z-modal"
              :cancel-title="$t('buttons.cancel_alternative')"
              :ok-title="$t('buttons.valid')"
              size="sm"
              @ok="onDeleteTemplate"
              @cancel="closeDeletionConfirmationPopup"
            >
              <p>
                {{
                  $t('searchModule.modal.delete_template_description', {
                    name: selectedTemplate?.label,
                  })
                }}
              </p>
            </b-modal>
          </b-list-group-item>
        </b-list-group>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useSelectionTemplates } from '@/components/shared/SearchModule/composables/useSelectionTemplates'
import useModal from '@/composables/modal'
import useToastComposable from '@/composables/toast'
import store from '@/store'
import router from '@/router'
import i18n from '@/i18n'
import mitt from '@/plugins/mitt.js'

const isOpen = ref(false)
const toggleButton = ref(null)
const showDeletePopup = ref(false)

// Get templates from store
const templates = computed(
  () => store.getters['selection_templates/getTemplates']
)
const selectedTemplate = computed(
  () => store.getters['selection_templates/getSelectedTemplate']
)

const {
  getTemplates,
  applyTemplate,
  getStorageKey,
  deleteTemplate,
  transferTemplatesFromLocalStorageToDb,
} = useSelectionTemplates()
const { showModalConfirm } = useModal()
const { showToast } = useToastComposable({ store, i18n })

// Function to sync templates from db and save necessary ones from local storage to db
const syncTemplates = async () => {
  try {
    console.debug(9,'[SelectionTemplatesSection] Starting template sync...')
    console.debug(9,
      '[SelectionTemplatesSection] Current route:',
      router.currentRoute.name
    )
    console.debug(9,'[SelectionTemplatesSection] Storage key:', getStorageKey())

    console.debug(9,
      '[SelectionTemplatesSection] Loading templates from storage...'
    )
    const storedTemplates = await getTemplates()
    console.debug(9,
      '[SelectionTemplatesSection] Raw templates loaded:',
      storedTemplates
    )

    // Update templates in store
    console.debug(9,'[SelectionTemplatesSection] Updating store with templates')
    store.dispatch('selection_templates/loadTemplates', storedTemplates)

    // If we have a selected template that no longer exists, clear it
    if (
      selectedTemplate.value &&
      !storedTemplates.find((t) => t.name === selectedTemplate.value.name)
    ) {
      console.debug(9,
        '[SelectionTemplatesSection] Selected template no longer exists, clearing selection'
      )
      store.dispatch('selection_templates/setSelectedTemplate', null)
    }

    console.debug(9,'[SelectionTemplatesSection] Template sync completed')
    return storedTemplates
  } catch (error) {
    console.error('[SelectionTemplatesSection] Error syncing templates:', error)
    showToast({
      type: 'error',
      title: i18n.t('search_module.templates.load_error_title'),
      text: i18n.t('search_module.templates.load_error_text'),
      timeout: 5000,
    })
    return []
  }
}

// Expose for testing
window.sss = window.sss || {}
window.sss.load = syncTemplates

// Load templates on component creation and set up sync
onMounted(async () => {
  console.debug(9,'[SelectionTemplatesSection] Component mounted')
  document.addEventListener('click', handleClickOutside)

  //Initial transfer of templates from local storage to db
  await transferTemplatesFromLocalStorageToDb()

  // Initial template load
  await syncTemplates()

  // Set up template update listeners
  mitt.on('selection-templates/updated', async () => {
    console.debug(9,'[SelectionTemplatesSection] Received template update event')
    await syncTemplates()
  })

  mitt.on('selection-templates/applied', (template) => {
    console.debug(9,
      '[SelectionTemplatesSection] Template applied, updating selection:',
      template
    )
    store.dispatch('selection_templates/setSelectedTemplate', template)
  })

  //Listen event for created template
  mitt.on('selection-templates/created', async (label) => {
    showToast({
      type: 'success',
      title: i18n.t('search_module.templates.create_success_title'),
      text: i18n.t('search_module.templates.create_success_text', {
        name: label,
      }),
      timeout: 3000,
    })
  })

  //Listen event for updated template
  mitt.on('selection-templates/updated', async (label) => {
    showToast({
      type: 'success',
      title: i18n.t('search_module.templates.update_success_title'),
      text: i18n.t('search_module.templates.update_success_text', {
        name: label,
      }),
      timeout: 3000,
    })
  })

  //Liste event for template save error
  mitt.on('selection-templates/save-error', async (label) => {
    showToast({
      type: 'error',
      title: i18n.t('search_module.templates.save_error_title'),
      text: i18n.t('search_module.templates.save_error_text', {
        name: label,
      }),
      timeout: 3000,
    })
  })
})

// Clean up event listeners
onUnmounted(() => {
  console.debug(9,'[SelectionTemplatesSection] Component unmounting')
  document.removeEventListener('click', handleClickOutside)
  mitt.off('selection-templates/updated')
  mitt.off('selection-templates/applied')
})

// Watch for route changes to reload templates
watch(
  () => router.currentRoute.name,
  async () => {
    console.debug(9,
      '[SelectionTemplatesSection] Route changed, reloading templates'
    )
    await syncTemplates()
  }
)

const handleClickOutside = (event) => {
  if (toggleButton.value && !toggleButton.value.contains(event.target)) {
    isOpen.value = false
  }
}

const onTemplateSelect = async (template) => {
  try {
    console.debug(9,'[SelectionTemplatesSection] Selecting template:', template)
    isOpen.value = false

    if (!template) {
      // Clear selection
      store.dispatch('selection_templates/setSelectedTemplate', null)
      store.dispatch('search_module/resetStore')
      return
    }

    // Apply template selection
    await applyTemplate(template)
  } catch (error) {
    console.error(
      '[SelectionTemplatesSection] Error selecting template:',
      error
    )
    showToast({
      type: 'error',
      title: i18n.t('search_module.templates.apply_error_title'),
      text: i18n.t('search_module.templates.apply_error_text'),
      timeout: 5000,
    })
  }
}

const onDeleteTemplate = async () => {
  try {
    console.debug(9,
      '[SelectionTemplatesSection] Attempting to delete template:',
      selectedTemplate?.value
    )

    //Keep track of template label for toast message
    const templateLabel = selectedTemplate?.value?.label

    console.debug(9,'[SelectionTemplatesSection] Deleting template')
    const updatedTemplates = await deleteTemplate(selectedTemplate?.value?.id)

    console.debug(9,'[SelectionTemplatesSection] Updating UI with new templates')
    store.dispatch('selection_templates/loadTemplates', updatedTemplates)

    //Close confirmation popup
    closeDeletionConfirmationPopup()

    showToast({
      type: 'success',
      title: i18n.t('search_module.templates.delete_success_title'),
      text: i18n.t('search_module.templates.delete_success_text', {
        name: templateLabel,
      }),
      timeout: 3000,
    })

    await store.dispatch('selection_templates/setSelectedTemplate', null)
    await store.dispatch('search_module/resetStore')
  } catch (error) {
    console.error('[SelectionTemplatesSection] Error deleting template:', error)
    //Close confirmation popup
    closeDeletionConfirmationPopup()

    showToast({
      type: 'error',
      title: i18n.t('search_module.templates.delete_error_title'),
      text: i18n.t('search_module.templates.delete_error_text'),
      timeout: 5000,
    })
  }
}

const closeDeletionConfirmationPopup = () => {
  console.debug(9,'[SaveSelectionButton] Closing delete popup')
  showDeletePopup.value = false
  store.dispatch('selection_templates/setSelectedTemplate', null)
}

const showDeletionConfirmationPopup = (template) => {
  store.dispatch('selection_templates/setSelectedTemplate', template)
  showDeletePopup.value = true
}
</script>

<style lang="scss" scoped>
.selection-templates-section {
  padding: 0 20px 10px;
  width: 100%; // Add full width

  .custom-select-container {
    position: relative;
    width: 100%;
    margin: 0 auto; // Center the container

    .toggle-button {
      width: 100%;
      text-align: left;
      position: relative;
      padding: 0 13px;
      height: 40px;
      color: var(--color-slate-gray);

      span {
        font-size: 14px;
      }

      .icon {
        position: absolute;
        right: 1.5rem;
        top: 50%;
        transform: translateY(-50%);
        font-weight: 900;
        color: var(--color-dark-blue);
        font-size: 25px;
      }
    }

    .templates-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      z-index: 1000;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-top: 5px;
      max-height: 300px;
      overflow-y: auto;
      width: 100%; // Ensure dropdown matches button width

      .template-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 15px;
        width: 100%; // Full width items

        &:hover {
          background-color: var(--color-main);
          color: #ffffff;
        }

        .template-name {
          flex-grow: 1;
          margin-right: 10px;
        }

        .delete-button {
          visibility: hidden;
        }

        &:hover .delete-button {
          visibility: visible;
        }
      }
    }
  }
}

// Import search selection modal styles
@import '@/styles/search-selection-modal';

@include modal-styles;
</style>
