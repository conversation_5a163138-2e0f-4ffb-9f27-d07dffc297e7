<template>
  <div v-if="showSaveSelectionButton !== false" class="save-selection-button">
    <div @click="saveCurrentSelection">
      <VueIcon
        icon="mdi:content-save-outline"
        color="var(--color-main)"
        :style="{ fontSize: `24px`, cursor: 'pointer' }"
      />
    </div>

    <!-- Save Template Dialog -->
    <b-modal
      v-model="showSaveDialog"
      :title="$t('searchModule.modal.save_template_title')"
      class="high-z-modal"
      :cancel-title="$t('buttons.cancel_alternative')"
      :ok-title="$t('buttons.valid')"
      size="sm"
      @ok="validateTemplateName"
      @cancel="closeDialog"
    >
      <p>
        {{ $t('searchModule.modal.save_template_description') }}
      </p>
      <b-form-input
        v-model="newTemplateName"
        :placeholder="$t('searchModule.modal.template_name_placeholder')"
        required
      />
    </b-modal>
  </div>
</template>

<script setup>
import { ref, computed, inject } from 'vue'
import { useSelectionTemplates } from '@/components/shared/SearchModule/composables/useSelectionTemplates'
import store from '@/store'
import mitt from '@/plugins/mitt.js'
import VueIcon from '@/components/shared/VueIcon.vue'

const emit = defineEmits(['template-saved'])

// Inject
const showSaveSelectionButton = inject('showSaveSelectionButton', true)

// State
const showSaveDialog = ref(false)
const newTemplateName = ref('')

// Composables
const { saveTemplate, getTemplates } = useSelectionTemplates()

const hasSelection = computed(() => store.getters['search_module/hasSelection'])

// Methods
const saveCurrentSelection = () => {
  console.debug('[SaveSelectionButton] Opening save dialog')
  showSaveDialog.value = true
}

const validateTemplateName = async () => {
  if (newTemplateName.value) {
    try {
      console.debug(
        '[SaveSelectionButton] Getting current selection from store...'
      )
      const fullSelection = store.getters['search_module/getSelection']
      const activeTab = store.getters['search_module/activeSearchFormTabName']

      // Create a new selection object with only the active tab's selections
      const currentSelection = {
        selectedDateRanges: fullSelection.selectedDateRanges,
      }

      // Only include selections from the active tab
      if (activeTab === 'vehicle' && fullSelection.selectedVehiclesIds) {
        currentSelection.selectedVehiclesIds = fullSelection.selectedVehiclesIds
      } else if (activeTab === 'driver' && fullSelection.selectedDriversIds) {
        currentSelection.selectedDriversIds = fullSelection.selectedDriversIds
      } else if (activeTab === 'circuit' && fullSelection.selectedCircuitsIds) {
        currentSelection.selectedCircuitsIds = fullSelection.selectedCircuitsIds
      }

      console.debug(
        '[SaveSelectionButton] Filtered selection for active tab:',
        currentSelection
      )

      console.debug(
        '[SaveSelectionButton] Saving template with name:',
        newTemplateName.value
      )
      await saveTemplate(newTemplateName.value, currentSelection)

      // Reload templates list after saving
      console.debug('[SaveSelectionButton] Reloading templates after save')
      const updatedTemplates = await getTemplates()
      store.dispatch('selection_templates/loadTemplates', updatedTemplates)

      // Emit events for synchronization
      console.debug('[SaveSelectionButton] Emitting template-saved event')
      emit('template-saved')
      mitt.emit('selection-templates/updated')

      closeDialog()
    } catch (error) {
      console.error('[SaveSelectionButton] Failed to save template:', error)
      // Here you might want to show an error message to the user
    }
  }
}

const closeDialog = () => {
  console.debug('[SaveSelectionButton] Closing save dialog')
  showSaveDialog.value = false
  newTemplateName.value = ''
}
</script>

<style lang="scss" scoped>
.save-selection-button {
  display: inline-block;
  font-size: 0.875rem;
}

// Import search selection modal styles
@import '@/styles/search-selection-modal';

@include modal-styles;
</style>
