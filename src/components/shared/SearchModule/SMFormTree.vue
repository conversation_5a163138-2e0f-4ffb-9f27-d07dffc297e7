<template>
  <div class="wrapper">
    <SMTree
      v-show="!SMFormTreeShowLoader.value"
      ref="tree"
      :options="treeOptions"
      :data="treeData"
      @checked="onTreeChecked"
    >
      <template #toolbar-right>
        <slot name="toolbar-right" />
      </template>
    </SMTree>
    <LoaderSpinner v-show="SMFormTreeShowLoader.value" />
  </div>
</template>
<script>
import SMTree from './SMTree.vue'
import { mapGetters } from 'vuex'
import LoaderSpinner from '@/components/shared/LoaderSpinner.vue'
export default {
  components: {
    SMTree,
    LoaderSpinner,
  },
  inject: {
    SMFormTreeShowLoader: { value: false },
  },
  props: {
    type: {
      type: String,
      default: 'vehicle',
    },
  },
  data() {
    return {
      treeOptions: {},
      treeData: {},
    }
  },
  computed: {
    ...mapGetters({
      vehicles: 'search_module/getVehicles',
      drivers: 'search_module/getDrivers',
      circuits: 'search_module/getCircuits',
      searchStringFromStore: 'search_module/searchString',
    }),
    items() {
      return this[this.type + 's']
    },
    categories() {
      let tables = {
        vehicle: 'getVehicleCategories',
        driver: 'getDriverCategories',
        circuit: 'getCircuitCategories',
      }
      return this.$store.getters[`search_module/${tables[this.type]}`]
    },
    selectedIds() {
      return this.$store.getters['search_module/getSelectedIdsByType'](
        this.type
      )
    },
  },
  watch: {
    searchStringFromStore() {
      this.treeData = this.generateTreeData()
    },
    items() {
      this.treeData = this.generateTreeData()
    },
  },
  mounted() {
    this.treeData = this.generateTreeData()
    this.$mitt.on('search_module/refresh', () => {
      this.treeData = this.generateTreeData()
    })
    window._smft = this
  },
  methods: {
    onTreeChecked() {
      // récupère les éléments sur lesquels on a déjà une selection en cours
      const selections = []
      selections['vehicle'] =
        this.$store.getters['search_module/getSelectedVehicles']
      selections['driver'] =
        this.$store.getters['search_module/getSelectedDrivers']
      selections['circuit'] =
        this.$store.getters['search_module/getSelectedCircuits']

      // isSearString vaut true si on vient de faire une recherche
      const isSearchString = !!this.$store.getters['search_module/searchString']

      let ids = this.$refs.tree.getChildless(true).map((c) => c.id)

      if (!isSearchString && ids.length === 0) {
        // on vide la selection
        this.$store.dispatch('search_module/clearSelection', this.type)
        return
      }

      let selection = selections[this.type].map((s) => {
        return s.id
      })

      // si la treeview est réduite par une recherche, on ne travaille que sur
      // les enfants visibles ( filtrés ). Ainsi on ne perd pas la selection des
      // élements non filtrés.
      if (isSearchString) {
        const idsFiltered = this.$refs.tree.getChildless().map((c) => c.id)
        selection = selection.filter((x) => idsFiltered.includes(x))
      }
      // met à jour ids en effectuant une opération de toggle sur
      // les éléments de selection. Autrement dit, si un élément est présent
      // dans ids, il est supprimé ; s'il est absent, il est ajouté.
      ids = ids
        .filter((x) => !selection.includes(x))
        .concat(selection.filter((x) => !ids.includes(x)))

      this.$store.dispatch('search_module/toggleItems', {
        type: this.type,
        ids,
      })
    },
    generateTreeData() {
      let searchString = this.searchStringFromStore
      let items = this.items
      let allCategories = this.categories
      let type = this.type //vehicle, driver, circuit
      let self = this

      function getChildren(cats) {
        let array =
          (cats &&
            cats.map((c) => {
              //Sub-categories (Nested)
              let mappedSubCategories = getChildren(
                allCategories.filter((cc) => cc.parent_id == c.id)
              )
              //All items under category (vehicle, driver, circuit)
              let mappedItems = items
                .filter(
                  (i) =>
                    i.categoryId == c.id &&
                    (!searchString ||
                      i.name.toLowerCase().includes(searchString.toLowerCase()))
                )
                .map((item) => ({
                  id: item.id,
                  label: item.name,
                  checked: self.$store.getters['search_module/isItemSelected'](
                    type,
                    item.id
                  ),
                }))

              //Match categories
              if (
                searchString &&
                c.name.toLowerCase().includes(searchString.toLowerCase())
              ) {
                mappedItems = [
                  ...mappedItems,
                  ...items
                    .filter(
                      (i) =>
                        i.categoryId == c.id &&
                        !mappedItems.some((item) => item.id == i.id)
                    )
                    .map((item) => ({
                      id: item.id,
                      label: item.name,
                      checked: self.$store.getters[
                        'search_module/isItemSelected'
                      ](type, item.id),
                    })),
                ]
              }

              return {
                //Categories (vehicle, driver, circuit)
                id: c.id,
                label: c.name,
                collapsed: !searchString,
                icon: getNodeIcon(type),
                //A category will be checked if all sub-categories and items are checked
                checked:
                  mappedSubCategories.filter((i) => i.checked).length ==
                    mappedSubCategories.length &&
                  mappedItems.filter((i) => i.checked).length ==
                    mappedItems.length,
                children: [
                  //Sub-categories (Nested)
                  ...mappedSubCategories,

                  //Items (vehicle, driver, circuit)
                  ...mappedItems,
                ],
              }
            })) ||
          []
        //Hide categories nodes without items/childrens
        array = array.filter((a) => a.children.length > 0)
        return array
      }

      return {
        //Parent categories (root level)
        children: getChildren(allCategories.filter((c) => !c.parent_id)),
      }
    },
  },
}

function getNodeIcon(type = 'vehicle') {
  return {
    vehicle: 'far fa-folder',
    user: 'fas fa-user',
    circuit: 'fas fa-road',
  }[type]
}
</script>
