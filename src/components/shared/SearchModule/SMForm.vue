<template>
  <div class="search_module__form">
    <nav v-if="tabs.length === 0" class="nav nav-pills flex-row">
      <a
        v-if="isFixedTabEnabled('vehicle')"
        class="w-25 py-2 pb-1 flex-fill text-center nav-link"
        :class="{
          active: tabName === 'vehicle',
          highlight: highlightVehiclesTab,
        }"
        @click="clickTab('vehicle')"
        >{{ $t('common.Véhicule') }}</a
      >
      <a
        v-if="isFixedTabEnabled('driver')"
        class="w-25 py-2 pb-1 flex-fill text-center nav-link"
        :class="{
          active: tabName === 'driver',
          highlight: highlightDriversTab,
        }"
        @click="clickTab('driver')"
        >{{ $t('common.Chauffeur') }}</a
      >
      <a
        v-if="isFixedTabEnabled('circuit')"
        class="w-25 py-2 pb-1 flex-fill text-center nav-link"
        :class="{
          active: tabName === 'circuit',
          highlight: highlightCircuitsTab,
        }"
        @click="clickTab('circuit')"
        >{{ $t('common.Circuit') }}</a
      >
    </nav>

    <nav
      v-if="
        filteredTabs.length > 0 &&
        !(filteredTabs.length === 1 && filteredTabs[0] === 'disabled')
      "
      v-show="!results || (showDynamicTabsOnResultView && !!results)"
      class="nav nav-pills flex-row"
    >
      <a
        v-for="tab in filteredTabs"
        :key="tab.value"
        class="w-25 p-0 py-2 pb-1 flex-fill text-center nav-link"
        :class="{
          active: tabName === tab.value,
          highlight: highlightCircuitsTab,
        }"
        @click="clickTab(tab.value)"
        >{{ tab.label }}</a
      >
    </nav>

    <div v-show="shouldShowList('vehicle')" class="list scrollbar">
      <b-spinner
        v-if="!isInitialLoadingComplete.vehicles"
        class="loader"
        variant="info"
        style="width: 3rem; height: 3rem"
      />

      <SMFormTree v-if="isInitialLoadingComplete.vehicles" type="vehicle">
        <template #toolbar-right>
          <slot name="toolbar-right" />
        </template>
      </SMFormTree>
    </div>
    <div v-show="shouldShowList('driver')" class="list scrollbar">
      <b-spinner
        v-if="!isInitialLoadingComplete.drivers"
        class="loader"
        variant="info"
        style="width: 3rem; height: 3rem"
      />

      <SMFormTree v-if="isInitialLoadingComplete.drivers" type="driver">
        <template #toolbar-right>
          <slot name="toolbar-right" />
        </template>
      </SMFormTree>
    </div>
    <div v-show="shouldShowList('circuit')" class="list scrollbar">
      <b-spinner
        v-if="!isInitialLoadingComplete.circuits"
        class="loader"
        variant="info"
        style="width: 3rem; height: 3rem"
      />

      <SMFormTree v-if="isInitialLoadingComplete.circuits" type="circuit">
        <template #toolbar-right>
          <slot name="toolbar-right" />
        </template>
      </SMFormTree>
    </div>

    <div
      v-if="isSearchModuleDatePickerEnabled()"
      v-show="!results"
      class="date-picker-wrapper"
    >
      <slot name="date-picker" :form-actions="formActions">
        <SMDatePicker
          ref="datePicker"
          @clear="onDateClear"
          @onDateSelection="onDateSelection"
        />
      </slot>
    </div>

  

    <div v-if="!!$slots['search-filters']" v-show="!results">
      <div v-show="showCustomFilters">
        <slot name="search-filters" />
      </div>
      <p
        v-show="showFiltersToggleButton"
        class="custom_filters_toggle_text"
        @click="() => (showCustomFilters = !showCustomFilters)"
      >
        <span v-show="showCustomFilters">{{
          $t('search_module.extra_filters.not_collapsed_title')
        }}</span>
        <span v-show="!showCustomFilters">{{
          $t('search_module.extra_filters.collapsed_title')
        }}</span>
        <em
          class="fas"
          :class="showCustomFilters ? 'fa-angle-up' : 'fa-angle-down'"
        />
      </p>
    </div>

    <slot> </slot>

    <div class="row m-0 p-0">
      <div class="col-md-12 m-0 form_buttons">
        <b-button
          v-if="!results"
          v-show="resetButton"
          class="btn btn-block btn-light mt-2"
          size="sm"
          @click="reset"
        >
          {{ $t('buttons.cancel_alternative') }}
        </b-button>

        <slot name="search-validate-button">
          <b-button
            v-if="!results"
            variant="primary"
            class="btn btn-block btn-primary mt-2"
            :class="{ disabled: validButtonDisabled }"
            size="sm"
            :disabled="validButtonDisabled"
            @click="validate"
          >
            <span v-show="!$store.state.search_module.isSearchInProgress">
              {{ $t('common.Valider') }}
            </span>
            <span v-show="$store.state.search_module.isSearchInProgress">
              <b-spinner
                class="loader"
                variant="info"
                style="
                  width: 1rem;
                  height: 1rem;
                  margin: 0 auto;
                  display: block;
                "
              />
            </span>
          </b-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'

import SMDatePicker from './SMDatePicker.vue'
import SMFormTree from './SMFormTree.vue'

/**
 * @todo Remove fixed tabs (use prop tabs instead)
 */
export default {
  components: {
    SMFormTree,
    SMDatePicker,
  },
  inject: {
    /**
     * @todo Remove fixed tabs
     */
    showDynamicTabsOnResultView: {
      default: false,
    },
    /**
     * Hides date picker (right validation)
     */
    isSearchModuleDatePickerEnabled: {
      default: () => () => true,
    },
    /**
     * Allow us to hide some tabs (rights validations)
     * Note: To define tabs, use "searchFormTabs" in SearchModule.vue instead
     */
    blacklistedSearchModuleFormTabs: {
      default: () => [],
    },
    showCustomFiltersByDefault: {
      default: true,
    },
    showFiltersToggleButtonByDefault: {
      default: true,
    },
  },
  props: {
    /*
     * True if SearchModule.view equals 'results'
     */
    results: {
      type: Boolean,
      default: false,
    },
    highlightVehiclesTab: {
      type: Boolean,
      default: false,
    },
    highlightDriversTab: {
      type: Boolean,
      default: false,
    },
    highlightCircuitsTab: {
      type: Boolean,
      default: false,
    },
    tabs: {
      type: Array,
      default: () => [],
    },
    resetButton: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tabName: '',
      showCustomFilters: this.showCustomFiltersByDefault,
      showFiltersToggleButton: this.showFiltersToggleButtonByDefault,
    }
  },
  computed: {
    ...mapGetters({
      vehicleCategories: 'search_module/getVehicleCategories',
      isInitialLoadingComplete: 'search_module/isInitialLoadingComplete',
      activeSearchFormTabName: 'search_module/activeSearchFormTabName',
      hasSelection: 'search_module/hasSelection',
      isValidButtonDisabled: 'search_module/isValidButtonDisabled',
    }),
    filteredTabs() {
      return this.tabs.filter((tabItem) => {
        return !this.blacklistedSearchModuleFormTabs.includes(tabItem.value)
      })
    },
    formActions() {
      return {
        onDateClear: () => this.onDateClear,
        onDateSelection: (ranges) => this.onDateSelection(ranges),
      }
    },
    validButtonDisabled() {
      return !this.hasSelection || this.isValidButtonDisabled
    },
  },
  watch: {
    /**
     * Two-way bind with activeSearchFormTabName
     */
    activeSearchFormTabName(v) {
      if (v !== this.tabName) {
        this.tabName = v
      }
    },
  },
  mounted() {
    if (
      this.tabs.length > 0 &&
      !(this.tabs.length === 1 && this.tabs[0] === 'disabled')
    ) {
      this.clickTab(this.tabs[0].value || this.tabs[0])
    }

    //@todo: Refactor once fixed tabs are gone
    if (this.tabs.length === 0) {
      let type = ['vehicle', 'driver', 'circuit'].find(
        (type) => !this.blacklistedSearchModuleFormTabs.includes(type)
      )
      if (type) {
        this.clickTab(type)
      }
    }
  },
  methods: {
    /**
     * Blacklisted tabs will be skip (rights validations)
     */
    isFixedTabEnabled(name) {
      return !this.blacklistedSearchModuleFormTabs.includes(name)
    },
    reset() {
      this.$emit('reset')
    },
    shouldShowList(name) {
      return this.tabName == name && !this.results
    },
    validate() {
      this.$emit('validate')
    },
    //Clearing the date picker in the form modules removes all the selected date-ranges
    onDateClear() {
      this.$store.dispatch('search_module/clearDateSelection')
    },
    onDateSelection(ranges) {
      this.onDateClear()
      ranges.forEach((range) => {
        this.selectItem(null, range, 'date_range')
      })
    },
    selectItem(e, value, type) {
      this.$store.dispatch('search_module/selectItem', {
        value,
        type,
        origin: 'SMForm::selectItem',
      })
      e && e.stopPropagation()
      e && e.preventDefault()
    },
    clickTab(name) {
      console.debugVerbose(8, 'SMForm clickTab', name)
      this.tabName = name
      this.$store.dispatch('search_module/activeSearchFormTabName', name)
    },
  },
}
</script>

<style lang="scss">
.search_module__form {
  padding: 5px 0;
  width: calc(100% + 3px);
  background-color: white;
  margin: 0 0 0 -3px;
}

.search_module__form .nav {
  background-color: white;
  padding: 0 19px 0 23px;
}

.search_module__form .nav-pills {
  cursor: pointer;
}

.search_module__form .nav-pills .nav-link {
  color: var(--color-denim) !important;
  cursor: pointer;
}

.search_module__form .nav-pills .nav-link.active,
.search_module__form .nav-pills .show > .nav-link {
  border-bottom: 3px solid var(--color-hamburger);
  border-radius: 0;
}

.search_module__form .nav-pills .nav-link.active {
  background-color: transparent;
}

.search_module__form .nav-pills .nav-link.highlight {
  //background-color: #0a71ac17;
}

.search_module__form a {
  color: #495057;
}

.search_module__form .loader {
  margin: 0 auto;
  display: block;
  margin-top: 10px;
}

.search_module__form .category,
.search_module__form .vehicle {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.category_icon {
  color: #808080a1;
}

.search_module__form .category span {
  font-size: 14px;
}

.search_module__form .list {
  min-height: 205px;
  max-height: calc(
    100vh // screen height
    - 40px // search wrapper header height + paddings (5px per padding)
    - 50px // search input height
    - 43px // tabs height
    - 5px // form padding
    - (20px * 2) // tree margin * 2 (top bottom)
    - 34px // date input height
    - 45px // KLM button height
    - 45px // Validate button height
    - 11px // bottom offset for visibility
    - 100px //extra margin
  );
  overflow-y: auto;
  width: calc(100% - 20px - 20px);
  padding: 0 10px 0 0;
  margin: 5px 20px 20px 23px;
  scrollbar-color: white;

  &::-webkit-scrollbar-track {
    width: 2px;
    background: #48484820;
  }
}

.custom_filters_toggle_text {
  padding-top: 20px;
  font: normal normal normal 12px/14px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
  cursor: pointer;
  display: flex;
  justify-content: center;
  margin-bottom: 0;

  em {
    margin-left: 5px;
    font-size: 14px;
  }
}

.form_buttons {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  column-gap: 15px;
  align-items: flex-end;
  padding: 0 20px 0 23px;

  button {
    font: normal normal bold 14px/21px Open Sans;
    letter-spacing: 0px;
    color: #484848;
    width: auto;
    flex-shrink: 0;

    &[disabled].disabled {
      cursor: not-allowed;

      &:hover {
        opacity: 0.65;
      }
    }
  }

  button.btn-primary {
    font: normal normal bold 14px/21px Open Sans;
    letter-spacing: 0px;
    color: #ffffff;
  }
}

.date-picker-wrapper {
  padding: 0 20px 0 23px;
}
</style>
