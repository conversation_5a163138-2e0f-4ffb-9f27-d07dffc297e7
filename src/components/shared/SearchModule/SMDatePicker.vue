<template>
  <div v-show="showInput" class="pop_calendar">
    <div
      v-if="searchModuleDatetimePickerPlaceholder !== ''"
      class="filter_item"
    >
      <span class="filter_label">
        {{ $t(searchModuleDatetimePickerLabel) }}
        <i v-show="searchModuleDatetimeRequired" style="color: red">&nbsp;*</i>
      </span>
    </div>
    <date-picker
      ref="datepicker"
      v-model="range"
      class="sm_datepicker search-module-input"
      popup-class="sm_datepicker__popup"
      :props="config"
      :lang="lang"
      type="date"
      :show-week-number="true"
      :range="selectedType === 'range'"
      :multiple="selectedType === 'each_day'"
      :shortcuts="shortcuts"
      :format="$date.getDatePattern()"
      :open="visible"
      :disabled="isSearchModuleDatePickerDisabledHandler()"
      :placeholder="
        $te(searchModuleDatetimePickerPlaceholder)
          ? $t(searchModuleDatetimePickerPlaceholder)
          : ''
      "
      @focus="open('')"
      @confirm="confirmSelection"
      @clear="$emit('clear')"
      @pick="handlePick"
    >
      <div slot="footer" class="container-fluid p-0">
        <div class="mx-0 mt-2 text-left">
          <b-form-group
            v-slot="{ ariaDescribedby }"
            :label="$t('searchModule.date_picker.selection_label')"
            label-class="date_picker_label"
          >
            <b-form-radio-group
              v-model="selectedType"
              class="date-picker-form-wrapper"
              :options="selectionOptions"
              :aria-describedby="ariaDescribedby"
              name="type-options"
            />
          </b-form-group>
        </div>

        <div v-if="showTimePicker !== false" class="mx-0">
          <div class="mx-0 mt-2 text-left">
            <b-form-group
              :label="$t('searchModule.date_picker.time_slots')"
              label-class="date_picker_label"
            >
              <div class="date-picker-form-wrapper">
                <div class="mx-0 my-3">
                  <div class="col-12 px-0 d-flex flex-row">
                    <div class="text-left">
                      <label class="time_label">{{
                        $t('search_module.date_picker.time_start')
                      }}</label>
                      <flat-pickr
                        :key="'first'"
                        v-model="timeFrom"
                        :config="timePickerConfig"
                      />
                    </div>
                    <div class="ml-2 text-left">
                      <label class="time_label">{{
                        $t('search_module.date_picker.time_end')
                      }}</label>
                      <flat-pickr
                        :key="'last'"
                        v-model="timeTo"
                        :config="timePickerConfig"
                      />
                    </div>
                  </div>
                  <div>
                    <span v-if="isValidateButtonDisabled" class="text-danger">{{
                      $t('datepicker.timefrom_after_timeto')
                    }}</span>
                  </div>
                </div>
                <!--  Apply timerange to each daterange day  -->
                <b-form-checkbox v-model="applyTimeRangeToEachDayOfDateRange">
                  {{ $t('search_module.date_picker.time_mode.all_day') }}
                </b-form-checkbox>
              </div>
            </b-form-group>
          </div>
        </div>
        <div class="mx-0">
          <button class="mx-btn mx-datepicker-btn-confirm" @click="cancel">
            {{ $t('buttons.cancel_alternative') }}
          </button>

          <button
            class="mx-btn mx-datepicker-btn-confirm"
            :disabled="isValidateButtonDisabled"
            @click="handleConfirmSelection"
          >
            {{ $t('search_module.date_picker.validate') }}
          </button>
        </div>
      </div>
    </date-picker>
  </div>
</template>

<script>
import DatePicker from 'vue2-datepicker'
import 'vue2-datepicker/locale/fr'
import moment from 'moment'
import * as R from 'ramda'
import flatPickr from 'vue-flatpickr-component'
import 'flatpickr/dist/flatpickr.css'
import 'vue2-datepicker/index.css'
import { mapGetters } from 'vuex'
import { frenchDatePattern } from '@/plugins/plugin-i18n-dates'
import i18nDatepickerMixin from '@/mixins/i18n-datepicker.js'
import clickableWeekDaysMixin, {
  SMDatePickerClickableWeekDaysMixin,
} from '@/mixins/vue2-datepicker-weeks.js'
import { SMDatePickerOnPickEventMixin } from '@/mixins/vue2-datepicker-onPick-event'
import { computeStartAndEndTimeOfDate } from '@/services/search-service'
import {
  isDateAfterDate,
  sortDatesAscending,
  stripMillisecondsFromDate,
} from '@/utils/dates'
import { usePushDateRangeToSelection } from '@/composables/usePushDateRangeToSelection'

export default {
  name: 'SMDatePicker',
  components: {
    DatePicker,
    flatPickr,
  },
  mixins: [
    i18nDatepickerMixin,
    clickableWeekDaysMixin,
    SMDatePickerClickableWeekDaysMixin,
    SMDatePickerOnPickEventMixin,
  ],
  inject: {
    searchModuleDatetimePickerPlaceholder: {
      default: '',
    },
    searchModuleDatetimePickerLabel: {
      default: '',
    },
    searchModuleDatetimeRequired: {
      default: false,
    },
    /**
     * Delegates predefined selection click event (event listener)
     */
    onSearchModuleDatePickerPredefinedSelectionClick: {
      default: () => () => {},
    },
    /**
     * Custom handler to decide whenever to disable/enable the date picker (rights validations)
     */
    isSearchModuleDatePickerDisabledHandler: {
      default: () => () => {},
    },
    /**
     * Whether to show the time picker or not
     */
    showTimePicker: {
      default: true,
    },
  },
  props: {
    config: { type: Object, default: () => ({}) },
    value: {
      type: Array,
      default: () => [],
    },
    showInput: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      selectedType: 'each_day',
      selectionOptions: [
        {
          text: this.$t('search_module.date_picker.mode_each_day'),
          value: 'each_day',
        },
        {
          text: this.$t('search_module.date_picker.mode_range'),
          value: 'range',
        },
      ],
      timeFrom: '00:00',
      timeTo: '23:59',
      timePickerConfig: {
        enableTime: true,
        noCalendar: true,
        dateFormat: 'H:i',
        time_24hr: true,
        static: true,
      },
      visible: false,
      range: this.value || [],
      //Prevents range deletion onClick on shortcuts (7 last days, etc)
      shouldKeepRange: false,
      shortcuts: [
        {
          text: this.$t('search_module.shorcuts.today'),
          onClick: () => {
            const now = new Date()

            this.range = [
              stripMillisecondsFromDate(now),
              stripMillisecondsFromDate(now),
            ]
          },
        },
        {
          text: this.$t('search_module.shorcuts.yesterday'),
          onClick: () => {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24)

            this.range = [
              stripMillisecondsFromDate(date),
              stripMillisecondsFromDate(date),
            ]
          },
        },
        {
          text: this.$t('search_module.shorcuts.last_seven_days'),
          onClick: () => {
            //Pass shouldKeepRange to true to prevent range deletion
            this.shouldKeepRange = true

            this.selectedType = 'range'
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            const end = new Date()

            const cleanStart = stripMillisecondsFromDate(start)
            const cleanEnd = stripMillisecondsFromDate(end)

            this.range = [cleanStart, cleanEnd]

            this.onSearchModuleDatePickerPredefinedSelectionClick({
              length: moment(cleanEnd).diff(moment(cleanStart), 'days') + 1,
              limitDateSelectionRangeToNDays: (length) => {
                this.range = [
                  cleanStart,
                  stripMillisecondsFromDate(
                    moment(cleanStart).add(length, 'days').toDate()
                  ),
                ]
              },
            })
            //Pass shouldKeepRange to false : prevent range to be kept if selectedType changes again
            this.$nextTick(() => {
              this.shouldKeepRange = false
            })
          },
        },
        {
          text: this.$t('search_module.shorcuts.current_month'),
          onClick: () => {
            //Pass shouldKeepRange to true to prevent range deletion
            this.shouldKeepRange = true

            this.selectedType = 'range'

            const start = stripMillisecondsFromDate(
              moment().startOf('month').toDate()
            )
            const end = stripMillisecondsFromDate(
              moment().endOf('month').toDate()
            )

            this.range = [start, end]

            this.onSearchModuleDatePickerPredefinedSelectionClick({
              length: moment(end).diff(moment(start), 'days') + 1,
              limitDateSelectionRangeToNDays: (length) => {
                this.range = [
                  start,
                  stripMillisecondsFromDate(
                    moment(start).add(length, 'days').toDate()
                  ),
                ]
              },
            })
            //Pass shouldKeepRange to false : prevent range to be kept if selectedType changes again
            this.$nextTick(() => {
              this.shouldKeepRange = false
            })
          },
        },
        {
          text: this.$t('search_module.shorcuts.last_month'),
          onClick: () => {
            //Pass shouldKeepRange to true to prevent range deletion
            this.shouldKeepRange = true

            this.selectedType = 'range'

            const start = stripMillisecondsFromDate(
              moment().subtract(1, 'month').startOf('month').toDate()
            )
            const end = stripMillisecondsFromDate(
              moment().subtract(1, 'month').endOf('month').toDate()
            )

            this.range = [start, end]

            this.onSearchModuleDatePickerPredefinedSelectionClick({
              length: moment(end).diff(moment(start), 'days') + 1,
              limitDateSelectionRangeToNDays: (length) => {
                this.range = [
                  start,
                  stripMillisecondsFromDate(
                    moment(start).add(length, 'days').toDate()
                  ),
                ]
              },
            })
            //Pass shouldKeepRange to false : prevent range to be kept if selectedType changes again
            this.$nextTick(() => {
              this.shouldKeepRange = false
            })
          },
        },
      ],
    }
  },
  computed: {
    ...mapGetters({
      selectedDateRanges: 'search_module/getSelectedDateRanges',
      getApplyTimeRangeToEachDayOfDateRange:
        'search_module/getApplyTimeRangeToEachDayOfDateRange',
    }),
    applyTimeRangeToEachDayOfDateRange: {
      get() {
        return this.getApplyTimeRangeToEachDayOfDateRange
      },
      set(value) {
        this.$store.state.search_module.applyTimeRangeToEachDayOfDateRange =
          value
      },
    },
    isValidateButtonDisabled() {
      return (
        (this.getApplyTimeRangeToEachDayOfDateRange ||
          this.range.length === 1) &&
        isDateAfterDate(this.timeFrom, this.timeTo, 'HH:mm')
      )
    },
  },
  watch: {
    selectedDateRanges: {
      handler(dateRanges) {
        this.syncWithStore(dateRanges)
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    if (this.showInput) {
      this.syncWithStore(this.selectedDateRanges)
    }
  },
  destroyed() {
    //Tries to destroy the datepicker component
    try {
      this.$children[0].$destroy()
    } catch (err) {
      console.warn('Fail to destroy')
    }
  },
  methods: {
    syncWithStore(dateRanges) {
      //Sync with store: Clear selection
      if (dateRanges.length === 0) {
        this.range = []
        this.timeFrom = '00:00'
        this.timeTo = '23:59'
        return
      }
      //Sync with store: Update with latest selection (if local date picker has no selection)
      if (dateRanges.length >= 1 && this.range.length === 0) {
        if (dateRanges.length === 1) {
          this.range = [dateRanges[0][0], dateRanges[0][1]]
          this.selectedType = 'each_day'
        } else {
          this.range = [dateRanges[0][0], dateRanges[dateRanges.length - 1][1]]
          this.selectedType = 'range'
        }
        this.timeFrom = moment(dateRanges[dateRanges.length - 1][0]).format(
          'HH:mm'
        )
        this.timeTo = moment(dateRanges[dateRanges.length - 1][1]).format(
          'HH:mm'
        )
      }
    },
    setFromString(dateAsString) {
      let dateSeparationChar = '-'
      let dateWithSlashes = dateAsString.split('-').join('/')
      this.range = []
      this.timeFrom = '00:00'
      this.timeTo = '23:59'
      if (
        dateAsString.split(dateSeparationChar).length === 2 &&
        moment(
          dateWithSlashes + '/' + moment().year(),
          frenchDatePattern
        ).isValid()
      ) {
        this.range = [
          moment(dateWithSlashes + '/' + moment().year(), frenchDatePattern)._d,
          moment(dateWithSlashes + '/' + moment().year(), frenchDatePattern)._d,
        ]
      }

      if (
        dateAsString.split(dateSeparationChar).length === 2 &&
        dateAsString.split(dateSeparationChar)[1] === '' &&
        moment(
          dateWithSlashes +
            moment().month().toString() +
            moment().year().toString(),
          frenchDatePattern
        ).isValid()
      ) {
        this.range = [
          moment(
            dateWithSlashes +
              moment().month().toString() +
              moment().year().toString(),
            frenchDatePattern
          )._d,
          moment(
            dateWithSlashes +
              moment().month().toString() +
              moment().year().toString(),
            frenchDatePattern
          )._d,
        ]
      }

      if (
        dateAsString.split(dateSeparationChar).length === 3 &&
        moment(dateWithSlashes, frenchDatePattern).isValid()
      ) {
        this.range = [
          moment(dateWithSlashes, frenchDatePattern)._d,
          moment(dateWithSlashes, frenchDatePattern)._d,
        ]
      }
      if (dateAsString === dateSeparationChar) {
        this.range = [moment()._d, moment()._d]
      }

      return this.range
    },
    open(dateAsString) {
      this.setFromString(dateAsString)
      this.visible = true
    },
    cancel() {
      this.range = []
      this.timeFrom = '00:00'
      this.timeTo = '23:59'
      this.visible = false
      this.$emit('clear')
    },
    confirmSelection(value) {
      const { pushDateRangeToSelection } = usePushDateRangeToSelection()

      let selectionArray = []

      const applyTimeRangeToEachDayOfDateRange =
        this.$store.getters[
          'search_module/getApplyTimeRangeToEachDayOfDateRange'
        ]

      // Sort the dates first
      try {
        value = sortDatesAscending(value)
      } catch (error) {
        console.error('Error sorting dates:', error)
        return // Exit the function if sorting fails
      }

      if (this.selectedType === 'range') {
        const daysDiff = Math.abs(
          moment(value[0]).diff(moment(value[1]), 'days')
        )

        R.range(0, daysDiff + 1).forEach((index) => {
          let baseDate = moment(value[0]).add(index, 'days')

          const [m, mEnd] = computeStartAndEndTimeOfDate(
            baseDate,
            index,
            daysDiff + 1,
            this.timeFrom,
            this.timeTo,
            applyTimeRangeToEachDayOfDateRange
          )

          selectionArray = pushDateRangeToSelection(m, mEnd, selectionArray)
        })
      } else {
        value.forEach((date, index) => {
          const [m, mEnd] = computeStartAndEndTimeOfDate(
            date,
            index,
            value.length,
            this.timeFrom,
            this.timeTo,
            applyTimeRangeToEachDayOfDateRange
          )

          selectionArray = pushDateRangeToSelection(m, mEnd, selectionArray)
        })
      }

      this.$emit('onDateSelection', selectionArray)
      this.visible = false

      if (!this.showInput) {
        this.range = []
        this.timeFrom = '00:00'
        this.timeTo = '23:59'
      }
    },
    /**
     * Fix to handle vue2-datepicker behaviour which doesn't support date range of only one date (#47518)
     * @param value
     */
    handlePick(value) {
      //Skip if selectedType is not range
      if (this.selectedType !== 'range') {
        return
      }

      this.range = this.handlePickMixinForRangeType(
        value,
        this.selectedType,
        this.range
      )
    },
    handleConfirmSelection() {
      this.confirmSelection(this.range)
    },
  },
}
</script>
<style>
.sm_datepicker__popup .mx-datepicker-sidebar {
  width: 160px !important;
}
.sm_datepicker__popup .mx-datepicker-sidebar + .mx-datepicker-content {
  margin-left: 160px !important;
  width: min-content;
}
.sm_datepicker__popup .time_label {
  padding-right: 10px;
}
.sm_datepicker__popup .type_label {
  min-width: 100px;
  cursor: pointer;
  margin-left: 10px;
  text-align: left;
}
.sm_datepicker__popup .flatpickr-input {
  max-width: 100%;
}
.sm_datepicker {
  .mx-input-wrapper {
    .mx-input {
      height: 40px !important;
      box-shadow: none !important;
      border: none !important;
    }
  }
}
.pop_calendar .sm_datepicker,
.pop_calendar .sm_datepicker.mx-datepicker {
  max-width: 100%;
  width: 100%;
}
.mx-calendar {
  margin: 0 auto;
}

.date-picker-form-wrapper {
  padding: 5px;
  border: 1px solid #00000014;
}

.date_picker_label {
  font-weight: bold;
}

.mx-datepicker-btn-confirm {
  margin: 0px 3px;
}

.filter_label {
  font: normal normal normal 10px/14px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
  margin: 0px;
}
</style>
