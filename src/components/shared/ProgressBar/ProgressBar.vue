<template>
  <div class="progress-bar">
    <div
      class="progress-fill"
      :style="{ width: percentage + '%', backgroundColor: fillColor }"
    ></div>
    <div class="progress-text">{{ percentage }}%</div>
  </div>
</template>

<script setup>
const props = defineProps({
  percentage: {
    type: Number,
    required: true,
    validator: (val) => val >= 0 && val <= 100,
  },
})

// Map 0-100% to hue angle 0° (red) to 160° (green)
const fillColor = computed(() => {
  const hue = (props.percentage * 160) / 100
  return `hsl(${hue}, 56%, 49%)`
})
</script>

<style scoped lang="scss">
.progress-bar {
  position: relative;
  width: 100%;
  height: 20px;
  background-color: var(--color-wild-sand);
  border: 1px solid var(--color-border);
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #42b983;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: 600;
  color: black;
  pointer-events: none; /* so it doesn't interfere with clicks */
  user-select: none;
  font-size: 12px;
}
</style>
