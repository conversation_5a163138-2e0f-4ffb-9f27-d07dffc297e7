<template lang="pug">
.laf-title(@click="()=>$emit('click')")
    //.em.fas.fa-arrow-left(:style="style")
    Icon(
        icon="ic:round-arrow-back-ios-new"
        color="var(--color-main)"
        :style="{ fontSize: '20px' }"
    )
    span(:style="style" :class="{small:small}") {{title}}
</template>
<script setup>
import { Icon } from '@iconify/vue2'
import { computed } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: 'Title',
  },
  color: {
    type: String,
    default: '',
  },
  small: {
    type: Boolean,
    default: false,
  },
})
const style = computed(() => {
  let style = ''
  if (props.color) {
    style += `color:${props.color}`
  }
  return style
})
</script>
<style lang="scss" scoped>
.laf-title {
  display: flex;
  column-gap: 5px;
  align-items: center;
  cursor: pointer;

  em {
    position: relative;
  }

  span {
    font: normal normal bold 14px/20px Open Sans;
    letter-spacing: 0px;
    color: #014470;
    line-height: 15px;
  }
  span.small {
    max-width: 150px;
  }
}
</style>
