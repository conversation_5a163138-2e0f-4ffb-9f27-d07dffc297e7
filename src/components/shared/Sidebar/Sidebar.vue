<template>
  <keep-alive>
    <div
      v-show="!isDevice || (isDevice && isSidebarExpanded)"
      class="links"
      :class="rootClassname"
      style="position: relative"
    >
      <div
        v-if="$store.getters['sidebar/hasBackdrop']"
        class="sidebar__backdrop"
      ></div>
      <!-- localisation -->
      <SidebarButton
        v-if="isModuleEnabled('location_module')"
        v-show="hasRight('location_access')"
        :tooltip="$t('module_names.realtime')"
        :to="{ name: 'location_module' }"
        :icon-src="localisationIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-location)"
        :text="$t('module_names.realtime')"
      />
      <!-- Tableau de bord -->
      <SidebarButton
        v-if="isModuleEnabled('dashboard_module')"
        v-show="hasRight('dashboard_access')"
        :tooltip="$t('module_names.dashboard')"
        to_disabled="{ name: 'dashboard_module' }"
        href="#"
        :icon-src="dashboardIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-dashboard)"
        :text="$t('module_names.dashboard')"
        @click="
          () => $store.dispatch('app/setCurrentFloatingModule', 'dashboard')
        "
      />
      <!-- Identification -->
      <SidebarButton
        v-if="isModuleEnabled('sensors_module')"
        v-show="hasRight('identification_access')"
        :tooltip="$t('module_names.sensors')"
        :to="{ name: 'sensors_module' }"
        :icon-src="signalIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-identification)"
        :text="$t('module_names.sensors')"
      />
      <!-- Diagnostic -->
      <SidebarButton
        v-if="isModuleEnabled('diagnostics_module')"
        v-show="hasRight('diagnostics_access')"
        :tooltip="$t('module_names.diagnostics')"
        :to="{ name: 'diagnostics_module' }"
        :icon-src="diagnosticsIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        :text-color="defaultColor"
        :text="$t('module_names.diagnostics')"
      />
      <!-- Evènements -->
      <SidebarButton
        v-if="isModuleEnabled('events_module')"
        v-show="hasRight('events_access')"
        :tooltip="$t('module_names.eventsCartography')"
        :to="{ name: 'events_module' }"
        :icon-src="calendarIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-events)"
        :text="$t('module_names.eventsCartography')"
      />
      <!-- Contenants -->
      <SidebarButton
        v-if="isModuleEnabled('containers_module')"
        v-show="hasRight('contenants_access')"
        :tooltip="$t('module_names.containers_module')"
        :to="{ name: 'containers_module' }"
        :icon-src="containersModuleIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-containers-module)"
        :text="$t('module_names.containers_module')"
      />
      <!-- Alertes -->
      <SidebarButton
        v-if="isModuleEnabled('alerts_module')"
        v-show="hasRight('alerts_access')"
        :tooltip="$t('module_names.alerts')"
        :to="{ name: 'alerts_module' }"
        :icon-src="alertsIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-alerts)"
        :text="$t('module_names.alerts')"
      />
      <!-- Operational Statistics -->
      <SidebarButton
        v-if="isModuleEnabled('operational_statistics_module')"
        v-show="true || hasRight('GEOV3_OPERATIONAL_STATISTICS_ACCESS')"
        :tooltip="$t('operationalStatistics.title')"
        :to="{ name: 'statsmodule' }"
        :icon-src="chartIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        :text-color="defaultColor"
        :text="$t('operationalStatistics.title')"
      />
      <!-- Messagerie -->
      <SidebarButton
        v-if="isModuleEnabled('messages_module')"
        v-show="hasRight('messages_access')"
        :tooltip="$t('module_names.messages')"
        :to="{ name: 'messages_module' }"
        :icon-src="messagesIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-messages)"
        :text="$t('module_names.messages')"
      />
      <!-- Zones -->
      <SidebarButton
        v-if="isModuleEnabled('zones_module')"
        v-show="
          hasRight('zones_access') &&
          (hasRight('zones_view') ||
            hasRight('zones_types_admin') ||
            hasRight('zones_types_view'))
        "
        :tooltip="$t('module_names.zones')"
        :to="{ name: 'zones_module' }"
        :icon-src="zonesIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-zones)"
        :text="$t('module_names.zones')"
      />
      <!-- Points noirs -->
      <SidebarButton
        v-if="isModuleEnabled('blackspots_module')"
        v-show="hasRight('blackspot_access')"
        :tooltip="$t('blackspot.module_label')"
        :to="{ name: 'blackspot' }"
        :icon-src="blackspotsIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-black-points)"
        :text="$t('blackspot.module_label')"
      />
      <!-- Conteneurs -->
      <SidebarButton
        v-if="isModuleEnabled('containers_management')"
        v-show="hasRight('container_access')"
        :tooltip="$t('module_names.containers')"
        :to="{ name: 'containers_management' }"
        :icon-src="containersIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-containers)"
        :text="$t('module_names.containers')"
      />
      <!-- Ecoconduite -->
      <SidebarButton
        v-if="isModuleEnabled('ecoconduite_module')"
        v-show="hasRight('ecoconduite_access')"
        :tooltip="$t('module_names.ecoconduite')"
        :to="{ name: 'ecoconduite_module' }"
        :icon-src="ecoconduiteIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-ecoconduite)"
        :text="$t('module_names.ecoconduite')"
      />
      <!-- Aide en ligne -->
      <SidebarButton
        v-if="isModuleEnabled('help')"
        v-show="hasRight('help_access')"
        :tooltip="$t('module_names.questions')"
        href_disabled="getGeoredV2URL('/desktop/index.php/documentation/index/')"
        href="#"
        target="_blank"
        :icon-src="questionIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-help)"
        :text="$t('module_names.questions')"
        @click="onHelpLinkClick"
      />
      <!-- Admin -->
      <SidebarButton
        v-if="isModuleEnabled('admin')"
        v-show="hasExternalRight('admin')"
        :tooltip="$t('module_names.admin')"
        :href="getGeoredV2URL('/admin/index.php/')"
        target="_blank"
        :icon-src="adminIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-admin)"
        :text="$t('module_names.admin')"
      />
      <!-- Analyses -->
      <SidebarButton
        v-if="isModuleEnabled('analysis')"
        v-show="hasExternalRight('analyse')"
        :tooltip="$t('module_names.analysis')"
        :href="getGeoredV2URL('/analyses/index.php/')"
        target="_blank"
        :icon-src="chartIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-analysis)"
        :text="$t('module_names.analysis')"
      />
      <!-- Citi Mission / GDMV2 -->
      <SidebarButton
        v-show="
          hasExternalRight('citimission') ||
          hasExternalRight('gdm') ||
          hasExternalRight('gdmv2')
        "
        :tooltip="$t('module_names.citi_mission')"
        :href="getCitiMissionURL('/#')"
        target="_blank"
        :icon-src="citimissionIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-citimission)"
        :text="$t('module_names.citi_mission')"
      />
      <!-- Citi PAV -->
      <SidebarButton
        v-if="isModuleEnabled('citipav')"
        v-show="hasExternalRight('citipav')"
        :tooltip="$t('module_names.citipav')"
        :href="getCitipavURL()"
        target="_blank"
        :icon-src="citipavIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-citipav)"
        :text="$t('module_names.citipav')"
      />
      <!-- Citi Board / SimpliBoard -->
      <SidebarButton
        v-show="
          hasExternalRight('citiboard') || hasExternalRight('simpliboard')
        "
        :tooltip="$t('module_names.citi_board')"
        :href="getCitiBoardURL('/dashboard-v2')"
        target="_blank"
        :icon-src="citiboardIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-geored-desktop)"
        :text="$t('module_names.citi_board')"
      />
      <!-- Editour -->
      <SidebarButton
        v-show="hasExternalRight('editour')"
        :tooltip="$t('module_names.editour')"
        :href="getEditourURL('/')"
        target="_blank"
        :icon-src="editourIcon"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-editour)"
        :text="$t('module_names.editour')"
      />
      <hr />
      <!-- Sélection client -->
      <SidebarButton
        v-if="canUseClientSelectionFeature"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-login-as)"
        :text="currentUsername"
        href="#"
        :icon-src="accountIcon"
        :tooltip="$t('sidebar.tooltip.login_as')"
        @click="onAccountLinkClick"
      />
      <!-- Paramètres -->
      <SidebarButton
        :hover-background="defaultColor"
        hover-fill="white"
        href="#"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-settings)"
        :text="$t('sidebar.button_settings')"
        :icon-src="settingsIcon"
        :tooltip="$t('sidebar.tooltip.settings')"
        @click="
          () => $store.dispatch('app/setCurrentFloatingModule', 'settings')
        "
      />
      <!-- Déconnexion -->
      <SidebarButton
        :hover-background="defaultColor"
        hover-fill="white"
        href="#"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-logout)"
        :text="$t('sidebar.button_logout')"
        :icon-src="logoutIcon"
        :tooltip="$t('sidebar.tooltip.logout')"
        @click="logoutFromApplication"
      />

      <!--<SidebarButton
      v-if="isModuleEnabled('circuit_module')"
      v-show="hasRight('circuit_access')"
      :tooltip="$t('module_names.circuit')"
      :to="{ name: 'circuit_module' }"
      :icon-src="require('./assets/marker.svg')"
      :hover-background="defaultColor"
      hover-fill="white"
      :class="{ isExpanded: isSidebarExpanded }"
      :text-color="defaultColor"
      :text="$t('module_names.circuit')"
    />-->

      <!--
    <SidebarButton
      v-if="isModuleEnabled('objects_module')"
      v-show="hasRight('objects_access')"
      :tooltip="$t('module_names.objects')"
      :to="{ name: 'objects_module' }"
      :icon-src="require('./assets/marker_package.svg')"
      :hover-background="defaultColor"
      hover-fill="white"
      :class="{ isExpanded: isSidebarExpanded }"
      :text-color="defaultColor"
      :text="$t('module_names.objects')"
    />-->
      <!-- CITIFRET 20250122 suppression de la sidebar #46913
      <SidebarButton
        v-if="isModuleEnabled('citifret')"
        v-show="hasExternalRight('citifret')"
        :tooltip="$t('module_names.citifret')"
        :href="getCitiFretURL('/#/citifret')"
        target="_blank"
        :icon-src="require('./assets/citifret.svg')"
        :hover-background="defaultColor"
        hover-fill="white"
        :class="{ isExpanded: isSidebarExpanded }"
        text-color="var(--color-citifret)"
        :text="$t('module_names.citifret')"
      />
-->
    </div>
  </keep-alive>
</template>
<script>
import SidebarButton from './SidebarButton'
import externalAppsURLsMixin from '@/mixins/externalAppsURLs'
import { mapGetters } from 'vuex'
import Vue from 'vue'
import localisationIcon from '@c/shared/Sidebar/assets/localisation.svg'
import dashboardIcon from '@c/shared/Sidebar/assets/dashboard.svg'
import zonesIcon from '@c/shared/Sidebar/assets/zones.svg'
import citipavIcon from '@c/shared/Sidebar/assets/citi_pav.svg'
import adminIcon from '@c/shared/Sidebar/assets/admin.svg'
import diagnosticsIcon from '@c/shared/Sidebar/assets/diagnostics.svg'
import chartIcon from '@c/shared/Sidebar/assets/chart.svg' /* analyses */
import alertsIcon from '@c/shared/Sidebar/assets/alertes.svg'
import questionIcon from '@c/shared/Sidebar/assets/question.svg'
import signalIcon from '@c/shared/Sidebar/assets/signal.svg' /* identification */
import ecoconduiteIcon from '@c/shared/Sidebar/assets/eco_conduite.svg'
import citifretIcon from '@c/shared/Sidebar/assets/citifret.svg'
import calendarIcon from '@c/shared/Sidebar/assets/calendar.svg' /*events */
import messagesIcon from '@c/shared/Sidebar/assets/messages.svg'
import containersIcon from '@c/shared/Sidebar/assets/container.svg' /* containers management */
import blackspotsIcon from '@c/shared/Sidebar/assets/blackspots.svg'
import containersModuleIcon from '@c/shared/Sidebar/assets/containersModule.svg' /* containers module */
import citimissionIcon from '@c/shared/Sidebar/assets/CitiMission.svg'
import citiboardIcon from '@c/shared/Sidebar/assets/CitiBoard.svg'
import editourIcon from '@c/shared/Sidebar/assets/editour.svg'
import accountIcon from '@c/shared/Sidebar/assets/sidebar-account.svg'
import settingsIcon from '@c/shared/Sidebar/assets/sidebar-settings.svg'
import logoutIcon from '@c/shared/Sidebar/assets/sidebar-logout.svg'
import {
  getWebDocumentationCredentials,
  redirectToWebDocumentation,
} from '@/services/web-documentation-service'

export default {
  components: {
    SidebarButton,
  },
  mixins: [
    Vue.$mixins.userRightsMixin,
    externalAppsURLsMixin,
    Vue.$mixins.authMixin,
  ],
  data() {
    return {
      rootClassname: '',
      defaultColor: 'var(--color-sidebar-bg-hover)',
      localisationIcon,
      dashboardIcon,
      zonesIcon,
      citipavIcon,
      adminIcon,
      diagnosticsIcon,
      chartIcon,
      alertsIcon,
      questionIcon,
      signalIcon,
      ecoconduiteIcon,
      citifretIcon,
      calendarIcon,
      messagesIcon,
      containersIcon,
      blackspotsIcon,
      containersModuleIcon,
      citimissionIcon,
      citiboardIcon,
      editourIcon,
      accountIcon,
      settingsIcon,
      logoutIcon,
    }
  },
  computed: {
    ...mapGetters({
      isSidebarExpanded: 'sidebar/isSidebarExpanded',
      loginName: 'auth/loginName',
      canUseClientSelectionFeature: 'auth/canUseClientSelectionFeature',
    }),
    isDevice() {
      return this.$device.isMobileDevice() && this.$device.isTouchScreen()
    },
    currentUsername() {
      return `${this.loginName} (${this.$store.getters['auth/loginAs'].client})`
    },
  },
  mounted() {
    setTimeout(() => {
      this.rootClassname = 'visible'
    }, 1000)
  },
  methods: {
    onAccountLinkClick() {
      if (this.$store.getters['auth/canUseClientSelectionFeature']) {
        this.$routerPlugin.routeToLoginAs()
      }
    },
    /**
     * Change introduced by ticket #44739 : web documentation should now redirect to getOutline
     */
    async onHelpLinkClick() {
      try {
        const { url, token } = await getWebDocumentationCredentials()

        this.$nextTick(() => {
          redirectToWebDocumentation(url, token)
        })
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.links {
  display: flex;
  flex-direction: column;
  opacity: 0;
}
.visible {
  opacity: 1;
  transition: opacity 0.5s linear;
}
.sidebar__backdrop {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}
</style>
