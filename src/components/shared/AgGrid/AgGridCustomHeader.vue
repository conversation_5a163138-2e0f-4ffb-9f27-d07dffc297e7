<template>
  <div
    class="custom-header"
    :class="{ withSort: params.sortable && hasResults }"
    @click="toggleSort"
  >
    <div class="custom-label">
      <span class="label">{{ params.displayName }}</span>
      <!-- Tooltip -->
      <div v-if="params.headerTooltip" class="tooltip-icon">
        <em
          v-b-tooltip.hover.top
          class="fas fa-info-circle info-tooltip"
          :title="params.headerTooltip"
        ></em>
      </div>
    </div>
    <!--  Sorting  -->
    <div v-if="params.sortable && hasResults" class="custom-sort-icons">
      <i class="fas fa-sort-up" :class="{ active: currentSort === 'asc' }"></i>
      <i
        class="fas fa-sort-down"
        :class="{ active: currentSort === 'desc' }"
      ></i>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'AgGridCustomHeader',
  data() {
    return {
      currentSort: null,
    }
  },
  computed: {
    ...mapGetters({
      hasResults: 'operationalStatistics/hasResults',
    }),
  },
  mounted() {
    this.updateSortState()

    if (this.params?.column) {
      this.params.column.addEventListener('sortChanged', this.updateSortState)
    }
  },
  destroyed() {
    if (this.params?.column) {
      this.params.column.removeEventListener(
        'sortChanged',
        this.updateSortState
      )
    }
  },
  methods: {
    toggleSort() {
      if (!this.params?.column || !this.params?.setSort) return

      const newSort = this.currentSort === 'asc' ? 'desc' : 'asc'
      this.params.setSort(newSort, false)
    },

    updateSortState() {
      this.currentSort = this.params?.column?.getSort() || null
    },
  },
}
</script>

<style scoped>
.custom-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.custom-header.withSort {
  justify-content: space-between;
  cursor: pointer;
}
.custom-label {
  display: flex;
  align-items: center;
  justify-content: center;
}
.tooltip-icon {
  margin-left: 5px;
}
.custom-sort-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  i {
    font-size: 16px;
    height: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-main);
    opacity: 0.5;
  }
  i.active {
    opacity: 1;
  }

  i + i {
    margin-top: 2px;
  }
}
</style>
