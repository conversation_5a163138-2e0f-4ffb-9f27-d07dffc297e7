<template>
  <div class="grid-export-footer">
    <em
      v-b-tooltip.hover.top
      class="fas fa-calculator"
      :title="$t('operationalStatistics.exports.csvTooltip')"
      @click="exportCSV"
    ></em>
    <em
      v-b-tooltip.hover.top
      class="far fa-file-excel"
      :title="$t('operationalStatistics.exports.excelTooltip')"
      @click="exportExcel"
    ></em>
  </div>
</template>

<script>
import useOperationalStatisticsExporter from '@/composables/operationalStatistics/useOperationalStatisticsExporter'

/**
 * AG Grid Status Panel Component for exporting grid data
 * This component is designed to be used as a status panel in AG Grid
 * and receives grid APIs via the params prop from AG Grid
 */
export default {
  name: 'GridExportFooter',
  props: {
    params: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const { exportGridData } = useOperationalStatisticsExporter()

    const exportCSV = () => {
      if (window.vue && window.vue.$loader) {
        window.vue.$loader.showWhile(() => {
          exportGridData({
            gridApi: props.params.api,
            columnApi: props.params.columnApi,
            tableCode: props.params.tableCode || 'statsCircuit',
            format: 'csv',
          })
        })
      } else {
        exportGridData({
          gridApi: props.params.api,
          columnApi: props.params.columnApi,
          tableCode: props.params.tableCode || 'statsCircuit',
          format: 'csv',
        })
      }
    }

    const exportExcel = () => {
      if (window.vue && window.vue.$loader) {
        window.vue.$loader.showWhile(() => {
          exportGridData({
            gridApi: props.params.api,
            columnApi: props.params.columnApi,
            tableCode: props.params.tableCode || 'statsCircuit',
            format: 'xlsx',
          })
        })
      } else {
        exportGridData({
          gridApi: props.params.api,
          columnApi: props.params.columnApi,
          tableCode: props.params.tableCode || 'statsCircuit',
          format: 'xlsx',
        })
      }
    }

    return {
      exportCSV,
      exportExcel,
    }
  },
}
</script>

<style lang="scss" scoped>
.grid-export-footer {
  display: inline-flex;
  margin-right: 10px;
  box-shadow: 0px 2px 4px rgb(0 0 0 / 8%);
  margin-left: 10px;

  em {
    padding: 4px 10px;
    cursor: pointer;
    color: var(--color-dark-blue);

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}
</style>
