<template>
  <div
    :id="id"
    ref="root"
    class="datatable"
    style="position: relative"
    :class="{
      selectable: !!select,
      'loader-overlay': isDatatableLoadingValue,
    }"
    :data-name="name"
  >
    <div v-if="isDatatableLoadingValue" class="loader-wrapper">
      <b-spinner
        v-if="
          isDatatableLoadingShowSpinner &&
          isDatatableLoadingShowSpinner.value !== undefined
            ? isDatatableLoadingShowSpinner.value
            : isDatatableLoadingShowSpinner
        "
        class="loader"
        variant="info"
        style="width: 3rem; height: 3rem; margin: 0 auto; display: block"
      />
    </div>

    <table ref="table" class="dataTable__table display compact stripe" />

    <DataTablePaginator
      v-if="ssrPaging === true"
      ref="paginator"
      :page-length="pageLen"
      :name="name"
    />
  </div>
</template>
<script>
import { useProgresiveDatatableLoader } from '@c/shared/DataTable/DatatableLoader.vue'
import Vue from 'vue'
import $ from 'jquery'
import fr from '@/i18n/i18n.fr'
import { mapGetters } from 'vuex'
import DataTablePaginator from './DataTablePaginator.vue'
import * as R from 'ramda'

const tableProgressiveLoader = useProgresiveDatatableLoader()

const shouldLog =
  (getQueryStringValue('verbose') || '').includes('1') ||
  (getQueryStringValue('verbose') || '').includes('api')

import 'datatables.net'
import 'datatables.net-select-dt'
import '@/libs/datetime-moment'

const defaultLanguage = fr.datatable

const initDelay = 1000

var dynamicComponents = Vue.__DataTableDynamicComponents || {}
Vue.__DataTableDynamicComponents = dynamicComponents

var dataWatchFlags = {}
export function toggleDataWatch(name, shouldWatch = true) {
  dataWatchFlags[name] = shouldWatch
  console.debugVerbose(8, 'toggleDataWatch', name, shouldWatch, {
    dataWatchFlags,
  })
}

/**
 * Allow you to dynamically render a Vue component inside a Datatable cell
 *
 * Available properties:
 *  row Object
 *  datatable Function
 *  vueDatatable Object
 *
 * Usage:
 *  columnDefs: [
 *       {
 *         targets: 0,
 *         orderable: false,
 *         render: createComponentRender({
 *           name: "MagnifyingGlass",
 *           template: `
 *             <StatusIcon :row="row" ></StatusIcon>
 *           `,
 *         }),
 *       },
 * @namespace components
 * @category components
 * @subcategory shared/Datatable
 * @module DataTable
 */
export function createComponentRender(
  componentDefinition = {},
  extraOptions = {}
) {
  componentDefinition.props = {
    row: Object,
    datatable: Function,
    vueDatatable: Object,
  }
  dynamicComponents[componentDefinition.name] = componentDefinition

  return (data = '', type = '', row = {}) => {
    //Feat: createActionButtonDatatableColumn supports filterBy (func/string) to customize TextFilter behaviour
    if (extraOptions.filterBy && type === 'filter') {
      let val = ''
      if (
        typeof extraOptions.filterBy === 'string' &&
        row[extraOptions.filterBy] !== undefined
      ) {
        val = row[extraOptions.filterBy]
      }
      if (typeof extraOptions.filterBy === 'function') {
        val = extraOptions.filterBy(row)
      }

      return val

      //console.warn('Datatable.vue Invalid filterBy parameter (Expected string or function)')
    }

    let dataRow = ''
    try {
      dataRow = window.btoa(encodeURI(JSON.stringify(row)))
    } catch (err) {
      console.debugVerbose(8, 'datatable:fail_to_decode_row', {
        row,
      })
      throw err
    }
    return `<div data-needs-vue-rendering=1 data-component="${componentDefinition.name}" data-value="${data}"
            data-type="${type}"
            data-row="${dataRow}"
            ></div>`
  }
}

export function registerDatatableComponent(name, def = {}) {
  if (typeof def === 'function') {
    let dynamic = def
    def = {
      components: {
        [name + 'Inner']: dynamic,
      },
      template: `
      <${name + 'Inner'}/>
    `,
    }
  }
  def.name = name
  def.props = {
    row: Object,
    datatable: Function,
    vueDatatable: Object,
  }

  dynamicComponents[name] = def
}

/**
 * vueDataTableDrawOptions (Injectable)
 *
 * @typedef {Object} vueDataTableDrawOptions
 * @property {boolean} [batchDraw] True by default - Render mode that doesn't block UI.
 * @property {number} [batchSize] 500 by default - Batch size
 * @property {boolean} [showLoader] False by default - Display centered loader during rendering
 * @property {Object} [asyncRender]
 * @property {boolean} [asyncRender.progresiveLoader] Display small spinner in the bottom-right during rendering
 * @property {number} [asyncRender.batchSize] 500 by default - Batch size
 * @property {boolean} [asyncRender.drawOnce] False by default - Indicates Datatable jquery library to draw once after all batchs of items are set
 * @property {boolean} [disableQueueing] False by default - Disable logic that queue render if another render in progress
 * @property {number} [cooldownTimeoutMs] 300 by default - Timeout if render is queued due to rendering in progress. Increase to reduce renders count and increase performance. Unused if disableQueueing equals true.
 */

/**
 * Loaders:
 * There are three kind of loaders:
 * - Legacy loader: $loader.show/hide (Enable with vueDataTableDrawOptions.showLoader)
 *   (This will render in the screen center)
 * - Table loader (inject isDatatableLoading true/false)
 *   (This will render in the datatable DOM center)
 * - Progressive loader (See DatatableLoader.vue for usage)
 *   (This will render in the table bottom right)
 * @namespace components
 * @category components
 * @subcategory shared/datatable
 * @module DatatableComponent
 */
export default {
  components: {
    DataTablePaginator,
  },
  inject: {
    disableDatatableSetHeightFromParent: {
      default: null,
    },
    isDatatableLoading: {
      default: null, //{value: true/false}
    },
    isDatatableLoadingShowSpinner: {
      default: true,
    },
    vueDataTableBatchDrawHandler: {
      default: null,
    },
    /**
     * @type {vueDataTableDrawOptions}
     */
    vueDataTableDrawOptions: {
      default: {
        //showLoader:true Enable by default?
      },
    },
  },
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    rowId: {
      type: String,
      default: '',
    },
    columnDefs: {
      type: Array,
      default: () => [],
    },
    name: {
      type: String,
      required: true,
    },
    paging: {
      type: Boolean,
      default: true,
    },
    ssrPaging: {
      type: Boolean,
      default: false,
    },
    searching: {
      type: Boolean,
      default: true,
    },
    ssrColumnOrderingMiddleware: {
      type: Function,
      default: () => true,
    },
    language: {
      type: Object,
      default: () => ({}),
    },
    ssrSortingCooldownDuration: {
      type: Number,
      default: 0,
    },
    defaultSortingColumn: {
      type: Number,
      default: null,
    },
    defaultSortingColumnDirection: {
      type: String,
      default: 'asc',
    },
    scrollY: {
      type: [String, Boolean],
      default: false,
    },
    scroller: Boolean,
    ssrShowMultiColumnFilter: Boolean,
    select: {
      type: [Object, Boolean],
      default: false,
    },
    extraOptions: {
      type: Object,
      default: () => ({}),
    },
    autoHeight: {
      type: Boolean,
      default: false,
    },
    autoHeightOffset: {
      type: Number,
      default: 0,
    },
    autoHeightRefreshKey: {
      type: Number,
      default: 0,
    },
    initDelay: {
      type: Number,
      default: initDelay,
    },
    showPageLen: {
      type: Boolean,
      default: true,
    },
    //Set datatable directly (WIP) (Untested) (Use datatable store instead)
    dataset: {
      type: Array,
      default: () => [],
    },
    //Uses dataset property instead of datatable store (Use in combination with dataset)
    useDataset: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isRendering: false,
      isRenderingQueued: false,
      initializing: false,
      initialized: false,
      id: `datatable_${window.btoa(Math.random()).substring(0, 12)}_${
        this.name || 'no_name'
      }`,
      pageLength: this.extraOptions.pageLength || 10,
      datatable: null,
      ssrSortingCooldown: null,
    }
  },

  computed: {
    isDatatableLoadingValue() {
      return this.isDatatableLoading &&
        this.isDatatableLoading.value !== undefined
        ? this.isDatatableLoading.value
        : this.isDatatableLoading
    },
    ...mapGetters({
      getTableInfos: 'datatable/getTableInfos',
      getTableItems: 'datatable/getTableItems',
      appDynamicLayout: 'app/layout',
    }),
    currentPage() {
      return this.datatable && this.datatable.page()
    },
    pageLen() {
      return this.datatable && this.datatable.page.len()
    },
    data() {
      return this.getTableItems(this.name)
    },
    disablePaginationVisually: {
      get() {
        return this.$store.state.datatable.disablePaginationVisually
      },
      set(v) {
        this.$store.state.datatable.disablePaginationVisually = v
      },
    },
    resizeInProgress: {
      get() {
        return this.$store.state.app.resizeable.resizeInProgress
      },
      set(v) {
        this.$store.state.app.resizeable.resizeInProgress = v
      },
    },
    //Returns an array of the rows currently displayed
    currentRows() {
      return (
        this.datatable &&
        this.datatable.rows({ page: 'current' }).data().toArray()
      )
    },
  },
  watch: {
    resizeInProgress: {
      handler(newValue, oldValue) {
        if (newValue === false && oldValue === true) {
          this.$nextTick(() => this.draw())
        }
      },
      immediate: true,
      deep: true,
    },
    disablePaginationVisually: {
      handler(value) {
        console.debugVerbose(
          2,
          'datatable watch disablePaginationVisually',
          value
        )
        this.handleDisablePaginationVisually(value)
      },
      immediate: true,
    },
    /**
     * @todo: Ideally, this wrapper shouldn't know about the app layout. But app layout changes requires datatable to redraw-itself (column offset issue)
     */
    appDynamicLayout: {
      handler() {
        setTimeout(() => {
          this.$nextTick(() => {
            this.draw()
          })
        }, 600)
      },
      deep: true,
    },
    data: {
      handler(data) {
        if (dataWatchFlags[this.name] === false) {
          return //Optionally disable data watch
        }

        //console.log('Datatable.vue watch data ', data.length)

        if (this.useDataset) {
          return
        }

        this.$nextTick(() => {
          this.onDataChange(data, 'origin:data-watch')
        })
      },
      deep: true,
    },
    dataset: {
      handler(data) {
        if (this.useDataset) {
          this.onDataChange(data, 'origin:dataset-watch')
        }
      },
      immediate: true,
    },
    pageLength() {
      this.datatable && this.datatable.page.len(this.pageLength)
      this.$refs.paginator &&
        this.$refs.paginator.$emit('paginateToPage', {
          page: 1,
          itemsPerPage: this.pageLength,
        })
    },
    autoHeightRefreshKey() {
      if (this.autoHeight) {
        this.setHeightFromParent()
      }
    },
    //Store current rows
    currentRows: {
      handler(newValue) {
        this.$store.state.datatable.data.currentRows = newValue
      },
      immediate: true,
    },
    currentPage: {
      handler(newValue) {
        this.$store.state.datatable.currentPage = newValue
      },
      immediate: true,
    },
    pageLen: {
      handler(newValue) {
        this.$store.state.datatable.pageLength = newValue
      },
    },
  },
  mounted() {
    //console.log("datatable.mounted", this.name);
    this.cooldownSsrSorting()
    this.redrawOnWindowResizeBind()

    if (!this.useDataset) {
      //Initial data set from datatable store
      this.$nextTick(() => {
        this.onDataChange(this.data, 'origin:mounted')
      })
    }

    if (this.autoHeight) {
      $(window).resize(this.setHeightFromParent)
    }

    //Issue: Initialization might occurs before completely mounting the component (datatable rendering issue: horizontal scrollbar, columns, offets, etc)
    //Solution:Wait for initialized flag, then re-draw after one second
    this.waitForInitialized(() => {
      setTimeout(() => {
        try {
          this.datatable.draw()
          //console.log('Datatable.vue first draw (mounted)')
        } catch (err) {
          console.warn('Fail to draw', err)
        }
      }, 1000)
    })
  },
  beforeDestroy() {
    try {
      let tracker = console.trackTime(
        'table: destroy table instance',
        shouldLog
      )
      this.datatable.destroy()
      tracker.stop()
    } catch (err) {
      console.warn('Error while manually destroying table')
    }
  },
  destroyed() {
    $(window).off('resize', this.redraw)
    if (this.autoHeight) {
      $(window).off('resize', this.setHeightFromParent)
    }
  },
  methods: {
    /**
     * Will grey-out/disable pagination control visually
     *
     * Will try to set some css styles on the pagination dom element if available within 2000ms
     * @param {boolean} isDisabled
     */
    handleDisablePaginationVisually(isDisabled = false) {
      if (!this.initialized) {
        return setTimeout(() => {
          this.handleDisablePaginationVisually(isDisabled)
        }, 1000)
      } else {
        this.$nextTick(() => {
          //Wait to 2s for pagination render
          let start = Date.now()
          const checkAndToggle = () => {
            if (Date.now() - start > 2000) {
              return console.debugVerbose(
                2,
                'Disable pagination visually',
                isDisabled,
                'timeout'
              )
            }
            setTimeout(() => {
              const togglePaginationEnabled = (v) => {
                const el = document.querySelector(
                  `div[data-name='${this.name}'] .dataTables_paginate`
                )
                if (!el) {
                  return checkAndToggle()
                }
                el.style.pointerEvents = !v ? 'initial' : 'none'
                el.style.opacity = !v ? 1 : 0.5
                console.debugVerbose(
                  2,
                  'Disable pagination visually',
                  isDisabled
                )
              }
              togglePaginationEnabled(isDisabled)
            }, 250)
          }
          checkAndToggle()
        })
      }
    },
    waitForInitialized(cb) {
      const self = this //vm
      const waitObject = {
        called: false,
        timeoutId: null,
        cancelWait: function () {
          clearTimeout(this.timeoutId)
        },
      }

      function checkInitialized() {
        if (self.initialized) {
          if (!self._isDestroyed) {
            //console.log('Datatable.vue waitForInitialized call')
            cb()
            waitObject.called = true
          }
        } else {
          waitObject.timeoutId = setTimeout(checkInitialized, 200)
        }
      }

      checkInitialized()

      return waitObject
    },
    setHeightFromParent() {
      if (this.disableDatatableSetHeightFromParent === true) {
        return
      }

      if (
        this._isDestroyed ||
        !this.$refs.root ||
        !this.$refs.root.parentNode
      ) {
        return //Skip if destroyed
      }

      if (
        !!this.$refs.root &&
        $(this.$refs.root).find('.dataTables_scrollHead').length === 0
      ) {
        //console.log('setHeightFromParent::skip')
        return
      }
      const self = this

      let otherParentChildsHeight = $(this.$refs.root.parentNode)
        .children()
        .toArray()
        .filter(function (item) {
          return (
            $(item).attr('id') !== self.id && item.dataset.autoheight !== '0'
          )
        })
        .filter((item) => {
          return getComputedStyle(item).display !== 'none'
        })
        .map((item) => {
          return $(item).height()
        })
        .reduce((a, v) => a + v, 0)

      $(this.$refs.root).find('.dataTables_scrollBody').css('height', `${0}px`)

      let tableBaseHeight = $(this.$refs.root)
        .find('.dataTables_wrapper')
        .height()

      let maxHeight = $(this.$refs.root.parentNode).height()
      if ($(this.$refs.root.parentNode.parentNode).height() > maxHeight) {
        maxHeight = $(this.$refs.root.parentNode.parentNode).height()
      }

      let height =
        maxHeight -
        (otherParentChildsHeight +
          (this.autoHeightOffset || 0) +
          tableBaseHeight)
      $(this.$refs.root)
        .find('.dataTables_scrollBody')
        .css({
          height: `${height}px`,
          'max-height': `${height}px`,
        })
      /*console.log("setHeightFromParent::compute", {
        maxHeight,
        otherParentChildsHeight,
        autoHeightOffset: this.autoHeightOffset,
        tableBaseHeight,
        height,
      });*/
    },
    setOptions(options = {}) {
      $(`#${this.id} table`).DataTable({
        ...this.getOptions(),
        ...options,
        destroy: true,
      })
    },
    redrawOnWindowResizeBind() {
      this.redraw = () => {
        this.$nextTick(() => {
          this.draw()
        })
      }
      $(window).on('resize', this.redraw)
    },
    draw() {
      this.cooldownSsrSorting()
      this.$emit('draw', {
        name: this.name,
      })
      this.datatable.draw()
    },
    cooldownSsrSorting() {
      if (this.ssrPaging) {
        this.ssrSortingCooldown = Date.now()
      }
    },
    getDataTable() {
      return this.datatable
    },
    /**
     * Performs smart datatable (library) draw using data array.
     *
     * Mode: One time / Per batches (Default)
     * Sub-mode: batches sync/async (Large datasets)
     *
     * data comes from Datatable vuex store or prop (dataset)
     */
    async performTableDraw(debugOriginName = '') {
      if (this.isRendering) {
        this.isRenderingQueued = true
        return
      } else {
        this.isRendering = true
      }

      const data = this.useDataset ? this.dataset : this.data
      const self = this

      this.cooldownSsrSorting()

      //Configure defaults with vueDataTableDrawOptions prop (See ZonesTable.vue)
      const batchDraw =
        self.vueDataTableDrawOptions?.batchDraw === false ? false : true
      const defaultBatchSize = self.vueDataTableDrawOptions?.batchSize || 500
      const dataLength = data.length
      const defaultDrawTryAgainCooldownMs = 300

      //This will perform poorly on medium/large datasets (>2k)
      if (!batchDraw) {
        let trackDraw = console.trackTime('Datatable.vue draw', shouldLog)
        this.datatable.clear().rows.add(data || [])
        self.draw()
        trackDraw()
        return
      }

      const performSynchronousBatchRendering = () => {
        const track = console.trackTime(
          'datatable draw sync start batch size: ' + defaultBatchSize,
          shouldLog
        )
        this.datatable.rows().remove().draw() // Faster than this.datatable.clear() ?
        for (let i = 0; i < dataLength; i += defaultBatchSize) {
          const batch = data.slice(i, i + defaultBatchSize)
          this.datatable.rows.add(batch)
          self.draw()
        }
        track.stop()
      }

      /**
       * Performs better on large datasets (>5k)
       * @param {*} options
       * @param {Boolean} options.drawOnce Do not draw after each batch but instead draw once at the end.
       */
      const performAsynchronousBatchRendering = (options = {}) => {
        const batchSize = options.batchSize || defaultBatchSize
        const track = console.trackTime(
          'datatable draw async start batch size: ' + batchSize,
          shouldLog
        )

        return new Promise((resolve, reject) => {
          let currentIndex = 0
          const processNextBatch = () => {
            const batchEndIndex = Math.min(currentIndex + batchSize, dataLength)
            const batch = data.slice(currentIndex, batchEndIndex)
            currentIndex += batchSize
            self.datatable.rows.add(batch)
            if (currentIndex < dataLength) {
              if (self._isDestroyed) {
                return //Abort
              }
              if (options.drawOnce !== true) {
                self.datatable.draw()
              }

              setTimeout(processNextBatch, 0)
            } else {
              self.datatable.draw()
              track.stop()
              resolve()
            }
          }
          self.datatable.clear()
          processNextBatch()
        })
      }

      if (self.vueDataTableDrawOptions?.showLoader) {
        self.$loader.show()
      }

      const batchDrawTrack = console.trackTime(
        'Datatable.vue batch draw',
        shouldLog
      )

      //Feat: Customize table batch rendering
      if (self.vueDataTableBatchDrawHandler) {
        await self.vueDataTableBatchDrawHandler(data, this.datatable, () =>
          self.draw()
        )
        onDrawEnd()
      } else {
        if (self.vueDataTableDrawOptions?.asyncRender) {
          const disablePagination =
            self.vueDataTableDrawOptions?.asyncRender.disablePagination || false
          const showLoader =
            self.vueDataTableDrawOptions?.asyncRender.progresiveLoader
          if (showLoader) {
            tableProgressiveLoader.show('dt-draw')
            disablePagination && self.handleDisablePaginationVisually(true)
          }
          performAsynchronousBatchRendering(
            self.vueDataTableDrawOptions?.asyncRender
          ).finally(() => {
            if (showLoader) {
              tableProgressiveLoader.hide('dt-draw')
              disablePagination && self.handleDisablePaginationVisually(false)
            }
            onDrawEnd()
          })
        } else {
          tableProgressiveLoader.show('dt-draw')
          performSynchronousBatchRendering()
          tableProgressiveLoader.hide('dt-draw')
          onDrawEnd()
        }
      }

      function onDrawEnd() {
        batchDrawTrack.stop()

        //Hides middle screen loader
        if (self.vueDataTableDrawOptions?.showLoader) {
          self.$loader.hide()
        }

        self.isRendering = false

        self.$emit('change', self.name)

        //Optionally trigger a queued rendering after a cooldown period
        //Will try to render again within 30s
        if (
          self.isRenderingQueued &&
          self.vueDataTableDrawOptions?.disableQueueing !== true
        ) {
          const timeout =
            self.vueDataTableDrawOptions?.cooldownTimeoutMs ||
            defaultDrawTryAgainCooldownMs
          self.isRenderingQueued = false
          const tryAgainStartAt = Date.now()
          tryAgain()
          function tryAgain() {
            clearTimeout(self.drawQueuedTimeout)
            self.drawQueuedTimeout = setTimeout(() => {
              if (!self.isRendering) {
                console.log('performTableDraw after cooldown')
                self.performTableDraw(debugOriginName)
              } else {
                if (Date.now() - tryAgainStartAt > 30000) {
                  console.warn('DataTable.vue draw try again timeout')
                  return
                } else {
                  tryAgain()
                }
              }
            }, timeout)
          }
        }
      }
    },
    onDataChange(data, debugOriginName = '') {
      //console.log('onDataChange', data.length)

      if (this.datatable === null) {
        this.initializeDataTable() //Lazy load init
      }
      this.waitForInitialized(() => {
        this.$nextTick(() => {
          this.performTableDraw(debugOriginName)
        })
      })
    },
    getColumns() {
      if (this.columns) {
        return this.columns
      }
      if (this.labels) {
        return Object.keys(this.labels)
      }
    },
    getOptions() {
      let record = this.useDataset ? this.dataset[0] : this.data[0]
      let columns = this.getColumns() || Object.keys(record)

      //Warning: defaultLanguage is undefined (new i18n object is a plain object instead of a nested object). See i18n/index.js
      let language = {
        ...defaultLanguage,
        ...(this.language || {}),
      }
      delete language.emptyTable

      return {
        dom: 'lftipr',
        ordering: true,
        autoWidth: true,
        language,
        scrollX: true,
        searching: this.searching === true && !this.ssrPaging,
        scrollY: this.scrollY !== undefined ? this.scrollY : false,
        scroller: this.scroller !== undefined ? this.scroller : false,
        deferRender: window.innerHeight > 900 ? true : false,
        paging: this.paging === true && !this.ssrPaging,
        lengthChange: true,
        info: false,
        ...((this.select && { select: this.select }) || {}),
        rowId: this.rowId || 'id',
        columnDefs: (this.columnDefs && R.clone(this.columnDefs)) || undefined,
        data: this.useDataset ? this.dataset : this.data,
        columns: columns.map((c) => {
          const capitalize = (text, defaultValue = '') =>
            text
              ? text.charAt(0).toUpperCase() + text.substring(1)
              : defaultValue
          const getValue = (obj, key, defaultValue) =>
            obj[key] !== undefined ? obj[key] : defaultValue
          const isObj = () => typeof c === 'object'

          if (!isObj() && c.indexOf('::') !== -1) {
            c = {
              data: c.split('::')[0],
              title: this.$t(c.split('::')[1]),
            }
          }

          c = {
            data: typeof c === 'string' ? c : undefined,
            ...(isObj() ? c : {}),
            title:
              this.labels && this.labels[c] !== undefined
                ? this.labels[c]
                : (!isObj() && capitalize(c)) ||
                  (isObj() && getValue(c, 'title', getValue(c, 'label'))),
          }
          return c
        }),
        ...(this.extraOptions || {}),
        initComplete: () => {
          //console.log('Datatable.vue initComplete')
          this.onDraw()
        },
      }
    },
    /**
     * @warn This doesn't work well with column filters
     */
    toggleColumnVisible(index, value) {
      if (this.datatable) {
        this.datatable.column(index).visible(value)
        this.datatable.columns.adjust().draw(false)
      }
    },
    initializeDataTable(times = 0) {
      //console.log('Datatable.vue Initializing')

      const cssSelector = `#${this.id} table`

      if ($(cssSelector).length === 0) {
        if (times > 10) {
          throw new Error('Failed to initialize datatable')
        }
        setTimeout(() => {
          this.initializeDataTable(times++)
        }, 1000)
        return false
      }

      if (this.initializing) {
        return
      } else {
        this.initializing = true
      }

      let self = this

      $.fn.dataTable && $.fn.dataTable.moment(this.$date.getDatePattern())

      if (!$.fn.dataTable) {
        console.warn('Datatable::moment plugin not available')
      }

      const tableOptions = this.getOptions()

      let tracker = console.trackTime('table: datatable-init', shouldLog)

      tracker.count('wrapperLen', $(`#${this.id} table`).length)

      /*console.log('initialize', {
        el: $(`#${this.id} table`),
        tableOptions,
      })*/

      window._dt = this.datatable = $(cssSelector).DataTable(tableOptions)
      tracker.stop()

      this.datatable.on('draw.dt', (e) => {
        e.stopPropagation()
        //let wrapperId = $(e.target).closest(`[data-name="${this.name}"]`).get(0).id;
        //console.log("datatable.on.draw", this.name);

        this.onDraw()
      })

      this.datatable.on('page', async (e) => {
        const itemsInTable = this.$refs.table.querySelectorAll('tbody tr')

        if (itemsInTable && itemsInTable.length > 0) {
          itemsInTable[0].scrollIntoView()
        }
      })

      this.datatable.on('order.dt', async (e) => {
        e.stopPropagation()
        //let wrapperId = $(e.target).closest(`[data-name="${this.name}"]`).get(0).id;

        //console.log("datatable.on.order", this.name);

        if (this.ssrPaging) {
          if (
            this.ssrSortingCooldown != null &&
            Date.now() - this.ssrSortingCooldown <
              (this.ssrSortingCooldownDuration || 1000)
          ) {
            console.log('table: sorting update skip (ssr)')
            return
          } else {
            this.ssrSortingCooldown = Date.now()
          }

          let fetchOptions = {
            name: this.name,
            sort: this.datatable.order()[0],
          }
          if (
            this.ssrColumnOrderingMiddleware &&
            !this.ssrColumnOrderingMiddleware(fetchOptions)
          ) {
            console.log('table: Skip column ordering (SSR)')
            return
          } else {
            console.log('table: Sorting...')
          }

          this.$loader && this.$loader.show()
          await this.$store.dispatch('datatable/fetchTableItems', fetchOptions)

          this.$loader && this.$loader.hide()
        }

        this.$emit('order', {
          name: this.name,
        })
      })

      this.datatable.on('select.dt', function (e, dt, type, indexes) {
        let payload = {
          e,
          dt,
          type,
          indexes,
          ...(indexes.length >= 1 ? { item: dt.row(indexes[0]).data() } : {}),
        }
        self.$emit('select', payload)
        self.lastSelectedItem = payload.item || null
      })
      this.datatable.on('deselect.dt', function (e, dt, type, indexes) {
        self.$emit('deselect', {
          e,
          dt,
          type,
          indexes,
          item: self.lastSelectedItem || null,
        })
      })

      if (this.defaultSortingColumn) {
        tracker = console.trackTime('table: first-draw(sort)', shouldLog)
        this.datatable
          .column(this.defaultSortingColumn)
          .order(this.defaultSortingColumnDirection || 'asc')
        this.draw()
        tracker.stop()
      }

      setTimeout(() => {
        self.$emit('init', {
          name: this.name,
        })

        if (this.autoHeight) {
          this.setHeightFromParent()
        }

        //console.log('init finish')
      }, this.initDelay)

      this.initialized = true
    },
    onDraw() {
      let rendeables =
        (!!this.$refs.root &&
          Array.prototype.slice.call(
            this.$refs.root.querySelectorAll('[data-needs-vue-rendering]'),
            0
          )) ||
        []
      rendeables.forEach((el) => {
        let ComponentClass = Vue.extend(dynamicComponents[el.dataset.component])
        let row = JSON.parse(decodeURI(atob(el.dataset.row || btoa('{}'))))
        let instance = new ComponentClass({
          parent: this,
          propsData: {
            row,
            datatable: () => this.datatable,
            vueDatatable: this,
          },
        })
        instance.$mount()
        let parentNode = el.parentNode
        let index = Array.from(parentNode.children).indexOf(el)
        parentNode.removeChild(el)
        if (index == Array.from(parentNode.children).length - 1) {
          parentNode.appendChild(instance.$el)
        } else {
          parentNode.insertBefore(
            instance.$el,
            Array.from(parentNode.children)[index + 1]
          )
        }
      })
      if (this.autoHeight) {
        this.setHeightFromParent()
      }
    },
    getInnerHTMLFromVueRenderable: function (htmlRenderable) {
      let el = $(htmlRenderable).get(0)
      let ComponentClass = Vue.extend(dynamicComponents[el.dataset.component])
      let row = JSON.parse(decodeURI(atob(el.dataset.row || btoa('{}'))))
      let instance = new ComponentClass({
        parent: this,
        propsData: {
          row,
          datatable: () => this.datatable,
          vueDatatable: this,
        },
      })
      instance.$mount()
      return instance.$el.outerHTML
    },
  },
}
</script>
<style lang="scss">
.datatable th {
  color: #014470 !important;
  font-size: 12px;
}
.datatable td {
  color: #524d4d;
  font-size: 12px;
}
.dataTable__table {
  width: 100% !important;
}
.dataTable_cmp__rows_select {
  margin: 0px 5px;
}
.datatable_toolbar {
  display: grid;
  grid-template-columns: 50% 50%;
}
.datatable_filter {
  justify-self: end;
}
.datatable.selectable tr:hover {
  cursor: pointer !important;
}
.loader-wrapper {
  position: absolute;
  width: 100%;
  top: 45%;
  z-index: 10;
}

.loader-overlay > div:not(.loader-wrapper) {
  opacity: 0.1;
}
</style>
