/* eslint-disable */

/**
 * @module routes
 * @category Router
 */
import envService from '@/services/env-service.js'
import {
  areModulesWhitelisted,
  getWhitelistedModules,
} from '@/services/modules-service.js'
import blackspotRoutes from './modules/blackspot-routes'
import containersModuleRoutes from './modules/containers-module-routes'

/**
 * @todo Add functionId if missing (To enable route auditing)
 */
let routes = [
  ...(envService.isProduction() &&
  !envService.isIsoprod() &&
  !envService.isBeta()
    ? []
    : [
        {
          path: '/test',
          name: 'test',
          component: () =>
            import(/* webpackChunkName "main" */ '@/views/Test.vue'),
          props: {},
        },
      ]),
  {
    path: '/login',
    name: 'login_screen',
    component: () =>
      import(/* webpackChunkName: "main" */ '../views/LoginModule.vue'),
  },
  {
    path: '/login-client',
    alias: ['/login-as', '/select-client'],
    name: 'login_as',
    component: () =>
      import(/* webpackChunkName: "main" */ '../views/LoginAs.vue'),
  },
  {
    path: '/menu',
    name: 'app_quickmenu_screen',
    meta: {
      requiredRights: ['GEOV3_QUICKMENU_ACCESSS'],
      allowRedirect: false,
    },
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/QuickMenuModule/QuickMenuModule.vue'
      ),
  },
  {
    path: '/ecoconduite',
    name: 'ecoconduite_module',
    meta: {
      requiredRights: ['GEOV3_ECOCONDUITE_ACCESS'],
      weight: 2,
      functionId: 369,
    },
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/EcoConduiteModule.vue'
      ),
    children: [
      {
        path: '/ecoconduite/bloc',
        name: 'ecoconduiteBloc',
        meta: {
          requiredRights: ['GEOV3_ECOCONDUITE_ACCESS'],
        },
        component: () => import('@desktop/EcoConduite/EcoConduiteDataBloc.vue'),
      },
      {
        path: '/ecoconduite/table',
        name: 'ecoconduiteTable',
        meta: {
          requiredRights: ['GEOV3_ECOCONDUITE_ACCESS'],
        },
        component: () =>
          import('@desktop/EcoConduite/EcoConduiteDataTable.vue'),
      },
      {
        path: '/ecoconduite',
        name: 'ecoconduite_module',
        meta: {
          requiredRights: ['GEOV3_ECOCONDUITE_ACCESS'],
        },
        component: () => import('@desktop/EcoConduite/EcoConduiteDataBloc.vue'),
      },
    ],
  },
  {
    path: '/cartography',
    name: 'events_module',
    meta: {
      requiredRights: ['GEOV3_EVENTS_ACCESS'],
      functionId: 365,
      searchTemplateFunctionId: 365,
    },
    component: () =>
      import(
        /* webpackChunkName: "events_module" */
        '../views/EventsModule.vue'
      ),
  },
  {
    path: '/diagnostics',
    name: 'diagnostics_module',
    meta: {
      functionId: 368,
      requiredRights: ['GEOV3_DIAGNOSTIC_ACCESS'],
    },
    props: (route) => ({ searchParams: route.params.searchParams || null }),
    component: () =>
      import(
        /* webpackChunkName: "diagnostics_module" */
        '../views/DiagnosticsModule.vue'
      ),
  },
  {
    path: '/zones',
    name: 'zones_module',
    meta: {
      functionId: 359,
      requiredRights: ['GEOV3_ZONES_ACCESS'],
    },
    component: () =>
      import(/* webpackChunkName: "zones_module" */ '../views/ZonesModule.vue'),
  },
  /*{
    path: '/circuit',
    name: 'circuit_module',
    meta: {
      requiredRights: ['GEOV3_CIRCUIT_ACCESS'],
    },
    component: () =>
      import(
        
        '../views/CircuitModule.vue'
      ),
  },
  {
    path: '/objects',
    name: 'objects_module',
    meta: {
      requiredRights: ['GEOV3_OBJECTS_ACCESS'],
    },
    component: () =>
      import(
        
        '../views/ObjectsModule.vue'
      ),
  },*/
  {
    path: '/dashboard',
    name: 'dashboard_module',
    meta: {
      requiredRights: ['GEOV3_DASHBOARD_ACCESS'],
    },
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/DashboardModule.vue'
      ),
  },
  {
    path: '/identification',
    alias: '/sensors',
    name: 'sensors_module',
    meta: {
      requiredRights: ['GEOV3_IDENTIFICATION_ACCESS'],
      functionId: 364,
      searchTemplateFunctionId: 364,
    },
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/SensorsModule.vue'
      ),
  },
  {
    path: '/alerts',
    name: 'alerts_module',
    meta: {
      requiredRights: ['GEOV3_ALERTS_ACCESS'],
      weight: 3,
      functionId: 389,
      searchTemplateFunctionId: 389,
    },
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/AlertsModule.vue'
      ),
  },
  {
    path: '/statsmodule',
    name: 'statsmodule',
    component: () => import(/* webpackChunkName: "stats_module" */ '../views/StatsModule.vue'),
    meta: {
      requiresAuth: true,
      requiredRights: [], //'GEOV3_OPERATIONAL_STATISTICS_ACCESS'
      // functionId: XXX, // TODO: Assign a functionId if required for auditing
      // weight: Y, // TODO: Assign a weight for sidebar ordering if needed
    },
  },
  {
    path: '/loginjwt',
    name: 'loginjwt',
    meta: {},
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/LoginJWT.vue'
      ),
  },
  {
    path: '/resetPassword',
    alias: '/resetPassword',
    name: 'resetPassword',
    meta: {},
    component: () => import('../views/ResetPassword.vue'),
  },
  ...[blackspotRoutes],
  ...[containersModuleRoutes],
  {
    path: '/messages',
    name: 'messages_module',
    meta: {
      functionId: 408,
    },
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/MessagesModule.vue'
      ),
  },
  {
    path: '/containers',
    name: 'containers_management',
    meta: {
      functionId: 416,
      description: 'Containers management / Gestion des conteneurs',
    },
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/ContainersManagementModule.vue'
      ),
  },
  {
    path: '/reload_screen',
    name: 'reload_screen',
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/ReloadView.vue'
      ),
  },
  {
    path: '/',
    name: 'location_module',
    meta: {
      requiredRights: ['GEOV3_LOCALISATION_ACCESS'],
      weight: 1,
      //functionId: 356,//Manual track instead
      storeName: 'location_module',
      moduleName: 'location_module',
      searchTemplateFunctionId: 356,
    },
    props: (route) => ({ searchParams: route.params.searchParams || null }),
    component: () =>
      import(
        /* webpackChunkName: "main" */
        '../views/LocationModule.vue'
      ),
  },
]

//If whitelist modules env is provided and match at least one module, add only whitelisted modules:
console.debugVerbose(8, 'Routes whitelist mode?', areModulesWhitelisted())
if (areModulesWhitelisted()) {
  let whitelistedModules = getWhitelistedModules()

  const isRouteWhitelisted = (r) =>
    whitelistedModules.includes(r.name) ||
    (r?.meta?.moduleName && whitelistedModules.includes(r?.meta?.moduleName))

  if (routes.filter((r) => isRouteWhitelisted(r)).length > 0) {
    routes = routes.filter((r) => {
      return (
        ['login_screen', 'reload_screen', 'loginjwt', 'resetPassword'].includes(
          r.name
        ) || isRouteWhitelisted(r)
      )
    })
    console.debugVerbose(8, 'Routes has been whitelisted', whitelistedModules)
  } else {
    console.debugVerbose(8, 'Routes whitelist skip')
  }
} else {
  console.debugVerbose(8, 'Routes whitelist skip', {
    routes,
  })
}

routes.push({
  path: '*',
  redirect: '/',
})

/**
 * List of routes
 */
export default routes
