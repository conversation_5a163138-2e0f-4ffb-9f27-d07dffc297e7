import {
  getZones,
  saveZone,
  getZoneCategories,
} from '@/services/zones-service.js'
import L from 'leaflet'
import * as R from 'ramda'
import api from '@/api'
import i18n from '@/i18n'
import { queueOperationOnce } from '@/utils/promise.js'
import { apiCacheStorage } from '@/api/api-cache.js'
import { geocodingPlugin } from '@/plugins/vue-services'
import { getQueryStringValue } from '@/utils/querystring'
import { mitt } from '@/plugins/mitt.js'
import useToastComposable from '@/composables/toast.ts'
import { getBoundsFromPolygon, getLayerAndMapBoundsMatch } from '@/utils/geo'
import store from '@/store'

let showToastInternal = null

export function showToast(text, type = 'info', title = '', timeout = 2000) {
  return showToastInternal({
    type,
    text,
    title: title,
    timeout,
  })
}

const logging =
  (getQueryStringValue('verbose') || '').includes('1') ||
  (getQueryStringValue('verbose') || '').includes('zones')

export const zoneTypeIcons = {
  drapeau_bleu: `drapeau_bleu_32x32.gif`,
  drapeau_orange: `drapeau_orange_32x32.gif`,
  drapeau_rouge: `drapeau_rouge_32x32.gif`,
  drapeau_vert: `drapeau_vert_32x32.gif`,
  icone_clientbleu: `icone_clientbleu_16x16.gif`,
  icone_clientrouge: `icone_clientrouge_16x16.gif`,
  icone_clientvert: `icone_clientvert_16x16.gif`,
  icone_depot: `icone_depot_16x16.gif`,
  icone_entrepot: `icone_entrepot_16x16.gif`,
  icone_fournisseur: `icone_fournisseur_16x16.gif`,
  icone_garage: `icone_garage_16x16.gif`,
  icone_interdit: `icone_interdit_16x16.gif`,
  icone_livraison: `icone_livraison_16x16.gif`,
  icone_maison: `icone_maison_16x16.gif`,
}

export const EVENT_ZONE_MODULE_ZONE_LAYER_HIGHLIGHT =
  'zone_module__zone_layer__click'

/**
 *
 * @param {*} iconKey e.g drapeau_bleu
 * @returns
 */
export function normalizeZoneTypeIconItem(iconKey = '') {
  return (
    (!!iconKey && {
      id: iconKey,
      name: i18n.t(`zones.zone_type_icons_labels.${iconKey}`),
      iconSrc: `/img/zones/${zoneTypeIcons[iconKey]}`,
      iconFileName: zoneTypeIcons[iconKey],
    }) ||
    null
  )
}

const globalLeafletGeomanOptions = {
  continueDrawing: false,
  markerStyle: {
    icon: L.divIcon({
      className: 'zone_marker__access_point_icon',
      html: `<svg xmlns="http: //www. W3. Org/2000/svg" width="18.385" height="18.385" viewbox="0 0 18.385 18.385" > <g class="a" transform="translate(9.192) rotate(45)" : fill="var(--color-dark-blue)" > <rect class="b" style="stroke: none" stroke="none" width="13" height="13" rx="2" /> <rect class="c" style="fill: none" fill="none" x="0.5" y="0.5" width="12" height="12" rx="1.5" /> </g> </svg>`,
    }),
  },
}

function configureGeomanTranslations(leafletMapInstance) {
  const customTranslation = {
    buttonTitles: {
      drawMarkerButton: i18n.t('zones.map.buttons.set_access_point'),
      drawPolyButton: i18n.t('zones.map.buttons.draw_polygon'),
      drawCircleButton: i18n.t('zones.map.buttons.draw_circle'),
      editButton: i18n.t('zones.map.buttons.edit_layers'),
      dragButton: i18n.t('zones.map.buttons.move_layers'),
      deleteButton: i18n.t('zones.map.buttons.remove_layers'),
      rotateButton: i18n.t('zones.map.buttons.rotate_layers'),
    },
  }

  leafletMapInstance.pm.setLang('customName', customTranslation, i18n.locale)
}

export function useZoneModuleProvider({ store: providedStore }) {
  if (!providedStore) {
    console.warn('Please initialize useZoneModuleProvider with {store}')
  }

  const { showToast: showToastInternalFn } = useToastComposable({
    store,
    i18n,
  })
  showToastInternal = showToastInternalFn

  const getDefaultZoneItem = () => ({
    name: '',
    isAnalysis: false,
    isInternal: false,
    isShared: false,
    isSharedEdition: false,
  })

  let lastEnableClustering = null

  const state = reactive({
    isMounted: false,
    zoneTypesList: [],
    zonesList: [],
    zoneCategoriesList: [],
    mapFilteredTypes: [],

    simplicitiMapVM: ref(null),
    mapVM: ref(null),

    targetZoneItem: null, //Zone being zoomed

    isMapConfigured: false,
    stateFlags: [],
    geomanControls: false,

    selectedTabName: '',

    currentZone: getDefaultZoneItem(),
    currentZoneType: {},
    leafletZoneLayer: null,
    leafletZoneAccessLayer: null,
    selectedZone: {},
  })

  const updateStateLeafletLayersTimeout = ref(null)
  const interactionMode = ref('view') //view / edit / create

  window._zms = state

  watchEffect(watchEffectHandlerForLeafletGeomanToolbarToogle)

  //These will configure the map interactions and update the zone markers
  watch(() => state.mapVM, watchLog('mapVM', updateMapLayersHandler))
  watch(() => state.zonesList, watchLog('zonesList', updateMapLayersHandler), {
    deep: true,
  })
  watch(
    () => state.mapFilteredTypes,
    watchLog('mapFilteredTypes', updateMapLayersHandler),
    {
      deep: true,
    }
  )

  watch(interactionMode, () => {
    state.mapVM.clearLayers('zones')

    if (state.mapVM) {
      let pm = state.mapVM?.map?.pm
      if (pm) {
        pm.disableDraw() //Disable drawing mode when user switch from/to view/edit/create
      }
    }

    if (['edit', 'create'].includes(interactionMode.value)) {
      store.state.simpliciti_map.mapReverseGeocodingOnMouseClickEnabled = false

      //Issue: A single leaflet layer group do not support clustering a non-clustering mode at the same time
      //Solution 1: If clustering is enabled, zoom is restricted to avoid rendering other layers in clustering mode (state.mapVM.map.setMinZoom(16) )
      ///state.mapVM.map.setMinZoom(16)
      //Solution 2: Render clustering and non-clustering layers in different layer groups
      //Solution 3: Remove clustering so that there is not zoom restriction (Active) (state.mapVM.map.setMinZoom(null))
      state.mapVM.map.setMinZoom(null)
    } else {
      state.mapVM.map.setMinZoom(null)
      store.state.simpliciti_map.mapReverseGeocodingOnMouseClickEnabled = true
    }

    /*if ('edit' === interactionMode.value) {
      updateStateLeafletLayers()
    }*/

    updateMapLayersHandler()
    setTimeout(() => {
      updateMapLayersHandler()
    }, 1000)
  })

  watch(
    () => state.simplicitiMapVM?.zoomLevel,
    watchLog('zoomLevel', updateMapLayersHandler)
  )

  watch(
    () => state.simplicitiMapVM?.moveendTimestamp,
    watchLog('moveendTimestamp', updateMapLayersHandler)
  )

  function watchLog(message, handler) {
    return () => {
      /*if (!state.isMounted) {
        return logging && console.log('zm::watch-skip-unmounted::' + message)
      }*/

      logging && console.log('zm::watch::' + message)
      handler()
    }
  }

  const scope = {
    state,
    zoneTypeIcons,
    interactionMode,
    //Getters
    isProcessing: computed(
      () =>
        !![
          'savingZone',
          'zonesListLoading',
          'zoneTypesListLoading',
          'zonesCategoriesLoading',
          'blinkingZone',
        ].find((name) => state.stateFlags.includes(name))
    ),
    hasMutationInProgress: computed(() =>
      state.stateFlags.includes('savingZone')
    ),
    isZonesListLoading: computed(() =>
      state.stateFlags.includes('zonesListLoading')
    ),
    isZoneTypesListLoading: computed(() =>
      state.stateFlags.includes('zoneTypesListLoading')
    ),
    isZoneCategoriesListLoading: computed(() =>
      state.stateFlags.includes('zonesCategoriesLoading')
    ),

    //Actions,
    mapZoneItemFitBounds,

    /**
     * @param {*} item
     */
    async saveZone(item) {
      const stateFlag = addStateFlag('savingZone')
      try {
        let newZone = await saveZone({ ...item })
        scope.resetCurrentZoneItem()
        scope.updateZoneInsideLocalZonesList(newZone)
        stateFlag.clear()
        scope.refreshMapLayers()

        apiCacheStorage.invalidateCacheByKeyInclude('area_areas').then(() => {
          getZones().then(() => {
            logging && console.log('zones cache updated successfully')
          })
        })
      } catch (err) {
        stateFlag.clear()
        throw err
      }
    },
    resetCurrentZoneItem() {
      state.currentZone = getDefaultZoneItem()

      if (state.leafletZoneLayer) {
        state.leafletZoneLayer.remove()
        state.leafletZoneLayer = null
      }
      if (state.leafletZoneAccessLayer) {
        state.leafletZoneAccessLayer.remove()
        state.leafletZoneAccessLayer = null
      }
    },
    refreshMapLayers: updateMapLayersHandler,
    updateZoneInsideLocalZonesList(zoneItem) {
      let others = state.zonesList.filter((item) => item.id !== zoneItem.id)
      state.zonesList = [...others, Object.freeze(zoneItem)]
    },
    async updateZones() {
      logging && console.log('updateZones')
      let firstCallbackCalled = false
      const stateFlag = addStateFlag('zonesListLoading')
      state.zonesList.length = 0
      state.zonesList = await getZones({
        callback(items) {
          if (firstCallbackCalled) {
            return //We only grab the first pack of data (500) and then we wait for the final response.
          }
          firstCallbackCalled = true
          state.zonesList = [...state.zonesList, ...items]
          logging && console.log('Progresively loading the zone list')
        },
      })
      stateFlag.clear()
    },
    updateZoneTypes: async (bypassCache = false) => {
      const stateFlag = addStateFlag('zoneTypesListLoading')

      if (bypassCache) {
        await apiCacheStorage.invalidateCacheByKeyInclude('area_types')
      }

      //state.zoneTypesList = await getZoneTypes()
      state.zoneTypesList.length = 0
      state.zoneTypesList = await api.zoneType.getAllPooling({
        sort: (a, b) => (a.name < b.name ? -1 : 1),
      })
      stateFlag.clear()

      state.mapFilteredTypes = [
        ...state.zoneTypesList.map((o) => Object.freeze(o)),
      ]
    },
    updateZoneCategories: async () => {
      const stateFlag = addStateFlag('zonesCategoriesLoading')
      state.zoneCategoriesList = await getZoneCategories()
      stateFlag.clear()
    },
    editZoneItem,
    updateStateLeafletLayers,
  }
  window._zmss = scope
  return scope

  function editZoneItem(zoneItem) {
    state.currentZone = { ...zoneItem }
    interactionMode.value = 'edit'
    state.mapVM.map.setView([zoneItem.lat, zoneItem.lng], 18)
  }

  function watchEffectHandlerForLeafletGeomanToolbarToogle() {
    logging && console.log('watch::add-or-remove-map-toolbar')

    let previousState = state.geomanControls

    if (
      ['edit', 'create'].includes(interactionMode.value) &&
      state.mapVM &&
      !state.geomanControls &&
      state.selectedTabName === 'zones'
    ) {
      state.geomanControls = true
    } else {
      state.geomanControls = false
    }

    if (interactionMode.value === 'view' && state.geomanControls) {
      state.geomanControls = false
    }

    if (previousState !== state.geomanControls) {
      if (state.geomanControls) {
        state.mapVM.map.pm.addControls({
          position: 'topleft',
          drawCircle: true,
          drawPolygon: true,
          drawPolyline: false,
          drawCircleMarker: false,
          drawText: false,
          cutPolygon: false,
          drawRectangle: false,
        })
      } else {
        state.mapVM.map.pm.removeControls()
      }
    }
  }

  /**
   * Removes the leaflet layer from the zones layer group (only if the layer group exists)
   * @param {*} layer
   */
  function removeLayerSafe(layer) {
    if (state.mapVM.layerGroups.zones) {
      state.mapVM.layerGroups.zones.removeLayer(layer)
    }
  }

  /**
   *
   * @param {*} e Leaflet Geoman event
   */
  async function onLeafletGeomanLayerCreated(e) {
    logging && console.log('pm:create')
    let newLayer = e.layer
    let isZoneLayer = ['Circle', 'Polygon'].includes(e.shape)
    let isZoneAccessLayer = ['Marker'].includes(e.shape)
    newLayer.options.pmIgnore = false
    Object.keys(state.currentZone || {}).forEach(
      (key) => (newLayer.options[key] = state.currentZone[key])
    )

    if (isZoneLayer) {
      newLayer.options.isZoneGeometry = true
      if (state.leafletZoneLayer) {
        state.leafletZoneLayer.remove()
        removeLayerSafe(state.leafletZoneLayer)
      }
      if (state.currentZone) {
        newLayer.externalId = state.currentZone.id
      }
      state.leafletZoneLayer = newLayer
    }

    if (isZoneAccessLayer) {
      //Validate: If no address found via reverse geocoding, cancel

      let reversed = await geocodingPlugin.reverseGeocoding({
        latitude: newLayer.getLatLng().lat,
        longitude: newLayer.getLatLng().lng,
      })
      if (!reversed.city) {
        newLayer.remove()
        showToast('zones.draw_access_layer_reverse_geocoding_fail', 'warning')
        return
      }

      if (state.leafletZoneAccessLayer) {
        state.leafletZoneAccessLayer.remove()
        removeLayerSafe(state.leafletZoneAccessLayer)
      }
      state.leafletZoneAccessLayer = newLayer
    }

    L.PM.reInitLayer(newLayer)
  }

  async function mapZoneItemFitBounds(zoneItem) {
    if (!state.mapVM) {
      return
    }
    const stateFlag = addStateFlag('blinkingZone')
    try {
      state.targetZoneItem = zoneItem

      //Compute zone bounds from polygon
      const zoneBounds = getBoundsFromPolygon(zoneItem.polygon)

      //Fallback to standard max zoom level if !zoneBounds
      if (!zoneBounds) {
        state.mapVM.map.flyTo([zoneItem.lat, zoneItem.lng], 18)
      } else {
        //Compute optimal zoom level from zoneBounds
        const optimalZoomLevel = state.mapVM.map.getBoundsZoom(zoneBounds)

        state.mapVM.map.flyTo([zoneItem.lat, zoneItem.lng], optimalZoomLevel)
      }

      await blinkLeafletLayerIfFound()
    } finally {
      stateFlag.clear()
    }

    function blinkLeafletLayerIfFound() {
      return new Promise((resolve, reject) => {
        //Also try to grab the zone layer and make it blink
        try {
          // @ts-expect-error
          let layer = state.mapVM.layerGroups.zones
            .getLayers()
            .find((l) => l.externalId === zoneItem.id)
          if (layer) {
            //console.log('blink success')
            let fillColor = layer.options.fillColor
            layer.setStyle({
              fillColor: 'blue',
            })
            setTimeout(() => {
              layer.setStyle({
                fillColor: fillColor,
              })
              resolve()
            }, 400)
          } else {
            //reject(new Error('blink fail no layer'))
            resolve()
            console.warn('blink fail no layer found', zoneItem.id)
          }
        } catch (err) {
          reject(err)
          console.warn({
            details: 'While making layer blink',
            err,
          })
        }
      })
    }
  }

  function configureMapInteractions() {
    logging && console.log('zm::configureMapInteractions')
    configureGeomanTranslations(state.mapVM.map)
    state.mapVM.map.pm.setGlobalOptions(globalLeafletGeomanOptions)
    state.mapVM.map.on('pm:create', onLeafletGeomanLayerCreated)
    //state.mapVM.map.on('zoomend', updateMapLayersHandler)
    state.mapVM.map.on('pm:remove', (e) => {
      if (e.shape === 'Marker') {
        state.leafletZoneAccessLayer = null
      }
      if (['Circle', 'Polygon'].includes(e.shape)) {
        state.leafletZoneLayer = null
      }
    })
    state.isMapConfigured = true
  }

  function updateMapLayersHandler() {
    //Prevent queuing render operation if map is not ready yet (less cpu intensive)
    if (!state.mapVM?.map) {
      setTimeout(() => {
        updateMapLayersHandler()
      }, 2000)
      return
    }

    queueOperationOnce(
      `zones_module__updateMapLayersHandlerRunSequential`,
      updateMapLayersHandlerRunSequential,
      {
        clearPreviousTimeout: false, //Will skip if already queued
        isSequential: true, //Will skip if already processing
        timeout: 1000, //Will only be called one if multiple calls within 200ms (e.g triggered from watcher)
        /*resolve(success) {
          if (success === false) {
            setTimeout(() => {
              updateMapLayersHandler() //e.g if map is not ready yet, retry
            }, 1000)
          }
        },*/
      }
    )
  }

  async function updateMapLayersHandlerRunSequential() {
    if (!state.mapVM?.map) {
      return false //LeafletMap is not ready yet
    }

    logging && console.log('updateMapLayers::run')

    if (!state.isMapConfigured) {
      configureMapInteractions()
    }

    if (state.zonesList.length === 0) {
      logging && console.log('updateMapLayers::start::no-zones-skip')
      return true
    }

    let zonesList = state.zonesList.map((item) => ({ ...item })) //remove ref

    let mapBounds = state.mapVM.map.getBounds()

    //Update: Disable clustering at all levels for testing
    let enableClustering = false //state.simplicitiMapVM.zoomLevel <= 14

    //Clear markers when transitioning between clustering and non-clustering mode
    if (
      typeof lastEnableClustering === 'boolean' &&
      lastEnableClustering !== enableClustering
    ) {
      state.mapVM.clearLayers('zones', {
        removeLayerGroup: true,
      })
      logging && console.log('updateMapLayers::clear')
    }

    lastEnableClustering = enableClustering

    let clusteringOptions = {}

    if (enableClustering) {
      clusteringOptions = {
        clusterOptions: {
          singleMarkerMode: true,
          iconCreateFunction: function (cluster) {
            return getClusterIcon(cluster)
          },
        },
      }
    }

    let mapZoom = state.mapVM?.map?._zoom || 5

    let visible = true
    let filteredZones = []
    if (!areZoneVisibleUnderCurrentZoom(mapZoom)) {
      visible = false
    } else {
      //Filter zones to render based on type, viewport bounds and invalid x,y.
      filteredZones = zonesList.filter((zoneItem) => {
        //on edit mode, only draw the zone we are editing
        if (interactionMode.value === 'edit') {
          if (state.currentZone.id !== zoneItem.id) {
            return false
          }
        }

        //Issue: The zone layer that we are editing should be removed if redraw
        if (
          state.leafletZoneLayer &&
          (state.leafletZoneLayer.externalId || state.currentZone.id) ===
            zoneItem.id
        ) {
          return false
        }

        const typeMatch = state.mapFilteredTypes.some(
          (typeItem) => typeItem.id === zoneItem.typeId
        )
        const hasCoordinates = !!zoneItem.lat && !!zoneItem.lng

        let boundsMatch = getLayerAndMapBoundsMatch(zoneItem, mapBounds)

        return typeMatch && boundsMatch && hasCoordinates
      })

      //Limit the number of zones to render (performance reasons)
      let maxMapZonesToRender = parseInt(
        getQueryStringValue('maxMapZones') || 1000
      )
      if (filteredZones.length > maxMapZonesToRender) {
        filteredZones = filteredZones.slice(0, maxMapZonesToRender) //Only show 1k elements at time
      }
    }

    await state.mapVM.drawGeometries({
      ...clusteringOptions,
      data: filteredZones,
      layer: 'zones',
      visible,
      generate(item) {
        if (enableClustering) {
          return L.marker([item.lat, item.lng]) //Dummy marker
        }
        let layers = generateLeafletZoneGeometriesFromItem(item, state)
        let [zoneLayer] = layers

        if (zoneLayer) {
          const zoneHighlightHandler = (e) => {
            if (interactionMode.value === 'view') {
              if (e.originalEvent) {
                e.originalEvent.stopPropagation()
              } else {
                e.stopPropagation && e.stopPropagation()
              }

              item = state.zonesList.find((z) => z.id === item.id) //Ensure item is up to date
              mitt.emit(EVENT_ZONE_MODULE_ZONE_LAYER_HIGHLIGHT, item)
            }
          }

          zoneLayer.on('click', zoneHighlightHandler)
          if (layers.length > 1) {
            layers[1].on('click', zoneHighlightHandler) //Icon+Text layer
          }

          const editZoneHandler = (e) => {
            //Edit a zone is only possible during the view mode
            if (interactionMode.value !== 'view') {
              return
            }

            if (e.originalEvent) {
              e.originalEvent.stopPropagation()
            } else {
              e.stopPropagation && e.stopPropagation()
            }

            item = state.zonesList.find((z) => z.id === item.id) //Ensure item is up to date
            editZoneItem(item)
          }

          zoneLayer.on('dblclick', editZoneHandler)
          if (layers.length > 1) {
            layers[1].on('dblclick', editZoneHandler) //Icon+Text layer
          }
        }

        return layers
      },
      preserveExistingLayers: !enableClustering,
      update(layer, item, layers) {
        item = state.zonesList.find((z) => z.id === item.id) //Ensure item is up to date

        let isVisible =
          !enableClustering &&
          state.mapFilteredTypes.some((typeItem) => typeItem.id === item.typeId)

        if (!isVisible) {
          layers.forEach((l) => l.remove()) //This will remove the layer from the map bet will be keep in the layer group
          return true
        } else {
          layers.forEach((layer) => {
            if (!layer._map) {
              layer.addTo(state.mapVM.map) //Add again to the map if removed (e.g toggle zones using map filters)
            }
          })
        }

        let accessPointLayer = layers.find((l) => l.options.isAccessPoint)
        let textLayer = layers.find((l) => l.options.isText)
        let zoneLayer = layers.find((l) => l.options.isZoneGeometry)

        function skipLayerUpdateAndGenerate() {
          if (zoneLayer) {
            zoneLayer.remove()
            removeLayerSafe(zoneLayer)
          }
          if (accessPointLayer) {
            accessPointLayer.remove()
            removeLayerSafe(accessPointLayer)
          }
          if (textLayer) {
            textLayer.remove()
            removeLayerSafe(textLayer)
          }
          return false //generate zone from scratch
        }

        let shouldTextAndAccessLayerBeVisible = mapZoom > 14

        //Zone text and access icon are show/hide based on zoom. If sync is needed, generate the zone again.
        if (
          (!shouldTextAndAccessLayerBeVisible &&
            (!!textLayer || !!accessPointLayer)) ||
          (shouldTextAndAccessLayerBeVisible &&
            (!textLayer || !accessPointLayer))
        ) {
          return skipLayerUpdateAndGenerate()
        }

        if (accessPointLayer) {
          accessPointLayer.setLatLng([item.accessLat, item.accessLng])
        }

        if (textLayer) {
          textLayer.setLatLng([item.lat, item.lng])
          let showLabel = mapZoom >= 17
          if (
            (zoneLayer && item.iconSrc !== zoneLayer.options.iconSrc) ||
            zoneLayer.options.name !== item.name
          ) {
            textLayer.setIcon(
              createZoneTextLayerIcon(item.iconSrc, item.name, showLabel)
            )
          } else {
            if (textLayer.showLabel !== showLabel) {
              textLayer.setIcon(
                createZoneTextLayerIcon(item.iconSrc, item.name, showLabel)
              )
            }
            textLayer.showLabel = showLabel //Why storing label in layer?
          }
        }

        //Circle zone
        if (zoneLayer.setLatLng) {
          zoneLayer.setLatLng([item.lat, item.lng])
        }

        //Polygon zone
        if (zoneLayer.setLatLngs) {
          zoneLayer.setLatLngs(item.polygon)
        }

        zoneLayer.setStyle({ color: item.color, fillColor: item.color })

        if (!zoneLayer.setLatLng) {
          logging &&
            console.log(
              'updateMapLayers::drawGeometries::update: zoneLayer update fail',
              {
                zoneLayer,
              }
            )
        }

        return true
      },
      afterUpdate({ updatedLayers, notUpdatedLayers }) {
        notUpdatedLayers.forEach((l) => l.remove()) //i.g If we delete a zone it means the associated layer will not be updated when fetching zones again. We remove those orphan layers.

        let shouldCheckTextCollisions = (state.mapVM?.map?._zoom || 5) >= 17
        if (shouldCheckTextCollisions) {
          setTimeout(() => {
            //Without timeout, zones layers are not updated somehow
            toggleTextLayersBasedOnCollisions({
              items: filteredZones,
              layerGroup: state.mapVM.layerGroups.zones,
            })
            logging && console.log('toggleTextLayersBasedOnCollisions')
          }, 500)
        }
        if (interactionMode.value === 'edit' && !state.leafletZoneLayer) {
          clearTimeout(updateStateLeafletLayersTimeout.value)
          updateStateLeafletLayers()
        }
        logging &&
          console.log('updateMapLayers::drawGeometries:afterUpdate', {
            updatedLayers: updatedLayers.length,
            notUpdatedLayers: notUpdatedLayers.length,
          })
      },
    })

    logging && console.log('updateMapLayers::draw:end')

    return true
  }

  /**
   * Hides text layers if overlaps with other text layers (Only layers visible in the current viewport)
   *
   */
  function toggleTextLayersBasedOnCollisions({ items, layerGroup }) {
    let allTextLayers = layerGroup
      .getLayers()
      .filter((layer) => layer.options.isText)

    let track = console.trackTime('Checking collisions (text layers)', false)

    track.count('allTextLayers', {
      allTextLayers: allTextLayers,
      allLayers: layerGroup.getLayers(),
    })

    let itemsAndTextLayers = items.map((zoneItem) => ({
      textLayer: allTextLayers.find(
        (layer) => layer.externalId === zoneItem.id
      ),
      zoneItem,
    }))

    track.count('itemsAndTextLayers', {
      itemsAndTextLayers: itemsAndTextLayers,
    })

    let itemsWithoutTextLayer = itemsAndTextLayers.filter((o) => !o.textLayer)
    let itemsWithTextLayer = itemsAndTextLayers.filter((o) => o.textLayer)

    itemsWithoutTextLayer.forEach(({ zoneItem }) =>
      track.count(`has-no-text-layer (${zoneItem.name})`)
    )
    itemsWithTextLayer.forEach(({ zoneItem }) =>
      track.count(`has-text-layer (${zoneItem.name})`)
    )

    //Traverse items
    itemsWithTextLayer.forEach(({ zoneItem, textLayer }) => {
      let overlap = doesTextOverlapInViewport({
        zoneItem,
        textLayer,
        itemsWithTextLayer,
      })
      if (overlap) {
        if (state.targetZoneItem?.id === zoneItem.id) {
          track.count(
            'overlap for ' + zoneItem.name,
            'skip because is a zone being targeted'
          )
          return
        }

        track.count('overlap for ' + zoneItem.name)
        textLayer.setIcon(
          createZoneTextLayerIcon(zoneItem.iconSrc, zoneItem.name, false)
        ) //Hide text node
      } else {
        track.count('no-overlap for ' + zoneItem.name)
      }
    })
    track()

    function doesTextOverlapInViewport({
      zoneItem,
      textLayer,
      itemsWithTextLayer,
    }) {
      function collide(elementA, elementB) {
        //Get the bounding rectangles of the two elements
        var rectA = elementA.getBoundingClientRect()
        var rectB = elementB.getBoundingClientRect()

        //Check if the two rectangles overlap
        return (
          rectA.left < rectB.right &&
          rectA.right > rectB.left &&
          rectA.top < rectB.bottom &&
          rectA.bottom > rectB.top
        )
      }

      let textLayersToCompare = itemsWithTextLayer.filter(
        ({ zoneItem: item }) => {
          return item.id !== zoneItem.id
        }
      )

      logging &&
        console.log('doesTextOverlapInViewport', {
          textLayersToCompare,
          textLayer,
        })

      return textLayersToCompare.some(({ textLayer: currTextLayer }) => {
        //lealfet bounds intersects doesn't seem to work ok on markers
        //return currTextLayer.getBounds().intersects(textLayer.getBounds())
        //lets compare the name dom node for collisions
        let span1 = currTextLayer.getElement()?.querySelector('span')
        let span2 = textLayer.getElement()?.querySelector('span')

        if (!span1 || !span2) {
          return false //At some point, if we start hiding dom nodes, they will not be available here
        }

        /*console.log('doesTextOverlapInViewport', {
          span1: span1.innerHTML,
          span2: span2.innerHTML,
          value: collide(span1, span2),
        })*/
        return collide(span1, span2)
      })
    }
  }

  /**
   * Sync Leaflet layers (zone and access) into state (Edit)
   *
   * This function is called up to 30 times by itself because there is a race condition
   * (The time leaflet renders the layer and becomes available)
   */
  function updateStateLeafletLayers(times = 10) {
    logging && console.log('updateStateLeafletLayers')
    let id = state.currentZone.id
    let layers = []
    state.mapVM.map.eachLayer((l) => layers.push(l))

    state.leafletZoneAccessLayer = layers.find(
      (l) =>
        (l.externalId == id || l.options.id == id) && l.options.isAccessPoint
    )
    if (state.leafletZoneAccessLayer) {
      //throw new Error('Associated layer not found (Access point)')
      state.leafletZoneAccessLayer.options.pmIgnore = false
      L.PM.reInitLayer(state.leafletZoneAccessLayer)
    }

    state.leafletZoneLayer = layers.find(
      (l) =>
        (l.externalId == id || l.options.id == id) && l.options.isZoneGeometry
    )
    if (state.leafletZoneLayer) {
      console.info('zm layer set')
      state.leafletZoneLayer.options.pmIgnore = false

      state.mapVM?.map.addLayer(state.leafletZoneLayer.remove())
      let key = Object.keys(state.mapVM?.layerGroups.zones._layers).find(
        (k) => {
          let layer = state.mapVM?.layerGroups.zones._layers[k]
          return layer.externalId === state.leafletZoneLayer.externalId
        }
      )
      if (key) {
        delete state.mapVM?.layerGroups.zones._layers[key]
      }

      L.PM.reInitLayer(state.leafletZoneLayer)
    } else {
      if (times <= 0) {
        return console.warn('Zone layer not found')
      }
      updateStateLeafletLayersTimeout.value = setTimeout(
        () => updateStateLeafletLayers(times--),
        500
      )
    }
  }

  /**
   * @todo move to utils: addToArrTemp(arr, name)
   * @param {*} name
   * @returns
   */
  function addStateFlag(name) {
    state.stateFlags = [...[name], ...R.without([name], state.stateFlags)]
    return {
      clear() {
        state.stateFlags = [...R.without([name], state.stateFlags)]
      },
    }
  }
}

export function getZoneRenderingVisibleFlags() {
  let isZoneRenderingGeometryVisible = store.getters['settings/getParameter'](
    'isZoneRenderingGeometryVisible'
  )
  const isZoneRenderingIconVisible = store.getters['settings/getParameter'](
    'isZoneRenderingIconVisible'
  )
  const isZoneRenderingAcessPointVisible = store.getters[
    'settings/getParameter'
  ]('isZoneRenderingAcessPointVisible')

  //Fallback: If none is enable, the bare minimum is the zone geometry
  if (
    !isZoneRenderingGeometryVisible &&
    !isZoneRenderingIconVisible &&
    !isZoneRenderingAcessPointVisible
  ) {
    isZoneRenderingGeometryVisible = true
  }

  return {
    isZoneRenderingGeometryVisible,
    isZoneRenderingIconVisible,
    isZoneRenderingAcessPointVisible,
  }
}

/**
 * Zone text: Zoom level below 100m
 *
 * @param {*} currentZoom
 * @returns
 */
export function isZoneTextVisible(currentZoom) {
  return currentZoom > 16
}

/**
 * Zone icon: Zoom level below 500m
 * @param {*} currentZoom
 * @returns
 */
export function areZoneIconAndAccessPointVisiblesUnderCurrentZoom(currentZoom) {
  return currentZoom > 14
}

export function areZoneVisibleUnderCurrentZoom(currentZoom) {
  return currentZoom >= 4
}

/**
 * Will generate a Circle/Polygon along with an access point icon and the Zone name (Text node)
 * @param {*} item
 * @param {*} param1
 * @returns
 */
function generateLeafletZoneGeometriesFromItem(item, state) {
  let pathOptions = {
    weight: 2,
    color: item.color,
    fill: true,
    fillOpacity: 0.8,
    fillColor: item.color,
  }

  const mapZoom = state.mapVM?.map?._zoom || 5
  let geometry,
    accessPointGeometry,
    textGeometry = null

  let {
    isZoneRenderingGeometryVisible,
    isZoneRenderingIconVisible,
    isZoneRenderingAcessPointVisible,
  } = getZoneRenderingVisibleFlags()

  if (item.id === state?.currentZone?.id) {
    isZoneRenderingAcessPointVisible =
      isZoneRenderingGeometryVisible =
      isZoneRenderingIconVisible =
        true
  }

  if (isZoneRenderingGeometryVisible) {
    geometry = item.isPolygon
      ? L.polygon(item.polygon, {
          ...pathOptions,
          isZoneGeometry: true,
        })
      : L.circle([item.lat, item.lng], {
          ...pathOptions,
          radius: item.radius,
          isZoneGeometry: true,
        })
  }

  if (isZoneRenderingAcessPointVisible) {
    accessPointGeometry =
      item.accessLat && item.accessLng
        ? L.marker([item.accessLat, item.accessLng], {
            icon: L.divIcon({
              className: 'zone_marker__access_point_icon',
              html: `<svg xmlns="http: //www. W3. Org/2000/svg" width="18.385" height="18.385" viewbox="0 0 18.385 18.385" > <g class="a" transform="translate(9.192) rotate(45)" : fill="var(--color-dark-blue)" > <rect class="b" style="stroke: none" stroke="none" width="13" height="13" rx="2" /> <rect class="c" style="fill: none" fill="none" x="0.5" y="0.5" width="12" height="12" rx="1.5" /> </g> </svg>`,
            }),
            isAccessPoint: true,
            id: item.id,
          })
        : null
  }

  if (isZoneRenderingIconVisible) {
    textGeometry = new L.marker([item.lat, item.lng], {
      opacity: 1,
      icon: createZoneTextLayerIcon(
        item.iconSrc,
        item.name,
        isZoneTextVisible(mapZoom)
      ),
      isText: true,
    })
  }

  const layers = []

  if (geometry) {
    layers.push(geometry)
  }

  if (areZoneIconAndAccessPointVisiblesUnderCurrentZoom(mapZoom)) {
    if (textGeometry) {
      layers.push(textGeometry)
    }
    if (accessPointGeometry) {
      layers.push(accessPointGeometry)
    }
  }

  /*
  if (layers.length === 0) {
    throw new Error(
      'Zone rendering should use at least one layer (geometry, icon, access)'
    )
  }*/

  return layers.filter((g) => !!g)
}

function createZoneTextLayerIcon(iconSrc, name = '', showName = true) {
  let nameHTML = `<div><span>${showName ? name : ''}</span></div>`
  return L.divIcon({
    html: `<div>
      <img src="${iconSrc}" title="${name}"/>
      ${nameHTML}
      </div>
      `,
    iconAnchor: [16, 2],
    className: 'zone_marker__text_icon',
  })
}

/**
 * Uses the same icon as identification module
 * @param {*} cluster
 * @returns
 */
function getClusterIcon(cluster) {
  return L.divIcon({
    className: 'cluster_marker',
    html: `<div class="cluster_position_marker cluster_identification_marker">${cluster.getChildCount()}</div>`,
  })
  /*
  let size = 21
  size = cluster.getChildCount() > 99 ? 16 : size
  size = cluster.getChildCount() > 999 ? 12 : size
  return L.divIcon({
    className: 'cluster_marker',
    html: `<div class="marker__content">
    <div>
      <img class="marker_icon alert_marker_icon"
          src="./lib/realtimeMap/assets/picto_pins/Pin.svg">
      <div class="alert_marker_icon_inner marker_icon_cluster_text" style="font-size:${size}px;">
          ${cluster.getChildCount()}
      </div>
    </img>
  </div>`,
  })*/
}
