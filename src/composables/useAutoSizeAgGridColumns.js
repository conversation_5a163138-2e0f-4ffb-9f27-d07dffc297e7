import { ref } from 'vue'

export function useAutoSizeAgGridColumns(gridApi, columnApi, currentDomLayout) {
  const autoSizeAllColumns = () => {
    if (!columnApi.value || !gridApi.value) return

    const allColumnIds = columnApi.value
      .getAllGridColumns()
      .map((col) => col.getColId())

    columnApi.value.autoSizeColumns(allColumnIds, false)
  }

  return {
    autoSizeAllColumns,
  }
}
