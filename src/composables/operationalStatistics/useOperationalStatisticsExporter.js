import i18n from '@/i18n'
import store from '@/store'
import { exportDatatable } from '@/services/export-service'
import {
  formatDate,
  formatTime,
  formatSecondsToHHMMSS,
  formatSecondsToHHMM,
  driverNameGetter,
  tippingCountGetter,
  getNestedValue,
  escapeCSVField,
} from '@/utils/operationalStatisticsFormatters'

const fileName = 'useOperationalStatisticsExporter.js' // For logging

export default function useOperationalStatisticsExporter(
  getPooledDataFromLocalStorage
) {
  // const { t } = i18n; // 't' will be used as i18n.t()

  const exportDataToCSV = () => {
    const functionName = 'exportDataToCSV'
    console.log(
      `%c${fileName} ${functionName} Exporting data to CSV`,
      'color: dodgerblue'
    )
    const dataToExport = getPooledDataFromLocalStorage()

    if (!dataToExport || dataToExport.length === 0) {
      store.dispatch(
        'operationalStatistics/setOpStatsError',
        i18n.t('operationalStatistics.errors.noDataToExport')
      )
      // Consider a more integrated notification system if available
      alert(i18n.t('operationalStatistics.errors.noDataToExport'))
      return
    }

    const exportColumns = [
      {
        headerKey: 'operationalStatistics.gridHeaders.vehicleName',
        field: 'vehicleName',
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.driverName',
        getter: driverNameGetter,
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.roundName',
        field: 'roundName',
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.executionDate',
        field: 'executionStart',
        formatter: (val) => formatDate(val, i18n.locale),
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.executionStartTime',
        field: 'executionStart',
        formatter: (val) => formatTime(val, i18n.locale),
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.executionEndTime',
        field: 'executionEnd',
        formatter: (val) => formatTime(val, i18n.locale),
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.executionDuration',
        field: 'duration',
        formatter: formatSecondsToHHMMSS,
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.serviceTime',
        field: 'fdr.serviceTime',
        formatter: formatSecondsToHHMM,
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.theoreticalDistance',
        field: 'theoricalDistance',
        formatter: (val) => (val != null ? `${val} km` : ''),
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.actualDistance',
        field: 'actualDistance',
        formatter: (val) => (val != null ? `${val} km` : ''),
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.totalDistance',
        field: 'distance',
        formatter: (val) => (val != null ? `${val} km` : ''),
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.averageSpeed',
        field: 'averageSpeed',
        formatter: (val) => (val != null ? `${val} km/h` : ''),
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.fuelConsumed',
        field: 'fuelConsumed',
        formatter: (val) => (val != null ? `${val} L` : ''),
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.binsCollectedLeft',
        field: 'binCollection.leftSideNumber',
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.binsCollectedRight',
        field: 'binCollection.rightSideNumber',
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.weightCollected',
        field: 'binCollection.weight',
        formatter: (val) => (val != null ? `${val} kg` : ''),
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.tippingCount',
        getter: tippingCountGetter,
      },
      {
        headerKey: 'operationalStatistics.gridHeaders.status',
        field: 'status',
      },
      // Hidden fields can be added here if needed for export, otherwise they are skipped
      // { headerKey: 'operationalStatistics.gridHeaders.roundExecutionId', field: 'roundExecutionId' },
      // { headerKey: 'operationalStatistics.gridHeaders.roundId', field: 'roundId' },
      // { headerKey: 'operationalStatistics.gridHeaders.vehicleId', field: 'vehicleId' },
      // { headerKey: 'operationalStatistics.gridHeaders.clientId', field: 'clientId' },
      // { headerKey: 'operationalStatistics.gridHeaders.fdrId', field: 'fdr.id' },
    ]

    try {
      const headers = exportColumns.map((col) => i18n.t(col.headerKey))
      const csvRows = [headers.map(escapeCSVField).join(',')] // Header row

      dataToExport.forEach((item) => {
        const row = exportColumns.map((col) => {
          let value
          if (col.getter) {
            value = col.getter(item)
          } else if (col.field) {
            value = getNestedValue(item, col.field)
          }
          if (col.formatter) {
            value = col.formatter(value)
          }
          return escapeCSVField(value)
        })
        csvRows.push(row.join(','))
      })

      const csvString = csvRows.join('\n')
      const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[:T]/g, '-')
      link.setAttribute('download', `operational_statistics_${timestamp}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      console.log(
        `%c${fileName} ${functionName} CSV export successful`,
        'color: mediumseagreen'
      )
      store.dispatch('operationalStatistics/setOpStatsPoolingProgress', {
        text: i18n.t('operationalStatistics.messages.exportSuccess'),
        percentage: 100,
      }) // Use progress for feedback
    } catch (err) {
      console.error(
        `%c${fileName} ${functionName} Error generating CSV`,
        'color: tomato',
        { message: err.message, stack: err.stack }
      )
      store.dispatch(
        'operationalStatistics/setOpStatsError',
        i18n.t('operationalStatistics.errors.exportFailed')
      )
      alert(
        i18n.t('operationalStatistics.errors.exportFailed') + `: ${err.message}`
      )
    }
  }

  /**
   * Extract data directly from AG Grid API
   * @param {Object} gridApi - AG Grid API instance
   * @param {Object} columnApi - AG Grid Column API instance
   * @returns {Object} - { rows, columns }
   */
  const extractGridData = (gridApi, columnApi) => {
    const columns = columnApi
      .getAllDisplayedColumns()
      .filter((col) => !col.getColDef().hide) // Only visible columns
      .map((col) => ({
        headerName: col.getColDef().headerName,
        field: col.getColDef().field,
        valueGetter: col.getColDef().valueGetter,
        valueFormatter: col.getColDef().valueFormatter,
      }))

    const rows = []
    gridApi.forEachNodeAfterFilterAndSort((node) => {
      const rowData = {}
      columns.forEach((col) => {
        let value

        // Use valueGetter if available
        if (col.valueGetter) {
          value = col.valueGetter({ data: node.data })
        } else if (col.field) {
          value = getNestedValue(node.data, col.field)
        }

        // Apply valueFormatter if available
        if (col.valueFormatter && value !== undefined && value !== null) {
          value = col.valueFormatter({ value })
        }

        // Use header name as key for export
        rowData[col.headerName] = value || ''
      })
      rows.push(rowData)
    })

    return {
      rows,
      columns: columns.map((c) => c.headerName),
    }
  }

  /**
   * Export grid data to specified format
   * @param {Object} options - Export options
   * @param {Object} options.gridApi - AG Grid API instance
   * @param {Object} options.columnApi - AG Grid Column API instance
   * @param {string} options.tableCode - Table code for filename
   * @param {string} options.format - Export format ('csv' or 'xlsx')
   */
  const exportGridData = ({ gridApi, columnApi, tableCode, format }) => {
    const functionName = 'exportGridData'
    console.log(
      `%c${fileName} ${functionName} Exporting grid data to ${format}`,
      'color: dodgerblue'
    )

    const { rows, columns } = extractGridData(gridApi, columnApi)

    if (!rows || rows.length === 0) {
      store.dispatch(
        'operationalStatistics/setOpStatsError',
        i18n.t('operationalStatistics.errors.noDataToExport')
      )
      // Show toast notification
      if (window.vue && window.vue.$toast) {
        window.vue.$toast.warning({
          title: i18n.t('operationalStatistics.validation.required'),
          text: i18n.t('operationalStatistics.errors.noDataToExport'),
          timeout: 4000,
        })
      }
      return
    }

    try {
      exportDatatable({
        data: rows,
        columns: columns,
        tableCode: tableCode,
        format: format,
      })

      console.log(
        `%c${fileName} ${functionName} ${format.toUpperCase()} export successful`,
        'color: mediumseagreen'
      )

      store.dispatch('operationalStatistics/setOpStatsPoolingProgress', {
        text: i18n.t('operationalStatistics.messages.exportSuccess'),
        percentage: 100,
      })
    } catch (err) {
      console.error(
        `%c${fileName} ${functionName} Error generating ${format.toUpperCase()}`,
        'color: tomato',
        { message: err.message, stack: err.stack }
      )

      store.dispatch(
        'operationalStatistics/setOpStatsError',
        i18n.t('operationalStatistics.errors.exportFailed')
      )

      if (window.vue && window.vue.$toast) {
        window.vue.$toast.error({
          title: i18n.t('operationalStatistics.validation.required'),
          text:
            i18n.t('operationalStatistics.errors.exportFailed') +
            `: ${err.message}`,
          timeout: 4000,
        })
      }
    }
  }

  return {
    exportDataToCSV,
    exportGridData,
    extractGridData,
  }
}
