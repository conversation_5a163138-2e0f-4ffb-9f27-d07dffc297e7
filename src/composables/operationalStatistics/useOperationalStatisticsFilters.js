import { ref, watch, computed } from 'vue'
import store from '@/store'
import moment from 'moment'
import { flattenArrayOfPairs } from '@/utils/array'
import { getDaysDifferenceBetweenMinMaxDates } from '@/utils/dates'

/**
 * Composable for managing operational statistics filter state.
 */
export default function useOperationalStatisticsFilters() {
  // Local reactive state for filters, initialized to defaults
  const selectedVehicles = ref([])
  const dateRange = ref({ startDate: null, endDate: null })

  const selectedCircuits = computed({
    get: () =>
      store.getters['search_module/getSelectedOperationalStatsCircuits'],
    set: (value) =>
      (store.state.search_module.selectedOperationalStatsCircuits = value),
  })

  const selectedCircuitCategories = computed({
    get: () =>
      store.getters[
        'search_module/getSelectedOperationalStatsCircuitsCategories'
      ],
    set: (value) =>
      (store.state.search_module.selectedOperationalStatsCircuitsCategories =
        value),
  })

  // Get filter criteria from Vuex store
  const vuexFilterCriteria = computed(
    () => store.getters['operationalStatistics/filterCriteria']
  )

  // Initialize local filters from Vuex store state once
  // This ensures that if Vuex has persisted/initial state, we use it.
  if (vuexFilterCriteria.value) {
    selectedCircuits.value = [...(vuexFilterCriteria.value.circuits || [])]

    selectedVehicles.value = [...(vuexFilterCriteria.value.vehicles || [])]
    dateRange.value = {
      startDate: vuexFilterCriteria.value.dateRange?.startDate || null,
      endDate: vuexFilterCriteria.value.dateRange?.endDate || null,
    }
  }

  // Watch for changes in local filter states and dispatch action to update Vuex
  watch(
    [selectedCircuits, selectedVehicles, dateRange],
    ([circuits, vehicles, dr]) => {
      const newFilterCriteria = {
        circuits: circuits,
        vehicles: vehicles,
        dateRange: dr,
      }
      // console.log('useOperationalStatisticsFilters: Local change, dispatching updateOpStatsFilters', newFilterCriteria);
      store.dispatch(
        'operationalStatistics/updateOpStatsFilters',
        newFilterCriteria
      )
    },
    { deep: true } // Use deep watch for dateRange object and arrays if they contain objects
  )

  // Watch for external changes in Vuex store (e.g., devtools, initial load) and update local state
  watch(
    vuexFilterCriteria,
    (newFilters) => {
      if (newFilters) {
        // console.log('useOperationalStatisticsFilters: Vuex change, updating local state', newFilters);
        selectedCircuits.value = [...(newFilters.circuits || [])]
        selectedVehicles.value = [...(newFilters.vehicles || [])]
        dateRange.value = {
          startDate: newFilters.dateRange?.startDate || null,
          endDate: newFilters.dateRange?.endDate || null,
        }
      }
    },
    { deep: true }
  )

  // Computed property to group all filter criteria
  const currentFilterCriteria = computed(() => ({
    selectedCircuitsLen: selectedCircuits.value.length,
    selectedVehiclesLen: selectedVehicles.value.length,
    selectedCircuits: selectedCircuits.value,
    selectedVehicles: selectedVehicles.value,
    dateRange: dateRange.value,
    selectedCircuitCategoriesLen: selectedCircuitCategories.value.length,
  }))

  const lastSearchedDateRange = computed(() => {
    return {
      startDate:
        store.state.operationalStatistics.lastSearchedFilterCriteria.dateRange
          .startDate,
      endDate:
        store.state.operationalStatistics.lastSearchedFilterCriteria.dateRange
          .endDate,
    }
  })

  const dateRangeExceedsOneYear = computed(() => {
    const spanDays = getDaysDifferenceBetweenMinMaxDates([
      lastSearchedDateRange.value.startDate,
      lastSearchedDateRange.value.endDate,
    ])
    return spanDays > 365
  })

  // Function to reset filters to their default/empty state
  const resetFilters = () => {
    // console.log('useOperationalStatisticsFilters: Resetting filters');
    selectedCircuits.value = []
    selectedVehicles.value = []
    dateRange.value = { startDate: null, endDate: null }
    // The watch effect will automatically dispatch the cleared state to Vuex
  }

  const syncSelectedVehiclesFromStore = () => {
    selectedVehicles.value = store.getters['search_module/getSelectedVehicles']
  }

  watch(
    () => store.getters['search_module/getSelectedVehicles'],
    (newVehicles) => {
      syncSelectedVehiclesFromStore()
      console.log('SearchModule selectedVehicles changed')
    },
    { deep: true, immediate: true }
  )

  watch(
    () => store.getters['search_module/getSelectedDateRanges'],
    (newDateRanges) => {
      if (newDateRanges.length === 0) {
        dateRange.value = { startDate: null, endDate: null }
        return
      }

      //Get a flattened array of date ranges
      const flattenedDateRanges = flattenArrayOfPairs(newDateRanges)

      //Get first and last date
      dateRange.value = {
        startDate: flattenedDateRanges[0],
        endDate: flattenedDateRanges[flattenedDateRanges.length - 1],
      }
      console.log('SearchModule selectedDateRanges changed', {
        newDateRanges,
      })
    },
    { deep: true, immediate: true }
  )

  //For each selected circuit category, add all circuits to the selected circuits
  watch(
    () => selectedCircuitCategories.value,
    (newCategories) => {
      selectedCircuits.value = []
      newCategories.forEach((category) => {
        let matches = store.getters['search_module/getCircuits'].filter(
          (circuit) => parseInt(circuit.categoryId) === parseInt(category.id)
        )
        matches.forEach((match) => {
          if (
            !selectedCircuits.value.some(
              (circuit) => parseInt(circuit.id) === parseInt(match.id)
            )
          ) {
            selectedCircuits.value.push({
              id: match.id,
              name: match.name,
              categoryId: match.categoryId,
            })
          }
        })
      })

      console.log(
        'useOperationalStatisticsFilters: selectedCircuitCategories changed',
        newCategories
      )
    },
    { deep: true, immediate: true }
  )

  return {
    selectedCircuits,
    selectedVehicles,
    dateRange,
    currentFilterCriteria,
    selectedCircuitCategories,
    resetFilters,
    syncSelectedVehiclesFromStore,
    dateRangeExceedsOneYear,
  }
}
