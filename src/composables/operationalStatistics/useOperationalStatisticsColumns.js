import store from '@/store'
import { computed } from 'vue'
import i18n from '@/i18n'
import {
  adjustDateWithTimezone,
  formatDate,
  formatDatetime,
  formatTime,
} from '@/utils/dates'
import { metersToKilometers } from '@/utils/number'

export default function useOperationalStatisticsColumns() {
  const activeTab = computed(() => {
    return store.state.operationalStatistics.activeTab
  })

  const columnDefs = computed(() => {
    const tab = activeTab.value

    const generalInfosGroup = getGeneralInfosGroup(tab)

    const crewInfosGroup = getCrewInfosGroup()

    const roundInfosGroup = getRoundInfosGroup()

    const sensorsInfosGroup = getSensorsInfosGroup(tab)

    const emptyingWeighingInfosGroup = getEmptyingWeighingInfosGroup(tab)

    const anomaliesInfosGroup = getAnomaliesInfosGroup(tab)

    const routeInfosGroup = getRouteInfosGroup()

    return tab === 0
      ? [
          generalInfosGroup,
          crewInfosGroup,
          roundInfosGroup,
          sensorsInfosGroup,
          emptyingWeighingInfosGroup,
          anomaliesInfosGroup,
        ]
      : [
          generalInfosGroup,
          routeInfosGroup,
          sensorsInfosGroup,
          emptyingWeighingInfosGroup,
          anomaliesInfosGroup,
        ]
  })

  //Get common group, with specific columns for each tab
  function getGeneralInfosGroup(tab) {
    const columnIdPrefix = 'generalInfos'

    const sharedVehicleNameColumn = {
      colId: `${columnIdPrefix}_vehicleName_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.generalInfos.vehicleName'
        ),
        headerTooltip: '',
        sortable: true,
      },
      field: 'vehicle',
    }

    const sharedExecutionStartColumn = {
      colId: `${columnIdPrefix}_executionStart_tab${tab}`,
      sort: 'desc', // Default sorted column
      headerComponentParams: {
        displayName: i18n.t(
          `operationalStatistics.gridHeaders.generalInfos.${tab}.executionStart`
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          `operationalStatistics.gridHeaders.generalInfos.tooltips.${tab}.start`
        ),
        sortable: true,
      },
      field: tab === 0 ? 'executionStart' : 'start',
      valueFormatter: (params) =>
        formatDatetime(adjustDateWithTimezone(params.value)),
    }

    const sharedExecutionEndColumn = {
      colId: `${columnIdPrefix}_executionEnd_tab${tab}`,
      sort: 'desc', // Default sorted column
      headerComponentParams: {
        displayName: i18n.t(
          `operationalStatistics.gridHeaders.generalInfos.${tab}.executionEnd`
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          `operationalStatistics.gridHeaders.generalInfos.tooltips.${tab}.end`
        ),
        sortable: true,
      },
      field: tab === 0 ? 'executionEnd' : 'end',
      valueFormatter: (params) =>
        formatDatetime(adjustDateWithTimezone(params.value)),
    }

    const tab0DayColumn = {
      colId: `${columnIdPrefix}_day_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.generalInfos.day'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.generalInfos.tooltips.day'
        ),
        sortable: true,
      },
      field: 'day',
    }

    const tab0RoundNameColumn = {
      colId: `${columnIdPrefix}_roundName_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.generalInfos.circuitName'
        ),
        headerTooltip: '',
        sortable: true,
      },
      field: 'round',
    }

    const tab0RoundFluxColumn = {
      colId: `${columnIdPrefix}_roundFlux_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.generalInfos.circuitFlux'
        ),
        headerTooltip: '',
        sortable: true,
      },
      field: 'flux',
    }

    return {
      headerName: i18n.t(
        'operationalStatistics.gridHeadersGroups.generalInfos'
      ),
      groupId: `${columnIdPrefix}_tab${tab}`,
      marryChildren: true,
      children:
        tab === 0
          ? [
              sharedExecutionStartColumn,
              sharedExecutionEndColumn,
              tab0DayColumn,
              sharedVehicleNameColumn,
              tab0RoundNameColumn,
              tab0RoundFluxColumn,
            ]
          : [
              sharedExecutionStartColumn,
              sharedExecutionEndColumn,
              sharedVehicleNameColumn,
            ],
    }
  }

  function getCrewInfosGroup() {
    const columnIdPrefix = 'crewInfos'

    const plannedDriverColumn = {
      colId: `${columnIdPrefix}_plannedDriver_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.crew.plannedDriver'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.crew.tooltips.plannedDriver'
        ),
        sortable: true,
      },
      valueGetter: arrayFieldFormatter('planDrivers', ','),
    }

    const declaredDriverColumn = {
      colId: `${columnIdPrefix}_declaredDriver_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.crew.declaredDriver'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.crew.tooltips.declaredDriver'
        ),
        sortable: true,
      },
      valueGetter: arrayFieldFormatter('realDrivers', ','),
    }

    const identifiedDriverColumn = {
      colId: `${columnIdPrefix}_identifiedDriver_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.crew.identifiedDriver'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.crew.tooltips.identifiedDriver'
        ),
        sortable: true,
      },
      valueGetter: 'identificationDriver',
    }

    const ripersColumn = {
      colId: `${columnIdPrefix}_ripers_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t('operationalStatistics.gridHeaders.crew.ripers'),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.crew.tooltips.ripers'
        ),
        sortable: true,
      },
      valueGetter: arrayFieldFormatter('ripers', ','),
    }

    return {
      headerName: i18n.t('operationalStatistics.gridHeadersGroups.crew'),
      groupId: `${columnIdPrefix}_tab0`,
      marryChildren: true,
      children: [
        plannedDriverColumn,
        declaredDriverColumn,
        identifiedDriverColumn,
        ripersColumn,
      ],
    }
  }

  function getRoundInfosGroup() {
    const columnIdPrefix = 'roundInfos'

    const realizationRateColumn = {
      colId: `${columnIdPrefix}_realizationRate_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.realizationRate'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.realizationRate'
        ),
        sortable: true,
      },
      field: 'realizationRate',
      valueFormatter: (params) =>
        params.value != null ? `${params.value} %` : '',
    }

    const realizationRateIgnoredColumn = {
      colId: `${columnIdPrefix}_realizationRateIgnored_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.realizationRateIgnored'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.realizationRateIgnored'
        ),
        sortable: true,
      },
      field: 'realizationRateIgnored',
      valueFormatter: (params) =>
        params.value != null ? `${params.value} %` : '',
    }

    const serviceTimeColumn = {
      colId: `${columnIdPrefix}_serviceTime_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.serviceTime'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.serviceTime'
        ),
        sortable: true,
      },
      field: 'serviceTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const serviceDistanceColumn = {
      colId: `${columnIdPrefix}_serviceKm_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.serviceKm'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.serviceKm'
        ),
        sortable: true,
      },
      field: 'serviceDistance',
      valueFormatter: (params) =>
        params.value != null ? `${metersToKilometers(params.value)} km` : '',
    }

    const collectionTimeColumn = {
      colId: `${columnIdPrefix}_collectionTime_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.collectionTime'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.collectionTime'
        ),
        sortable: true,
      },
      field: 'collectionTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const collectionDistanceColumn = {
      colId: `${columnIdPrefix}_collectionKm_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.collectionKm'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.collectionKm'
        ),
        sortable: true,
      },
      field: 'collectionDistance',
      valueFormatter: (params) =>
        params.value != null ? `${metersToKilometers(params.value)} km` : '',
    }

    const hlpTimeColumn = {
      colId: `${columnIdPrefix}_hlpTime_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t('operationalStatistics.gridHeaders.round.hlpTime'),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.hlpTime'
        ),
        sortable: true,
      },
      field: 'hlpTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const hlpDistanceColumn = {
      colId: `${columnIdPrefix}_hlpKm_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t('operationalStatistics.gridHeaders.round.hlpKm'),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.hlpKm'
        ),
        sortable: true,
      },
      field: 'hlpDistance',
      valueFormatter: (params) =>
        params.value != null ? `${metersToKilometers(params.value)} km` : '',
    }

    const reverseTimeColumn = {
      colId: `${columnIdPrefix}_reverseTime_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.reverseTime'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.reverseTime'
        ),
        sortable: true,
      },
      field: 'reverseTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const reverseDistanceColumn = {
      colId: `${columnIdPrefix}_reverseKm_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.reverseKm'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.reverseKm'
        ),
        sortable: true,
      },
      field: 'reverseDistance',
      valueFormatter: (params) =>
        params.value != null ? `${metersToKilometers(params.value)} km` : '',
    }

    const breakTimeColumn = {
      colId: `${columnIdPrefix}_breakTime_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.breakTime'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.breakTime'
        ),
        sortable: true,
      },
      field: 'breakTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const stopTimeColumn = {
      colId: `${columnIdPrefix}_stopTime_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t('operationalStatistics.gridHeaders.round.stopTime'),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.stopTime'
        ),
        sortable: true,
      },
      field: 'stopTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const stopNumberColumn = {
      colId: `${columnIdPrefix}_stopNumber_tab0`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.round.stopNumber'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.round.tooltips.stopNumber'
        ),
        sortable: true,
      },
      field: 'stopNumber',
      cellStyle: { 'text-align': 'right' },
    }

    return {
      headerName: i18n.t('operationalStatistics.gridHeadersGroups.round'),
      groupId: `${columnIdPrefix}_tab0`,
      marryChildren: true,
      children: [
        realizationRateColumn,
        realizationRateIgnoredColumn,
        serviceTimeColumn,
        serviceDistanceColumn,
        collectionTimeColumn,
        collectionDistanceColumn,
        hlpTimeColumn,
        hlpDistanceColumn,
        reverseTimeColumn,
        reverseDistanceColumn,
        breakTimeColumn,
        stopTimeColumn,
        stopNumberColumn,
      ],
    }
  }

  function getSensorsInfosGroup(tab) {
    const columnIdPrefix = 'sensorsInfos'

    const totalLiftNumberColumn = {
      colId: `${columnIdPrefix}_totalLiftNumber_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.sensors.totalLiftNumber'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.sensors.tooltips.totalLiftNumber'
        ),
        sortable: true,
      },
      field: 'liftNumber',
      cellStyle: { 'text-align': 'right' },
    }

    const leftLiftNumberColumn = {
      colId: `${columnIdPrefix}_leftLiftNumber_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.sensors.leftLiftNumber'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.sensors.tooltips.leftLiftNumber'
        ),
        sortable: true,
      },
      field: 'leftSideNumber',
      cellStyle: { 'text-align': 'right' },
    }

    const rightLiftNumberColumn = {
      colId: `${columnIdPrefix}_rightLiftNumber_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.sensors.rightLiftNumber'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.sensors.tooltips.rightLiftNumber'
        ),
        sortable: true,
      },
      field: 'rightSideNumber',
      cellStyle: { 'text-align': 'right' },
    }

    const biCompLiftNumberColumn = {
      colId: `${columnIdPrefix}_biCompLiftNumber_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.sensors.biCompLiftNumber'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.sensors.tooltips.biCompLiftNumber'
        ),
        sortable: true,
      },
      field: 'centralNumber',
      cellStyle: { 'text-align': 'right' },
    }

    const combinedLiftNumberColumn = {
      colId: `${columnIdPrefix}_combinedLiftNumber_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.sensors.combinedLiftNumber'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.sensors.tooltips.combinedLiftNumber'
        ),
        sortable: true,
      },
      field: 'bothSideNumber',
      cellStyle: { 'text-align': 'right' },
    }

    return {
      headerName: i18n.t('operationalStatistics.gridHeadersGroups.sensors'),
      groupId: `${columnIdPrefix}_tab${tab}`,
      marryChildren: true,
      children: [
        totalLiftNumberColumn,
        leftLiftNumberColumn,
        rightLiftNumberColumn,
        biCompLiftNumberColumn,
        combinedLiftNumberColumn,
      ],
    }
  }

  function getEmptyingWeighingInfosGroup(tab) {
    const sharedEmptyingNumberColumn = {
      colId: `emptyingNumber_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.emptyingWeighing.emptyingNumber'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.emptyingWeighing.tooltips.emptyingNumber'
        ),
        sortable: true,
      },
      field: 'emptyingNumber',
      cellStyle: { 'text-align': 'right' },
    }

    const sharedTotalWeightWeighedColumn = {
      colId: `totalWeightWeighed_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.emptyingWeighing.totalWeightWeighed'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.emptyingWeighing.tooltips.totalWeightWeighed'
        ),
        sortable: true,
      },
      field: 'weight',
      valueFormatter: (params) =>
        params.value != null ? `${params.value} kg` : '',
    }

    const tab0TotalWeightDeclaredColumn = {
      colId: `totalWeightDeclared_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.emptyingWeighing.totalWeightDeclared'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.emptyingWeighing.tooltips.totalWeightWeighed'
        ),
        sortable: true,
      },
      field: 'fdrWeight',
      valueFormatter: (params) =>
        params.value != null ? `${params.value} kg` : '',
    }

    const children =
      tab === 0
        ? [
            sharedEmptyingNumberColumn,
            tab0TotalWeightDeclaredColumn,
            sharedTotalWeightWeighedColumn,
          ]
        : [sharedEmptyingNumberColumn, sharedTotalWeightWeighedColumn]

    return {
      headerName: i18n.t(
        'operationalStatistics.gridHeadersGroups.emptyingWeighing'
      ),
      groupId: `emptyingWeighingInfos_tab${tab}`,
      marryChildren: true,
      children: children,
    }
  }

  function getAnomaliesInfosGroup(tab) {
    const anomalyNumberColumn = {
      colId: `anomalyNumber_tab${tab}`,
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.anomalies.anomalyNumber'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.anomalies.tooltips.anomalyNumber'
        ),
        sortable: true,
      },
      field: 'anomalyNumber',
      cellStyle: { 'text-align': 'right' },
    }

    return {
      headerName: i18n.t('operationalStatistics.gridHeadersGroups.anomalies'),
      groupId: `anomaliesInfos_tab${tab}`,
      marryChildren: true,
      children: [anomalyNumberColumn],
    }
  }

  function getRouteInfosGroup() {
    const routeTimeColumn = {
      colId: 'routeTime_tab1',
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.route.routeTime'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.route.tooltips.routeTime'
        ),
        sortable: true,
      },
      field: 'time',
      valueFormatter: (params) => formatTime(params.value),
    }

    const routeDistanceColumn = {
      colId: 'routeDistance_tab1',
      sort: '',
      headerComponentParams: {
        displayName: i18n.t('operationalStatistics.gridHeaders.route.routeKm'),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.route.tooltips.routeKm'
        ),
        sortable: true,
      },
      field: 'distance',
      valueFormatter: (params) =>
        params.value != null ? `${metersToKilometers(params.value)} km` : '',
    }

    const activityTimeColumn = {
      colId: 'activityTime_tab1',
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.route.activityTime'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.route.tooltips.activityTime'
        ),
        sortable: true,
      },
      field: 'activiteTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const activityDistanceColumn = {
      colId: 'activityDistance_tab1',
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.route.activityKm'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.route.tooltips.activityKm'
        ),
        sortable: true,
      },
      field: 'activiteDistance',
      valueFormatter: (params) =>
        params.value != null ? `${metersToKilometers(params.value)} km` : '',
    }

    const breakTimeColumn = {
      colId: 'breakTime_tab1',
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.route.breakTime'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.route.tooltips.breakTime'
        ),
        sortable: true,
      },
      field: 'breakTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const stopTimeColumn = {
      colId: 'stopTime_tab1',
      sort: '',
      headerComponentParams: {
        displayName: i18n.t('operationalStatistics.gridHeaders.route.stopTime'),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.route.tooltips.stopTime'
        ),
        sortable: true,
      },
      field: 'stopTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const reverseTimeColumn = {
      colId: 'reverseTime_tab1',
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.route.reverseTime'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.route.tooltips.reverseTime'
        ),
        sortable: true,
      },
      field: 'reverseTime',
      valueFormatter: (params) => formatTime(params.value),
    }

    const reverseDistanceColumn = {
      colId: 'reverseDistance_tab1',
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.route.reverseDistance'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.route.tooltips.reverseDistance'
        ),
        sortable: true,
      },
      field: 'reverseDistance',
      valueFormatter: (params) =>
        params.value != null ? `${metersToKilometers(params.value)} km` : '',
    }

    const consumptionColumn = {
      colId: 'consumption_tab1',
      sort: '',
      headerComponentParams: {
        displayName: i18n.t(
          'operationalStatistics.gridHeaders.route.consumption'
        ),
        hasTooltip: true,
        headerTooltip: i18n.t(
          'operationalStatistics.gridHeaders.route.tooltips.consumption'
        ),
        sortable: true,
      },
      field: 'consumption',
      valueFormatter: (params) =>
        params.value != null ? `${params.value} L` : '',
    }

    return {
      headerName: i18n.t('operationalStatistics.gridHeadersGroups.route'),
      groupId: 'routeInfos_tab1',
      marryChildren: true,
      children: [
        routeTimeColumn,
        routeDistanceColumn,
        activityTimeColumn,
        activityDistanceColumn,
        breakTimeColumn,
        stopTimeColumn,
        reverseTimeColumn,
        reverseDistanceColumn,
        consumptionColumn,
      ],
    }
  }

  return {
    columnDefs,
  }
}

export const arrayFieldFormatter =
  (fieldName, separator = ',') =>
  (params) => {
    const values = params?.data?.[fieldName]

    if (!Array.isArray(values)) return ''

    return values
      .map((v) => v.trim?.() ?? v) // Safely trim if it's a string
      .filter(Boolean)
      .join(separator)
  }
