import { computed, ref } from 'vue'
import i18n from '@/i18n'
import useOperationalStatisticsExporter from './useOperationalStatisticsExporter'
import store from '@/store'
import api from '@/api'
import { adjustDateWithTimezone, dateRangeToChunks } from '@/utils/dates'
import moment from 'moment'
import { usePoolingControl } from '@/composables/usePoolingControl'

const LOCAL_STORAGE_KEY = 'geored_op_stats_data'
const LOCAL_STORAGE_KEY_OPERATIONAL_POOLED_DATA =
  'geored_op_stats_operational_pooled_data'

const VEHICLES_BATCH_SIZE = 100
const ITEMS_PER_PAGE_API = 100 // Max items per page the API supports
const CIRCUITS_BATCH_SIZE = 10 // Circuits batch size
const DATERANGE_BATCH_SIZE = 10 // Date range batch size in days (max 90 days for round_executions API)
const MAX_PARRALEL_REQUESTS = 5

const { cancelRequested, resetPoolingControl } = usePoolingControl()

/**
 * Composable for fetching, pooling, and managing operational statistics data.
 */
export default function useOperationalStatisticsData() {
  // const { t } = i18n; // 't' will be used as i18n.t()

  const fileName = 'useOperationalStatisticsData.js' // For logging

  // --- Local Storage Management ---
  const getPooledDataFromLocalStorage = () => {
    const functionName = 'getPooledDataFromLocalStorage'
    const activeTab = store.state.operationalStatistics.activeTab
    try {
      const rawData =
        activeTab === 0
          ? localStorage.getItem(LOCAL_STORAGE_KEY)
          : localStorage.getItem(LOCAL_STORAGE_KEY_OPERATIONAL_POOLED_DATA)
      return rawData ? JSON.parse(rawData) : []
    } catch (err) {
      console.error(
        `%c${fileName} ${functionName} Error parsing data from localStorage`,
        'color: tomato',
        { message: err.message, stack: err.stack }
      )
      return []
    }
  }

  const saveBatchToLocalStorage = (newDataBatch) => {
    const functionName = 'saveBatchToLocalStorage'
    const activeTab = store.state.operationalStatistics.activeTab
    try {
      const existingData = getPooledDataFromLocalStorage()
      // Naive merge: replace if exists based on a unique ID, or append.
      // A more sophisticated merge/update strategy might be needed based on data structure.
      // For now, let's assume newDataBatch is an array of new items to add.
      const updatedData = [...existingData, ...newDataBatch]

      localStorage.setItem(
        activeTab === 0
          ? LOCAL_STORAGE_KEY
          : LOCAL_STORAGE_KEY_OPERATIONAL_POOLED_DATA,
        JSON.stringify(updatedData)
      )
    } catch (err) {
      console.error(
        `%c${fileName} ${functionName} Error saving data to localStorage`,
        'color: tomato',
        { message: err.message, stack: err.stack }
      )
      store.dispatch(
        'operationalStatistics/setOpStatsError',
        i18n.t('operationalStatistics.errors.localStorageQuotaExceeded')
      )
      // Potentially throw the error if the caller needs to react to it
    }
  }

  const clearLocalStorageCache = () => {
    const functionName = 'clearLocalStorageCache'
    console.log(
      `%c${fileName} ${functionName} Attempting to clear local cache`,
      'color: dodgerblue'
    )
    try {
      localStorage.removeItem(LOCAL_STORAGE_KEY)
      localStorage.removeItem(LOCAL_STORAGE_KEY_OPERATIONAL_POOLED_DATA)
      store.dispatch('operationalStatistics/setOpStatsPooledData', []) // Also clear Vuex if it holds a copy
      store.dispatch('operationalStatistics/setOpStatsPoolingProgress', {
        text: i18n.t('operationalStatistics.messages.cacheCleared'),
        percentage: 100,
      })
      console.log(
        `%c${fileName} ${functionName} Local cache cleared successfully`,
        'color: mediumseagreen'
      )
    } catch (err) {
      console.error(
        `%c${fileName} ${functionName} Error clearing local cache`,
        'color: tomato',
        { message: err.message, stack: err.stack }
      )
      store.dispatch(
        'operationalStatistics/setOpStatsError',
        i18n.t('operationalStatistics.errors.cacheClearFailed')
      )
    }
  }

  // --- Data Fetching Logic ---
  const fetchData = async (params) => {
    const functionName = 'fetchData'
    const actionDescription = 'Fetching batch of round executions'
    console.log(
      `%c${fileName} ${functionName} ${actionDescription}`,
      'color: dodgerblue',
      { data: params }
    )
    try {
      const response = await api.georedRoundExecutions.getAll(params)

      console.debug('useOperationalStatisticsData fetchData response', {
        response,
      })

      return response
    } catch (err) {
      const axiosResponse = err.isAxiosError ? err.response : null
      console.error(
        `%c${fileName} ${functionName} ${actionDescription} - API Error`,
        'color: tomato',
        {
          message: err.message,
          stack: err.stack,
          axiosResponseData: axiosResponse?.data,
          requestParams: params,
        }
      )
      store.dispatch(
        'operationalStatistics/setOpStatsError',
        i18n.t('operationalStatistics.errors.apiError', { error: err.message })
      )
      throw err // Re-throw to be caught by the calling pooling logic
    }
  }

  const fetchOperationalData = async (params) => {
    const functionName = 'fetchOperationalData'
    const actionDescription = 'Fetching batch of operational data'

    const getOperationalParams = () => {
      return {
        vehicleId: params.vehicleId,
        startAt: params.start,
        endAt: params.end,
        itemsPerPage: params.itemsPerPage || ITEMS_PER_PAGE_API, // Default to ITEMS_PER_PAGE_API if not provided
        page: params.page || 1, // Default to page 1 if not provided
      }
    }

    console.log(
      `%c${fileName} ${functionName} ${actionDescription}`,
      'color: dodgerblue',
      { data: params }
    )
    try {
      const response = await api.georedServiceVehicleAggregateGps.getAll(
        getOperationalParams()
      )

      console.debug('useOperationalStatisticsData fetchData response', {
        response,
      })

      return response
    } catch (err) {
      const axiosResponse = err.isAxiosError ? err.response : null
      console.error(
        `%c${fileName} ${functionName} ${actionDescription} - API Error`,
        'color: tomato',
        {
          message: err.message,
          stack: err.stack,
          axiosResponseData: axiosResponse?.data,
          requestParams: params,
        }
      )
      store.dispatch(
        'operationalStatistics/setOpStatsError',
        i18n.t('operationalStatistics.errors.apiError', { error: err.message })
      )
      throw err // Re-throw to be caught by the calling pooling logic
    }
  }

  /**
   * Fetches and pools operational statistics data from the API using a chunking mechanism to handle large datasets.
   * The function processes data in the following chunks:
   * 1. Circuits: Processes selected circuits or all circuits if none selected
   * 2. Vehicles: Processes vehicles in batches (VEHICLES_BATCH_SIZE) for each circuit
   * 3. Pagination: Handles pagination of API results (ITEMS_PER_PAGE_API items per page)
   *
   * The function updates the Vuex store with progress and loads data into:
   * - Vuex store for immediate UI updates
   * - localStorage for persistence across page reloads
   *
   * @param {Object} rawFilterCriteria - Filter criteria for data fetching
   * @param {Array} [rawFilterCriteria.selectedCircuits=[]] - Array of selected circuit objects with id property
   * @param {Array} [rawFilterCriteria.selectedCircuitCategories=[]] - Array of selected circuit category objects
   * @param {Array} [rawFilterCriteria.selectedVehicles=[]] - Array of selected vehicle objects with id property
   * @param {Object} [rawFilterCriteria.dateRange={startDate: null, endDate: null}] - Date range for filtering
   * @param {Date} [rawFilterCriteria.dateRange.startDate] - Start date for filtering
   * @param {Date} [rawFilterCriteria.dateRange.endDate] - End date for filtering
   * @returns {Promise<void>} Resolves when all data has been fetched and processed
   * @throws {Error} If there's an error during the API request or data processing
   * @example
   * await poolData({
   *   selectedCircuits: [{ id: 1 }],
   *   selectedVehicles: [{ id: 101 }, { id: 102 }],
   *   dateRange: { startDate: new Date('2023-01-01'), endDate: new Date('2023-01-31') }
   * });
   */
  const poolData = async (rawFilterCriteria) => {
    const functionName = 'poolData'
    const activeTab = store.state.operationalStatistics.activeTab

    console.log(
      `%c${fileName} ${functionName} Starting data pooling`,
      'color: dodgerblue',
      { rawFilterCriteria }
    )

    //Reset pooling control state
    resetPoolingControl()

    store.dispatch('operationalStatistics/setOpStatsLoading', true)
    store.dispatch('operationalStatistics/setOpStatsError', null)
    store.dispatch('operationalStatistics/setOpStatsPoolingProgress', {
      text: i18n.t('operationalStatistics.messages.initializing'),
      percentage: 0,
    })
    // localStorage.removeItem(LOCAL_STORAGE_KEY); // Clear previous data before new pooling
    // store.dispatch('operationalStatistics/setOpStatsPooledData', []);
    //

    if (activeTab === 0) {
      localStorage.removeItem(LOCAL_STORAGE_KEY)
      await store.dispatch('operationalStatistics/setOpStatsPooledData', [])
    } else if (activeTab === 1) {
      localStorage.removeItem(LOCAL_STORAGE_KEY_OPERATIONAL_POOLED_DATA)
      await store.dispatch(
        'operationalStatistics/setOpStatsOperationalPooledData',
        []
      )
    } else {
      throw new Error('Invalid tab provided')
    }

    let totalItemsFetchedOverall = 0

    try {
      // --- Filter Preparation ---
      const {
        selectedCircuits = [],
        selectedCircuitCategories = [],
        selectedVehicles = [],
        dateRange = { startDate: null, endDate: null },
      } = rawFilterCriteria

      const effectiveCircuitIds = getSelectedCircuitIds(
        activeTab,
        selectedCircuits,
        selectedCircuitCategories
      )

      const effectiveVehicleIds = selectedVehicles.map((v) => v.id)

      const apiParamsDateRange = prepareApiParamsDateRange(dateRange)

      // --- Get batchs ---
      const tasks = prepareTasks(
        effectiveCircuitIds,
        effectiveVehicleIds,
        apiParamsDateRange
      )

      const dateChunks = getDateChunks(dateRange, DATERANGE_BATCH_SIZE)

      const totalTasks = tasks.length * dateChunks.length
      let tasksCompleted = 0

      for (const chunk of dateChunks) {
        if (cancelRequested.value) break

        // Build date params for the current chunk
        const apiParamsDateRange = {}
        if (chunk.startDate) {
          apiParamsDateRange.start = adjustDateWithTimezone(
            chunk.startDate
          ).format('YYYY-MM-DDTHH:mm:ss')
        }
        if (chunk.endDate) {
          apiParamsDateRange.end = adjustDateWithTimezone(chunk.endDate).format(
            'YYYY-MM-DDTHH:mm:ss'
          )
        }

        const taskBatches = []
        for (let i = 0; i < tasks.length; i += MAX_PARRALEL_REQUESTS) {
          taskBatches.push(tasks.slice(i, i + MAX_PARRALEL_REQUESTS))
        }

        for (const batch of taskBatches) {
          if (cancelRequested.value) break

          await Promise.all(
            batch.map(async (taskParams) => {
              if (cancelRequested.value) return

              let currentPage = 1
              let hasMoreData = true

              const baseApiParams = {
                ...taskParams,
                ...apiParamsDateRange,
                itemsPerPage: ITEMS_PER_PAGE_API,
              }

              while (hasMoreData) {
                if (cancelRequested.value) {
                  break
                }

                const currentApiParams = { ...baseApiParams, page: currentPage }

                store.dispatch(
                  'operationalStatistics/setOpStatsPoolingProgress',
                  {
                    text: i18n.t('operationalStatistics.messages.fetching'),
                    percentage: Math.round((tasksCompleted / totalTasks) * 100),
                  }
                )

                let responseData
                if (activeTab === 0) {
                  responseData = await fetchData(currentApiParams)
                } else {
                  responseData = await fetchOperationalData(currentApiParams)
                }

                if (cancelRequested.value) return

                if (Array.isArray(responseData) && responseData.length > 0) {
                  saveBatchToLocalStorage(responseData)

                  if (activeTab === 0) {
                    await store.dispatch(
                      'operationalStatistics/updateOpStatsPooledData',
                      getPooledDataFromLocalStorage()
                    )
                  } else {
                    await store.dispatch(
                      'operationalStatistics/setOpStatsOperationalPooledData',
                      getPooledDataFromLocalStorage()
                    )
                  }

                  totalItemsFetchedOverall += responseData.length

                  if (responseData.length < ITEMS_PER_PAGE_API) {
                    hasMoreData = false
                  }
                  currentPage += 1
                } else {
                  hasMoreData = false
                }
              }
              tasksCompleted++
            })
          )
        }
      }

      if (!cancelRequested.value) {
        store.dispatch('operationalStatistics/setOpStatsPoolingProgress', {
          text: i18n.t('operationalStatistics.messages.poolingComplete', {
            totalItems: totalItemsFetchedOverall,
          }),
          percentage: 100,
        })
      }
    } catch (err) {
      // Error should have been set by fetchData, but ensure loading is false
      console.error(
        `%c${fileName} ${functionName} Critical error during pooling process`,
        'color: tomato',
        { message: err.message, stack: err.stack }
      )
      // Ensure error is set in store if not already by fetchData
      if (!store.state.operationalStatistics.moduleError) {
        store.dispatch(
          'operationalStatistics/setOpStatsError',
          i18n.t('operationalStatistics.errors.poolingFailed', {
            error: err.message,
          })
        )
      }
    } finally {
      store.dispatch('operationalStatistics/setOpStatsLoading', false)
      // Final update of pooled data in Vuex, in case the last batch wasn't dispatched or if an error occurred mid-batch
      if (activeTab === 0) {
        store.dispatch(
          'operationalStatistics/updateOpStatsPooledData',
          getPooledDataFromLocalStorage()
        )
      } else if (activeTab === 1) {
        store.dispatch(
          'operationalStatistics/setOpStatsOperationalPooledData',
          getPooledDataFromLocalStorage()
        )
      } else {
        console.error(
          `%c${fileName} ${functionName} Invalid active tab state: ${activeTab}`,
          'color: tomato'
        )
      }
    }
  }

  const { exportDataToCSV } = useOperationalStatisticsExporter(
    getPooledDataFromLocalStorage
  )

  const getSelectedCircuitIds = (
    activeTab,
    selectedCircuits,
    selectedCircuitCategories
  ) => {
    if (activeTab !== 0) return []

    let effectiveCircuitIds = selectedCircuits.map((c) => c.id)

    if (selectedCircuitCategories.length > 0) {
      const allCategories =
        store.getters['search_module/getCircuitCategories'] || []
      const allCircuits = store.getters['search_module/getCircuits'] || []

      selectedCircuitCategories.forEach((selCat) => {
        const categoryDetails = allCategories.find(
          (cat) => cat.id === selCat.id
        )
        if (categoryDetails && categoryDetails.circuits) {
          categoryDetails.circuits.forEach((circuitInCat) => {
            const circuitId =
              typeof circuitInCat === 'object' ? circuitInCat.id : circuitInCat
            if (!effectiveCircuitIds.includes(circuitId)) {
              effectiveCircuitIds.push(circuitId)
            }
          })
        }
      })
    }

    return [...new Set(effectiveCircuitIds)]
  }

  const prepareApiParamsDateRange = (dateRange) => {
    const apiParamsDateRange = {}

    if (dateRange.startDate) {
      apiParamsDateRange['start'] = adjustDateWithTimezone(
        dateRange.startDate
      ).format('YYYY-MM-DDTHH:mm:ss')
    }

    if (dateRange.endDate) {
      apiParamsDateRange['end'] = adjustDateWithTimezone(dateRange.endDate)
        .endOf('day')
        .format('YYYY-MM-DDTHH:mm:ss')
    }

    return apiParamsDateRange
  }

  const prepareTasks = (
    effectiveCircuitIds,
    effectiveVehicleIds,
    apiParamsDateRange
  ) => {
    const tasks = []

    if (effectiveCircuitIds.length > 0) {
      for (
        let i = 0;
        i < effectiveCircuitIds.length;
        i += CIRCUITS_BATCH_SIZE
      ) {
        const circuitBatch = effectiveCircuitIds.slice(
          i,
          i + CIRCUITS_BATCH_SIZE
        )
        if (effectiveVehicleIds.length > 0) {
          for (
            let j = 0;
            j < effectiveVehicleIds.length;
            j += VEHICLES_BATCH_SIZE
          ) {
            const vehicleBatch = effectiveVehicleIds.slice(
              j,
              j + VEHICLES_BATCH_SIZE
            )
            tasks.push({ roundId: circuitBatch, vehicleId: vehicleBatch })
          }
        } else {
          tasks.push({ roundId: circuitBatch })
        }
      }
    } else if (effectiveVehicleIds.length > 0) {
      for (
        let i = 0;
        i < effectiveVehicleIds.length;
        i += VEHICLES_BATCH_SIZE
      ) {
        const vehicleBatch = effectiveVehicleIds.slice(
          i,
          i + VEHICLES_BATCH_SIZE
        )
        tasks.push({ vehicleId: vehicleBatch })
      }
    }

    if (tasks.length === 0 && Object.keys(apiParamsDateRange).length > 0) {
      tasks.push({})
    }

    return tasks
  }

  const getDateChunks = (dateRange, chunkSize) => {
    if (dateRange.startDate && dateRange.endDate) {
      return dateRangeToChunks(
        moment(dateRange.startDate),
        moment(dateRange.endDate),
        chunkSize
      )
    }
    return [{ startDate: null, endDate: null }]
  }

  return {
    poolData,
    clearLocalStorageCache,
    exportDataToCSV, // This is now from useOperationalStatisticsExporter
    getPooledDataFromLocalStorage, // Expose for potential direct use or testing
    opStatsData: computed(() => store.state.operationalStatistics.pooledData),
    opStatsLoading: computed(
      () => store.state.operationalStatistics.moduleLoading
    ),
    opStatsError: computed(() => store.state.operationalStatistics.moduleError),
    opStatsPoolingProgress: computed(
      () => store.state.operationalStatistics.poolingProgress
    ),
    getSelectedCircuitIds,
    prepareApiParamsDateRange,
    prepareTasks,
    getDateChunks,
    fetchData,
    fetchOperationalData,
    saveBatchToLocalStorage,
  }
}
