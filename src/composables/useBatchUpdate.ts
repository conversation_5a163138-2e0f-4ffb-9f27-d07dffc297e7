/**
 * Formerly mixin vue-set-batch-mixins.js
 * Moved to a composable to solve Vue 3 breaking changes in AlertsModule.js (ticket #46797)
 */

import { reactive, nextTick } from 'vue'

type ValidationHandler<T = any> = (newItems: T[], currentItems: T[]) => T[]

interface VsbState {
  chunkSize: number
  batchSize: number
}

export function useBatchUpdate() {
  const state = reactive<VsbState>({
    chunkSize: 500,
    batchSize: 250,
  })

  async function batchUpdateAddData<T>(
    propName: string,
    data: T[],
    targetObject: Record<string, any>,
    validationHandler?: ValidationHandler<T>
  ): Promise<void> {
    // @ts-ignore
    const t = console.trackTime('batchUpdateAddData:' + propName)

    await addData(propName, data, targetObject, validationHandler)
    t?.stop()
  }

  /**
   * Adds data to a property in batches
   * @param propName - The property name
   * @param data - The data to be added
   * @param targetObject - The object containing the property (e.g., component state)
   * @param validationHandler - Optional function to validate/manipulate data before adding
   */
  async function addData<T>(
    propName: string,
    data: T[],
    targetObject: Record<string, any>,
    validationHandler?: ValidationHandler<T>
  ): Promise<void> {
    const chunks = splitIntoChunks(data, state.chunkSize)

    for (const chunk of chunks) {
      await addChunk(propName, chunk, targetObject, validationHandler)
    }
  }

  /**
   * Adds a chunk of data in smaller batches
   * @param propName - The property name
   * @param chunk - The chunk of data
   * @param targetObject - The object containing the property (e.g., component state)
   * @param validationHandler - Optional function to validate/manipulate data before adding
   */
  async function addChunk<T>(
    propName: string,
    chunk: T[],
    targetObject: Record<string, any>,
    validationHandler?: ValidationHandler<T>
  ): Promise<void> {
    for (let index = 0; index < chunk.length; index += state.batchSize) {
      const endIndex = Math.min(index + state.batchSize, chunk.length)
      const chunkSlice = chunk.slice(index, endIndex)

      await new Promise<void>((resolve) => {
        nextTick(() => {
          let newItems = chunkSlice
          if (validationHandler) {
            newItems = validationHandler(newItems, targetObject[propName].value)
          }

          if (
            targetObject[propName] &&
            targetObject[propName].value !== undefined
          ) {
            // If it's a `ref`, we update `value` directly
            targetObject[propName].value = [
              ...targetObject[propName].value,
              ...newItems,
            ]
          } else {
            // If it's just a reactive property, update the array normally
            targetObject[propName] = [...targetObject[propName], ...newItems]
          }
          nextTick(resolve)
        })
      })

      // Add delay between batches to improve performance
      await new Promise((resolve) => setTimeout(resolve, 200))
    }
  }

  /**
   * Splits an array into smaller chunks
   * @param arr - The original array
   * @param chunkSize - The chunk size
   * @returns Array of chunks
   */
  function splitIntoChunks<T>(arr: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < arr.length; i += chunkSize) {
      chunks.push(arr.slice(i, i + chunkSize))
    }
    return chunks
  }

  return {
    batchUpdateState: state,
    batchUpdateAddData,
    splitIntoChunks,
  }
}
