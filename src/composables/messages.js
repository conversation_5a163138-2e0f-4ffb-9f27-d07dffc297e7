import store from '@/store'
import moment from 'moment'
import api from '@/api'
import { chunkArray } from '@/utils/array'
import { getEnvIntValue } from '@/services/env-service'
import { dateRangeToChunks } from '@/utils/dates'

const MAX_PARALLEL_REQ = getEnvIntValue('messagesSearchMaxParallelReq', 20)
const MAX_DATES_PER_REQ = getEnvIntValue('messagesSearchMaxDatesPerReq', 30)
const MAX_VEHICLE_PER_REQ = getEnvIntValue('messagesSearchMaxVehiclesPerReq', 1)

function getSendMessageBasicPayload(
  vehicleIds,
  message = '',
  predefinedMessageId = null
) {
  let payload = {
    vehicleIds,
  }

  // If there's a message (edited or custom), use that
  if (message) {
    payload.message = message
  }
  // Otherwise if there's a predefined message ID, use that
  else if (predefinedMessageId) {
    payload.predefinedMessage = `/geored/message/predefineds/${predefinedMessageId}` //URI
  }

  return payload
}

export default function useMessages() {
  return {
    predefinedMessage: api?.predefinedMessage,
    async fetchPredefinedMessages() {
      return await api.predefinedMessage.getAllPooling()
    },
    /**
     *
     * @param {*} vehicleIds
     * @param {*} message
     * @param {*} predefinedMessageId
     * @param {*} plannedMessageParams {dates = [start,end], days =[], frequency}
     * @returns
     */
    async planMessage(
      vehicleIds,
      message,
      predefinedMessageId = null,
      plannedMessageParams = {}
    ) {
      if (vehicleIds.length === 0) {
        throw new Error('vehicleIds required')
      }
      if (!predefinedMessageId && !message) {
        throw new Error('message or predefinedMessageId required')
      }
      let payload = getSendMessageBasicPayload(
        vehicleIds,
        message,
        predefinedMessageId
      )
      let plannedMessagePayload = {
        startAt: moment(plannedMessageParams.dates[0]).format(
          api.APIV3RequestDatetimeFormat
        ),
        endAt: moment(plannedMessageParams.dates[1]).format(
          api.APIV3RequestDatetimeFormat
        ),
        hour: moment(
          plannedMessageParams.hour || plannedMessageParams.dates[0]
        ).format(api.APIV3RequestDatetimeFormat),
        day: null,
        frequency: `${api.APIUrls.APIV3_GEORED_RECURRENCE_RECURRENCE_FREQUENCIES}/${plannedMessageParams.frequency}`,
      }

      console.log({
        plannedMessagePayload,
        plannedMessageParams,
      })

      payload = { ...payload, ...plannedMessagePayload }
      let allRes = await Promise.all(
        plannedMessageParams.days.map((dayId) => {
          const payloadSubset = {
            ...payload,
            day: `${api.APIUrls.APIV3_GEORED_RECURRENCE_RECURRENCE_DAYS}/${dayId}`,
          }
          return api.v3.post(
            api.APIUrls.APIV3_GEORED_MESSAGE_SERVICE_SEND_PLANNED_MESSAGE,
            payloadSubset
          )
        })
      )
      console.log('planMessage', {
        allRes,
      })
      return allRes
    },
    async sendMessage(vehicleIds, message = '', predefinedMessageId = null) {
      if (vehicleIds.length === 0) {
        throw new Error('vehicleIds required')
      }
      const payload = getSendMessageBasicPayload(
        vehicleIds,
        message,
        predefinedMessageId
      )
      let res = await api.v3.post(
        api.APIUrls.APIV3_GEORED_MESSAGE_SERVICE_SEND_MESSAGE,
        payload
      )
      console.log('sendMessage', {
        res,
      })
      return res
    },
    fetchMessagesPerRange,
    fetchMessagesPerEachDays,
  }
}

/**
 * Retrieve messages associated to vehicule(s) by id and each date
 *
 * @param {int[]} vehicleIds list of vehicles ids
 * @param {string[]} dates list of formatted dates
 * @param {CallableFunction} onDataReceivedHandler callback when completed
 * @returns {Promise<*|Awaited<unknown>[]>}
 */
export async function fetchMessagesPerEachDays(
  vehicleIds,
  dates = [],
  onDataReceivedHandler
) {
  //ensure dates are sorted in ASC
  dates.sort((a, b) => (a - b < 0 ? -1 : 1))

  const finalSpliOpts = {
    async handleSubset(subset) {
      return await api.vehicleMessagesSrv.getAll({
        vehicleIds: subset.vehicleIds,
        startAt: moment(subset.dates[0])
          .clone()
          .hour(0)
          .minute(0)
          .second(0)
          .format(api.APIV3RequestDatetimeFormat),
        endAt: moment(subset.dates[subset.dates.length - 1])
          .clone()
          .hour(23)
          .minute(59)
          .second(59)
          .format(api.APIV3RequestDatetimeFormat),
      })
    },
    withSubsetResult(results) {
      if (onDataReceivedHandler) {
        onDataReceivedHandler(results)
      }
    },
  }

  const finalHandleSubset = async (ids, dates) => {
    let r = await finalSpliOpts.handleSubset({
      vehicleIds: ids,
      dates: dates,
    })
    finalSpliOpts.withSubsetResult(r)
  }

  const datesChunks = chunkArray(dates, 1) //If each day, limit to 1
  const vehicleIdsChunks = chunkArray(vehicleIds, MAX_VEHICLE_PER_REQ)
  let parallelPromises = []

  let track = console.trackTime('messages fetch')
  track.count('chunks', {
    dates,
    vehicleIdsChunks,
    datesChunks,
    MAX_DATES_PER_REQ,
    MAX_VEHICLE_PER_REQ,
    MAX_PARALLEL_REQ,
  })

  //Respect max veh per req
  for (let vehIds of vehicleIdsChunks) {
    //Respect max dates per req
    for (let dates of datesChunks) {
      //If more than parallel limit, await first and shift before continue
      if (parallelPromises.length > MAX_PARALLEL_REQ) {
        await parallelPromises[0]
        parallelPromises.shift()
      }

      track.count('request')
      parallelPromises.push(finalHandleSubset(vehIds, dates))
    }
  }

  //Resolve all
  await Promise.all(parallelPromises)
  track.stop()
}

/**
 * Retrieve messages associated to vehicule(s) by id and between date range
 *
 * @param {int[]} vehicleIds list of vehicles ids
 * @param {string[]} dates list of formatted dates
 * @param {CallableFunction} onDataReceivedHandler callback when completed
 * @returns {Promise<*|Awaited<unknown>[]>}
 */
export async function fetchMessagesPerRange(
  vehicleIds,
  dates = [],
  onDataReceivedHandler
) {
  //ensure dates are sorted in ASC
  dates.sort((a, b) => (a - b < 0 ? -1 : 1))

  let startDate = moment(dates[0])
  let endDate = moment(dates[dates.length - 1])

  if (endDate.hour() === 0 && endDate.minute() === 0) {
    endDate = endDate.hour(23).minute(59).second(59)
  }

  const vehiclesIdsChunks = chunkArray(vehicleIds, MAX_VEHICLE_PER_REQ)
  const diffDays = Math.abs(endDate.diff(startDate, 'day'))

  //contains chunks of startDate/endDate
  //If date range > MAX_DATES_PER_REQ, we create chunks ranges of 1 day
  const datesChunks = []

  //e.g more than 10 days
  if (diffDays <= MAX_DATES_PER_REQ) {
    datesChunks.push({
      startDate: startDate.clone(),
      endDate: endDate.clone(),
    })
  } else {
    //24hs or more without custom time
    if (startDate.hour() === 0 && endDate.hour() === 0) {
      datesChunks.push(...dateRangeToChunks(startDate, endDate))
    } else {
      //first chunk contains time to midnight
      datesChunks.push({
        startDate: startDate.clone(),
        endDate: startDate.clone().hour(23).minute(59).second(59),
      })
      //days in between (full days)
      datesChunks.push(
        ...dateRangeToChunks(
          startDate.clone().add(1, 'day').hour(0).minute(0).second(0),
          endDate.clone().subtract(1, 'day').hour(23).minute(59).second(59)
        )
      )
      //last chunk contains midnight to start time
      datesChunks.push({
        startDate: endDate.clone().hour(0).minute(0).second(0),
        endDate: endDate.clone(),
      })
    }
  }

  const handleSubset = async (ids, startDate, endDate) => {
    let r = await api.vehicleMessagesSrv.getAll({
      vehicleIds: ids,
      startAt: startDate.format(api.APIV3RequestDatetimeFormat),
      endAt: endDate.format(api.APIV3RequestDatetimeFormat),
    })
    onDataReceivedHandler(r)
  }

  let track = console.trackTime('messages fetch')
  track.count('chunks', {
    vehiclesIdsChunks,
    datesChunks,
    MAX_DATES_PER_REQ,
    MAX_VEHICLE_PER_REQ,
    MAX_PARALLEL_REQ,
    diffDays,
  })
  let parallelPromises = []
  for (let vehicleIdsSubset of vehiclesIdsChunks) {
    for (let datesSubset of datesChunks) {
      //If more than parallel limit, await first and shift before continue
      if (parallelPromises.length > MAX_PARALLEL_REQ) {
        console.log(
          'Shifting promise to respect max parallel req',
          MAX_PARALLEL_REQ
        )
        await parallelPromises[0]
        parallelPromises.shift()
      }

      track.count('request')
      parallelPromises.push(
        handleSubset(
          vehicleIdsSubset,
          datesSubset.startDate,
          datesSubset.endDate
        )
      )
    }
  }

  //Resolve all
  await Promise.all(parallelPromises)
  track.stop()
}

/**
 *
 * @param {Ref} filterText Ref to filterText input
 * @returns
 */
export function createTeeItemComputedDatasetFromVehicles(filterText) {
  const rawItems = computed(() => store.getters['search_module/getVehicles'])
  const rawItemCategories = computed(
    () => store.getters['search_module/getVehicleCategories']
  )

  return computed(() => {
    const filterHandler = (propName) => (circuitItem) => {
      let categoryItem = categories.find((c) => c.id == circuitItem.categoryId)
      return (
        !filterText.value ||
        categoryItem.filterMatchUpwards(filterText.value) ||
        circuitItem[propName]
          .toLowerCase()
          .includes(filterText.value.toLowerCase())
      )
    }

    let categories = rawItemCategories.value.map((categoryItem) => {
      return {
        id: categoryItem.id,
        label: categoryItem.name,
        children: [],
        icon: 'mdi:truck',
        isCategory: true,
        parentId: categoryItem.parent_id || null,
        filterMatchUpwards(text = '') {
          const match = this.label
            .toString()
            .toLowerCase()
            .includes(text.toString().toLowerCase())
          if (match) {
            return true
          } else {
            let parentItem = this.parentId
              ? categories.find((item) => item.id === this.parentId)
              : null
            if (parentItem) {
              return parentItem.filterMatchUpwards(text)
            }
          }
          return false
        },
        countTotalOrphans(children) {
          let count = 0
          children = children || this.children

          count += children.filter((c) => c.isCategory !== true).length

          for (let item of children) {
            count += (item.children || []).filter(
              (child) => child.isCategory !== true
            ).length
            count += this.countTotalOrphans(
              (item.children || []).filter((c) => c.isCategory === true)
            )
          }
          return count
        },
        countTotalOrphansFromFlatArray(currentId, children) {
          let count = 0
          currentId = currentId || this.id
          children = children || this.children

          let cats = rawItemCategories.value

          count += this.children.filter((ccc) => ccc.isCategory !== true).length //direct orphans

          cats
            .filter((c) => c.parent_id == currentId)
            .forEach((c) => {
              //direct childs

              let childs = cats.filter((c) => c.parent_id == c.id)
              count += this.countTotalOrphansFromFlatArray(c.id, childs)

              /*count+= categories.find(cc=>cc.id == c.id).children.filter(ccc=>ccc.isCategory !== true).length
                if(c.parent_id == currentId){
                  count+= this.countTotalOrphansFromFlatArray(c.id)
                }*/
            })
          return count
        },
      }
    })

    //Allocate items in each category
    rawItems.value.filter(filterHandler('name')).forEach((circuitItem) => {
      let categoryItem = categories.find((c) => c.id == circuitItem.categoryId)

      let computedCircuitItem = {
        id: circuitItem.id,
        label: circuitItem.name,
        children: [],
      }

      if (categoryItem) {
        categoryItem.children.push(computedCircuitItem)
      } else {
        categories.push(computedCircuitItem)
      }
    })

    //Nest categories
    let childCategories = []
    rawItemCategories.value.forEach((categoryItem) => {
      if (categoryItem.parent_id) {
        let parentCategoryItem = categories.find(
          (c) => c.id == categoryItem.parent_id
        )
        let childIndex = categories.findIndex((cc) => cc.id == categoryItem.id)

        //If the child is a category with no childs and is not a parent, skip
        if (
          categories[childIndex].isCategory === true &&
          categories[childIndex].countTotalOrphansFromFlatArray() == 0
          //categories[childIndex].children.length === 0 &&
        ) {
          return
        }

        parentCategoryItem.children.push(categories[childIndex])
        childCategories.push(categories[childIndex].id)
      }
    })

    return categories.filter(
      (c) =>
        c.countTotalOrphans() > 0 && !childCategories.some((cId) => cId == c.id)
    )
    //.filter(filterHandler('label'))
  })
}
