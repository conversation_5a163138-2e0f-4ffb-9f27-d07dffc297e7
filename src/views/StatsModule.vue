<template>
  <TLayout
    :sync-with-vuex="false"
    :sidebar="true"
    :menu="true"
    :menu-collapsed="false"
    :menu-full-collapse="true"
    :menu-toggle="true"
    :right-menu="false"
    :right-menu-bottom="false"
  >
    <template #sidebar>
      <Sidebar></Sidebar>
    </template>

    <template #menu>
      <StatsFilters
        @search-input="onSearchModuleSelectionChange"
        @search-clear="onSearchClear"
      />
    </template>

    <template #main>
      <div class="operational-statistics-module">
        <div class="tabs">
          <div v-for="(tab, index) in tabs" :key="index" class="tab-wrapper">
            <div
              class="tab"
              :class="{
                active: activeTab === index,
                disabled: isTabDisabled(index),
              }"
              :aria-disabled="isTabDisabled(index)"
              @click="!isTabDisabled(index) && (activeTab = index)"
            >
              <span>{{ tab }}</span>
            </div>

            <em
              v-if="index === 0 && isTabDisabled(index) && !isPoolingInProgress"
              v-b-tooltip.hover.top
              class="fas fa-info-circle info-tooltip ml-1"
              :title="
                $t('operationalStatistics.tabs.disabledCircuitTabTooltip')
              "
            ></em>
          </div>
        </div>

        <section class="data-section">
          <div
            v-if="isPoolingProgressVisible && poolingProgressDetails"
            class="pooling-progress"
          >
            <div class="pooling-progress-text">
              <p>
                {{ poolingProgressDetails.text }}
              </p>
            </div>
            <div class="pooling-progress-bar">
              <span>
                <ProgressBar
                  :percentage="parseInt(poolingProgressDetails.percentage)"
                />
              </span>
            </div>
            <div v-if="isPoolingInProgress" class="pooling-progress-cancel">
              <ButtonWrapper type="secondary" @click="handleCancelFetch">
                {{ $t('common.Annuler') }}
              </ButtonWrapper>
            </div>
          </div>

          <div v-if="moduleError" class="error-message">
            <p>{{ moduleError }}</p>
          </div>

          <StatsTable
            :row-data="rowData"
            :column-defs="columnDefs"
            :loading="isPoolingInProgress"
            :export-table-config="exportTableConfig"
            :has-results="hasResults"
            @grid-ready="onGridReady"
          />
        </section>
      </div>
    </template>
  </TLayout>
</template>

<script setup>
import { ref, provide, computed, onMounted, watch } from 'vue'
import store from '@/store'
import i18n from '@/i18n'
import TLayout from '@/components/shared/TLayout/TLayout.vue'
import Sidebar from '@/components/shared/Sidebar/Sidebar.vue'
import StatsFilters from '@/components/stats-module/StatsFilters.vue'
import StatsTable from '@/components/stats-module/StatsTable.vue'
import useOperationalStatisticsData from '@/composables/operationalStatistics/useOperationalStatisticsData'
import useToastComposable from '@/composables/toast'
import useOperationalStatisticsFilters from '@/composables/operationalStatistics/useOperationalStatisticsFilters'
import {
  adjustDateWithTimezone,
  formatDatetime,
  formatTime,
} from '@/utils/dates'
import envService from '@/services/env-service'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-alpine.css'
import ProgressBar from '@/components/shared/ProgressBar/ProgressBar.vue'
import { usePoolingControl } from '@/composables/usePoolingControl'
import ButtonWrapper from '@/components/shared/ButtonWrapper.vue'
import { useAutoSizeAgGridColumns } from '@/composables/useAutoSizeAgGridColumns'
import useOperationalStatisticsColumns from '@/composables/operationalStatistics/useOperationalStatisticsColumns'

provide('showCustomFiltersByDefault', true)

provide('searchFormTabs', [
  {
    label: i18n.t(`common.Véhicule`),
    value: 'vehicle',
  },
])

//Hide save selection button in search module
provide('showSaveSelectionButton', false)
//Hide time picker in SMDatePicker
provide('showTimePicker', false)

const gridApi = ref(null)
const columnApi = ref(null)
const currentDomLayout = ref('normal')

const { autoSizeAllColumns } = useAutoSizeAgGridColumns(
  gridApi,
  columnApi,
  currentDomLayout
)

const { requestCancel, cancelRequested } = usePoolingControl()

const { columnDefs } = useOperationalStatisticsColumns()

const { isStagging, isTestEnv, isPreprod, isBeta } = envService

const poolingProgressDetails = computed(
  () => store.state.operationalStatistics.poolingProgressDetails
)

const isPoolingProgressVisible = ref(false)
watch(
  () => poolingProgressDetails.value,
  (info) => {
    if (
      info.text === i18n.t('operationalStatistics.messages.poolingComplete')
    ) {
      setTimeout(() => {
        isPoolingProgressVisible.value = false
      }, 3000)
    } else {
      isPoolingProgressVisible.value = true
    }
  }
)

const fileName = 'OperationalStatisticsModule.vue'

const {
  selectedCircuits,
  selectedCircuitCategories,
  selectedVehicles,
  dateRange,
  currentFilterCriteria,
  syncSelectedVehiclesFromStore,
  dateRangeExceedsOneYear,
} = useOperationalStatisticsFilters()

const { poolData, exportDataToCSV, clearPooledDataCache } =
  useOperationalStatisticsData()

const hasCircuitBeenSearched = ref(false)
const hasSearchBeenPerformed = ref(false)

const onGridReady = (params) => {
  gridApi.value = params.api
  columnApi.value = params.columnApi
}

const rowData = computed(() => {
  return activeTab.value === 0
    ? store.getters['operationalStatistics/getOpStatsPooledData'] || []
    : store.getters['operationalStatistics/getOpStatsOperationalPooledData'] ||
        []
})

const tabs = [
  i18n.t('operationalStatistics.tabs.circuitView'),
  i18n.t('operationalStatistics.tabs.operationalView'),
]
const activeTab = computed({
  get: () => store.state.operationalStatistics.activeTab,
  set: (value) => {
    store.commit('operationalStatistics/SET_OP_STATS_ACTIVE_TAB', value)
  },
})

const ensureSearchModuleInitialized = async () => {
  if (!store.getters['search_module/isInitialLoadingComplete']) {
    console.log(
      `%c${fileName} ensureSearchModuleInitialized Triggering search_module initialization`,
      'color: orange'
    )
    await store.dispatch('search_module/initialize', {
      ignorePrevRequests: false,
    })
  }
}

onMounted(async () => {
  await store.dispatch('search_module/lazySync')
  syncSelectedVehiclesFromStore()
  console.log(
    `%c${fileName} onMounted - Initializing component and ensuring search_module is loaded`,
    'color: dodgerblue'
  )
  await ensureSearchModuleInitialized()
})

onUnmounted(() => {
  //Reset state when component is unmounted
  store.dispatch('operationalStatistics/initializeOperationalStatisticsModule')
})

const isPoolingInProgress = computed(
  () => store.state.operationalStatistics.isModuleLoading
)

const moduleError = computed(
  () => store.state.operationalStatistics.moduleError
)

const lastSearchedFilterCriteria = computed({
  get() {
    return store.state.operationalStatistics.lastSearchedFilterCriteria
  },
  set(value) {
    store.commit(
      'operationalStatistics/SET_LAST_SEARCHED_FILTER_CRITERIA',
      value
    )
  },
})

const { showToast } = useToastComposable({
  store,
  i18n,
})

const handlePoolData = async () => {
  syncSelectedVehiclesFromStore()
  const functionName = 'handlePoolData'

  const { selectedVehicles, dateRange } = lastSearchedFilterCriteria.value

  if (!selectedVehicles || selectedVehicles.length === 0) {
    showToast({
      type: 'warning',
      title: 'operationalStatistics.validation.required',
      text: 'operationalStatistics.validation.vehiclesRequired',
      timeout: 4000,
    })
    return
  }

  if (!dateRange || !dateRange.startDate || !dateRange.endDate) {
    showToast({
      type: 'warning',
      title: 'operationalStatistics.title',
      text: 'operationalStatistics.validation.dateRangeRequired',
      timeout: 4000,
    })
    return
  }

  if (store.getters['search_module/hasTodayInSelectedDateRanges']) {
    showToast({
      type: 'warning',
      title: 'operationalStatistics.title',
      text: 'operationalStatistics.validation.dateRangeContainsToday',
      timeout: 4000,
    })
    return
  }

  if (dateRangeExceedsOneYear.value) {
    showToast({
      type: 'warning',
      title: 'operationalStatistics.title',
      text: 'operationalStatistics.validation.dateRangeExceedsOneYear',
      timeout: 4000,
    })
    return
  }

  console.log(
    `%c${fileName} ${functionName} Pooling data with filters:`,
    'color: dodgerblue',
    {
      lastSearchedFilterCriteria: lastSearchedFilterCriteria.value,
    }
  )

  try {
    await poolData(lastSearchedFilterCriteria.value)
    //Auto-size columns after each pool
    autoSizeAllColumns()
  } catch (error) {
    console.error(
      `%c${fileName} ${functionName} Error during data pooling:`,
      'color: red',
      error
    )
  }
}

watch(
  () => store.getters['search_module/isInitialLoadingComplete'],
  (isComplete) => {
    if (!isComplete) {
      console.log(
        `%c${fileName} search_module re-detected as not initialized, re-triggering initialization.`,
        'color: orange'
      )
      ensureSearchModuleInitialized()
    }
  }
)

watch(
  activeTab,
  (newTab) => {
    autoSizeAllColumns()

    if (!hasSearchBeenPerformed.value) return

    const hasOperationalViewData =
      store.state.operationalStatistics.operationalPooledData?.length > 0

    const hasCircuitData =
      store.state.operationalStatistics.pooledData?.length > 0

    if (
      (newTab === 0 && !hasCircuitData) ||
      (newTab === 1 && !hasOperationalViewData)
    ) {
      handlePoolData()
    }
  },
  { immediate: false }
)

watch(isPoolingInProgress, (newValue) => {
  store.commit('search_module/setValidButtonDisabled', newValue)
})

const isTabDisabled = (index) => {
  if (isPoolingInProgress.value) return true
  if (index === 0)
    return (
      hasSearchBeenPerformed.value &&
      lastSearchedFilterCriteria.value.selectedCircuits.length === 0
    )
  return false
}

const onSearchModuleSelectionChange = async () => {
  hasCircuitBeenSearched.value = false
  hasSearchBeenPerformed.value = false

  await store.dispatch(
    'operationalStatistics/initializeOperationalStatisticsModule',
    false
  )

  lastSearchedFilterCriteria.value = {
    selectedVehicles: [...currentFilterCriteria.value.selectedVehicles],
    selectedCircuits: [...currentFilterCriteria.value.selectedCircuits],
    dateRange: { ...currentFilterCriteria.value.dateRange },
  }

  if (activeTab.value === 0 && selectedCircuits.value.length === 0) {
    activeTab.value = 1
  }

  await handlePoolData()

  hasCircuitBeenSearched.value = true
  hasSearchBeenPerformed.value = true
}

const onSearchClear = () => {
  selectedCircuits.value = []
  selectedCircuitCategories.value = []
}

const hasResults = computed(() => {
  return store.getters['operationalStatistics/hasResults']
})

const handleCancelFetch = () => {
  requestCancel()
}

const exportTableConfig = computed(() => ({
  tableCode: activeTab.value === 0 ? 'statsCircuit' : 'statsOperation',
}))

watch(
  () => cancelRequested.value,
  () => {
    if (cancelRequested.value) {
      isPoolingProgressVisible.value = false

      store.dispatch('operationalStatistics/setOpStatsLoading', false)

      showToast({
        type: 'warning',
        title: 'operationalStatistics.title',
        text: 'operationalStatistics.messages.poolingCancelled',
        timeout: 4000,
      })
    }
  }
)
</script>

<style scoped lang="scss">
.operational-statistics-module {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;

  .tabs {
    display: flex;
    justify-content: flex-start;

    .tab-wrapper {
      display: flex;
      align-items: center;
      margin-right: 10px;

      .tab {
        border-bottom: 3px solid transparent;

        span {
          color: var(--color-denim) !important;
          cursor: pointer;
        }
      }

      .tab.active {
        border-bottom: 3px solid var(--color-denim);
        border-radius: 0;
      }

      .tab.disabled {
        pointer-events: none;
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

.pooling-progress {
  display: flex;
  align-items: center;
  padding: 0 10px;
  width: 100%;
  background-color: var(--color-wild-sand);

  .pooling-progress-text {
    text-align: left;
    width: 25%;
  }

  .pooling-progress-bar {
    width: 65%;
  }

  .pooling-progress-cancel {
    width: 10%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    text-align: right;
  }
}

.pooling-progress p,
.error-message p {
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
}

.pooling-progress p {
  margin: 0;
  white-space: nowrap;
}

.pooling-progress span {
  flex-grow: 1; /* fills remaining space */
  max-width: 300px;
}

.error-message p {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.data-section {
  height: 100%;
}
</style>
