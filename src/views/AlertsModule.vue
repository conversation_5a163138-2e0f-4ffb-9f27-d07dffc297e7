<template>
  <TLayout
    :sync-with-vuex="false"
    :sidebar="true"
    :menu="true"
    :menu-collapsed="menuCollapsed"
    :menu-full-collapse="true"
    :menu-toggle="true"
    :right-menu="true"
    :right-menu-bottom="mode === 'table_map'"
  >
    <template #sidebar><Sidebar /></template>

    <template #menu>
      <SearchWrapper
        class="pb-4"
        @search-view-change="onSearchModuleViewChange"
        @search-clear="onSearchModuleClearSelection"
      >
        <template #search-module-filters>
          <div class="filter_item">
            <label class="filter_label">
              <span>{{ $t('alerts.table_column.type') }}</span>
              <i style="color: red">&nbsp;*</i>
            </label>
            <BetterSelect
              v-model="filters.selectedAlertTypes"
              :options="computedAlertTypes"
              :multiple="true"
              :style-overrides="getBetterSelectStyleOverrides"
            />
          </div>
          <div class="d-flex justify-content-center">
            <ButtonWrapper
              :transparent="true"
              icon="mdi:checkbox-marked"
              :custom-icon-style="{ fontSize: '20px' }"
              @click="selectAllAlertTypes"
            >
              {{ $t('common.all') }}
              <em
                v-b-tooltip.hover.top
                class="fas fa-info info-tooltip"
                :title="$t('searchModule.select_all')"
              ></em>
            </ButtonWrapper>
            <ButtonWrapper
              :transparent="true"
              icon="mdi:checkbox-blank-outline"
              :custom-icon-style="{ fontSize: '20px' }"
              @click="deselectAllAlertTypes"
            >
              {{ $t('common.none') }}
              <em
                v-b-tooltip.hover.top
                class="fas fa-info info-tooltip"
                :title="$t('searchModule.deselect_all')"
              ></em>
            </ButtonWrapper>
          </div>
        </template>

        <template #search-module-results>
          <div
            v-show="!loading && sortedItems.length === 0"
            class="row m-0 p-0"
          >
            <div class="col-12">
              <span v-show="itemsSize === 0">{{
                $t('alerts.no_results')
              }}</span>
              <span v-show="itemsSize !== 0">{{
                $t('alerts.no_results_after_filter')
              }}</span>
            </div>
          </div>
          <AlertListToolbar
            v-show="sortedItems.length > 0"
            :show-filter="hasAckNoAck"
            @map="() => switchMode('table_map')"
            @table="() => switchMode('table')"
            @sorting="onSorting"
            @filter="onFiltering"
          />
          <AlertList :items="sortedItems" :loading="loading" />
          <LoaderSpinner v-show="loading" class="mt-2" />
        </template>
      </SearchWrapper>
    </template>

    <template #right_menu>
      <div v-if="mode === 'table'" class="table-layout">
        <AlertStats :items="sortedItems" />
        <AlertTable
          :items="sortedItems"
          :header-text="tableHeaderText"
          :mode="mode"
          @mode="switchMode"
        />
      </div>
      <AlertMap v-if="mode === 'table_map' || mode === 'list'" />
    </template>

    <template #right_menu_bottom>
      <AlertTable
        v-if="
          mode === 'table_map' && (!loading || (loading && itemsSize < 500))
        "
        :mode="mode"
        :items="sortedItems"
        :header-text="tableHeaderText"
        @mode="switchMode"
      />
    </template>
  </TLayout>
</template>

<script setup>
import store from '@/store'
import mitt from '@/plugins/mitt.js'
import loader from '@/plugins/loader'
import { isVerboseMode } from '@/services/env-service.js'
import { useProgresiveDatatableLoader } from '@c/shared/DataTable/DatatableLoader.vue'
import Sidebar from '@c/shared/Sidebar/Sidebar.vue'
import SearchWrapper from '@c/shared/SearchModule/SearchWrapper/SearchWrapper.vue'
import TLayout from '@c/shared/TLayout/TLayout.vue'
import AlertList from '@c/alerts/AlertList.vue'
import AlertListToolbar from '@c/alerts/AlertListToolbar.vue'
import AlertTable from '@c/alerts/AlertTable.vue'
import AlertStats from '@c/alerts/AlertStats.vue'
import AlertMap from '@c/alerts/AlertMap.vue'
import moment from 'moment'
import searchService from '@/services/search-service.js'
import {
  computeStatusLabel,
  fetchAlertsProgressive,
  acknowledgeAlert,
  fetchAlertTypes,
  getSearchSelectionLimit,
} from '@/services/alert-service.js'
import i18n from '@/i18n'
import { normalizeString } from '@/utils/string.js'
import { throttleSmart } from '@/utils/async'
import { getEnvIntValue } from '@/services/env-service'
import { createOperationCancelManager } from '@/utils/workflow'
import { generateShortId } from '@/utils/crypto.js'
import { apiCacheStorage } from '@/api/api-cache'
import { getCurrentInstance } from 'vue'
import { useBatchUpdate } from '@/composables/useBatchUpdate'
import BetterSelect from '@/components/shared/BetterSelect.vue'
import ButtonWrapper from '@/components/shared/ButtonWrapper.vue'

// Refs
const updatingState = ref(false)
const mode = ref('list')
const menuCollapsed = ref(false)
const isAutomaticSearch = ref(false)
const startDateFormatted = ref('')
const endDateFormatted = ref('')
const sortingType = ref('NONE')
const filterType = ref('NONE')
const itemsSize = ref(0)
const sortedItems = ref([])
const loading = ref(false)
const isUnmounted = ref(false)
const itemsCache = ref([])
const worker = ref(null)
const hasAckNoAck = ref(false)
const instance = getCurrentInstance()
//Previously this._rcount
const rcount = ref(0)
//useVsb composable replaces deprecated useVsb mixin
const { batchUpdateState, batchUpdateAddData } = useBatchUpdate()

const filters = reactive({
  alertStatus: '',
  alertStatusList: [
    { text: i18n.t('alerts.main_search.results_filter.none'), value: '' },
    { text: i18n.t('alerts.filters.ack'), value: 2 },
    { text: i18n.t('alerts.filters.noack'), value: 1 },
  ],
  get selectedAlertTypes() {
    return store.getters['search_module/getSelectedAlertTypes']
  },
  set selectedAlertTypes(value) {
    store.commit('search_module/setSelectedAlertTypes', value)
  },
  alertTypes: [],
})

const tableProgressiveLoader = useProgresiveDatatableLoader()
const searchCancelManager = createOperationCancelManager('alerts search')

if (window.Worker) {
  worker.value = new Worker('/workers/alerts-worker.js')
}

const getNoneOption = () => ({
  text: i18n.t('alerts.main_search.results_filter.none'),
  value: '',
})

const maxItemsToShowWhenLoading = getEnvIntValue(
  'VITE_EVENTS_MAX_ITEMS_TO_SHOW_WHEN_LOADING',
  2000
)

const isDatatableLoading = computed(
  () => loading.value && itemsSize.value === 0
)

provide('searchModuleDatetimePickerLabel', 'alerts.main_datetime_picker_label')
provide(
  'searchModuleDatetimePickerPlaceholder',
  'alerts.main_datetime_picker_placeholder'
)
provide('searchModuleDatetimeRequired', true)
provide('searchModuleCanToggleFreesearch', false)
provide('searchFormTabs', [
  { label: i18n.t('common.Véhicule'), value: 'vehicle' },
])
provide('vueDataTableDrawOptions', {
  showLoader: true,
  debug: isVerboseMode('table') || isVerboseMode('alerts'),
  asyncRender: {
    batchSize: 250,
    drawOnce: true,
    progresiveLoader: true,
  },
  cooldownTimeoutMs: 5000,
})

const alertMarkerPopupOptions = (options = {}) => {
  return {
    popupOptions: {
      style: 'min-width:267px;',
    },
    popup: options.popup || (() => import('@c/alerts/AlertMapMarkerPopup.vue')),
  }
}

const acknowledgeAlertHandler = async (messageId) => {
  const promise = acknowledgeAlert(messageId)
  // Show loading indication
  await loader.show(promise)
  const r = await promise
  if (r.data && (r.data.errors || r.data.error)) {
    await store.dispatch('alert/addAlert', {
      type: 'warning',
      title: i18n.t('alerts.actions_ack.title'),
      text: i18n.t('alerts.actions_ack.fail'),
    })
  } else {
    await store.dispatch('alert/addAlert', {
      type: 'info',
      title: i18n.t('alerts.actions_ack.title'),
      text: i18n.t('alerts.actions_ack.success'),
    })

    let match = sortedItems.value.find((item) => item.message_id === messageId)

    if (match) {
      match.isAck = true
      match.statusLabel = computeStatusLabel(match.isAck)
      hasHackNoHackData()
      itemsCache.value.length = 0
      itemsCache.value.push(...sortedItems.value)
      await apiCacheStorage.invalidateCacheByKeyInclude('messages_alertes')
    }
  }
  const match = sortedItems.value.find((item) => item.id === messageId)
  if (match) {
    match.isAck = true
    match.statusLabel = computeStatusLabel(match.isAck)
    hasHackNoHackData()
    itemsCache.value.length = 0
    itemsCache.value.push(...sortedItems.value)
    await apiCacheStorage.invalidateCacheByKeyInclude('messages_alertes')
  }
}

const handleListItemZoomClick = (lat, lng) => {
  mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [[lat, lng]])
}

provide('isDatatableLoading', isDatatableLoading)
provide('alertMarkerPopupOptions', alertMarkerPopupOptions)
provide('acknowledgeAlert', acknowledgeAlertHandler)
provide('handleListItemZoomClick', handleListItemZoomClick)

//Computed
const searchModuleValidatedTimestamp = computed(
  () => store.getters['search_module/getValidatedTimestamp']
)
const selection = computed(() => store.getters['search_module/getSelection'])

const computedAlertTypes = computed(() => {
  return filters.alertTypes.map((item) => {
    const input = item.text
    const options = {
      replaceSpaces: true,
      convertToLowercase: true,
      removeParentheses: true,
      removeAccents: true,
      prefix: 'alert_types.',
    }

    const i18nCode = normalizeString(input, options)
    const output = i18n.te(i18nCode) ? i18n.t(i18nCode) : input // Fallback to original value if i18n code not found

    return {
      name: output,
      id: item.value,
    }
  })
})

const tableHeaderText = computed(() => {
  return startDateFormatted.value && endDateFormatted.value
    ? i18n.t('alerts.table_header_text_html', {
        fromDate: startDateFormatted.value,
        toDate: endDateFormatted.value,
      })
    : ''
})

const isSearchModuleValidSelection = computed(() => {
  return store.getters['search_module/isValidSelection']
})

const getBetterSelectStyleOverrides = computed(() => {
  return store.getters['search_module/getBetterSelectStyleOverrides']
})

const handleFilterChange = (newFiltersValue) => {
  let shouldDisableButton = false

  if (newFiltersValue.selectedAlertTypes.length < 1) {
    shouldDisableButton = true
  }

  store.commit('search_module/setValidButtonDisabled', shouldDisableButton)
}

//Watchers
watch(
  filters,
  (newValue) => {
    handleFilterChange(newValue)
  },
  { immediate: true, deep: true }
)

watch(isAutomaticSearch, (value) => {
  console.debugVerbose(8, 'watch isAutomaticSearch', value)
  if (!value) {
    searchCancelManager.cancelAnyOperationInProgress()
  }
})

watch(isSearchModuleValidSelection, (value) => {
  console.debugVerbose(8, 'watch isSearchModuleValidSelection', value)
  if (value) {
    isAutomaticSearch.value = false
  }
})

watch(sortingType, () => {
  if (loading.value) {
    return
  }
  updateItemsThrottle()
})

watch(filterType, () => {
  if (loading.value) {
    return
  }
  updateItemsThrottle()
})

watch(
  selection,
  () => {
    // User interaction on search selection should stop automatic search
    isAutomaticSearch.value = false
  },
  { deep: true }
)

watch(searchModuleValidatedTimestamp, () => {
  //The method is triggered automatically when a filter has been done on another module
  if (store.state.search_module.validatedAt === 0) {
    return
  }
  isAutomaticSearch.value = false
  fetchAlertsAndUpdateProgressively()
})

// onBeforeMount
onBeforeMount(() => {
  store.dispatch('search_module/resetStore', {
    origin: 'alerts/created',
  })

  //TODO fix this
  window._am = {
    itemsCache: itemsCache.value,
    vm: instance?.proxy,
  }
})

// onMounted
onMounted(async () => {
  cancelSearchsAndResetState()

  if (
    isAutomaticSearch.value &&
    getEnvIntValue('alertsSearchDisableAutomaticSearch') !== 1
  ) {
    await performAutomaticSearch()
  }

  // Fetch alert types and update filters
  const alertTypes = await fetchAlertTypes()
  filters.alertTypes = alertTypes.map((item) => ({
    text: item.label,
    value: item.id,
  }))

  // Set chunk and batch size
  batchUpdateState.chunkSize = 1000
  batchUpdateState.batchSize = 500

  await store.dispatch('selection_templates/setSelectedTemplate', null)
})

//onBeforeUnmount
onBeforeUnmount(() => {
  // Hide loader
  loader.hide

  // Clear items cache and reset state
  itemsCache.value.length = 0
  store.dispatch('search_module/setHasResults', false)
  store.dispatch('search_module/resetStore', { origin: 'alerts/destroyed' })
  store.dispatch('simpliciti_map/resetStore', null)

  // Reset automatic search flag
  isAutomaticSearch.value = false

  // Commit store mutations
  store.commit('search_module/setValidButtonDisabled', false)

  store.dispatch('selection_templates/setSelectedTemplate', null)

  isUnmounted.value = true
})

//Methods
const setMapDataset = throttleSmart(async () => {
  const track = console.trackTime('setMapDataset')

  await store.dispatch(
    'simpliciti_map/setDataset',
    {
      type: 'alertsMarkers',
      data: [...sortedItems.value],
    },
    { root: true }
  )

  track.stop()
})

const updateItemsThrottle = throttleSmart(async function (partialData = null) {
  return await updateItems(partialData || itemsCache.value, !!partialData)
}, 1000)

const getFilteredSortedData = (cachedItems) =>
  cachedItems
    .filter((item) => {
      if (filterType.value === 'ACK') {
        return item.isAck
      }
      if (filterType.value === 'NOACK') {
        return !item.isAck
      }
      return true
    })
    .sort((a, b) => {
      switch (sortingType.value) {
        case 'NONE':
        case 'RECENT':
          return a.timestamp > b.timestamp ? -1 : 1
        case 'NOACK':
          return !(a.isAck && !b.isAck) ? -1 : 1
        case 'ACK':
          return a.isAck && !b.isAck ? -1 : 1
        case 'TYPE':
          return a.type < b.type ? -1 : 1
        case 'NAME':
          return a.title < b.title ? -1 : 1
        case 'VEHICLE':
          return a.vehicleLabel < b.vehicleLabel ? -1 : 1
        case 'VEHICLE_CATEGORY':
          return a.vehicleCategory < b.vehicleCategory ? -1 : 1
      }
    })

const updateItems = async (cachedItems, isPartialUpdate) => {
  if (updatingState.value) {
    console.warn('Update state overlap', cachedItems.length, isPartialUpdate)
  }

  updatingState.value = true

  const withData = async (data) => {
    await batchUpdateAddData(
      'sortedItems',
      data,
      { sortedItems },
      (newItems, items) => {
        let filtered = newItems.filter((newItem) => {
          return !items.some((i) => i.id === newItem.id)
        })
        if (newItems.length !== filtered.length) {
          console.warn('Items duplication detected', {
            newItems,
            items,
            filtered,
          })
        }

        return filtered
      }
    )
  }

  return new Promise(async (resolve, reject) => {
    const track = console.trackTime('updateItems')
    track.count('cachedItems', { count: cachedItems.length })
    track.count(isPartialUpdate ? 'partialUpdate' : 'fullUpdate')

    if (!isPartialUpdate) {
      sortedItems.value = []
    }

    if (!worker.value) {
      const data = getFilteredSortedData(cachedItems)

      await withData(
        loading.value
          ? data.slice(0, Math.min(maxItemsToShowWhenLoading, itemsSize.value))
          : data
      )
      track.stop()
      updatingState.value = false
      resolve()
    } else {
      const id = generateShortId()
      let workerTrack = null
      const handler = async (event) => {
        if (event.data.id !== id) {
          return
        }
        workerTrack.stop()
        await withData(
          loading.value
            ? event.data.items.slice(
                0,
                Math.min(maxItemsToShowWhenLoading, itemsSize.value)
              )
            : event.data.items
        )
        worker.value.removeEventListener('message', handler)
        track.stop()
        updatingState.value = false
        resolve()
      }
      worker.value.addEventListener('message', handler)
      workerTrack = console.trackTime('workerTime')
      worker.value.postMessage({
        action: 'filterSort',
        params: {
          id,
          cachedItems,
          filterType: filterType.value,
          sortingType: sortingType.value,
        },
      })
    }
  })
}

const switchMode = (switchMode) => {
  mode.value = switchMode
}

const onFiltering = (filterTypeParam) => {
  filterType.value = filterTypeParam
}

const onSorting = (sortingTypeParam) => {
  sortingType.value = sortingTypeParam
}

const performAutomaticSearch = async () => {
  await searchService.waitUntilInitialized('vehicles')

  const allVehicleIds = store.getters['search_module/getVehicles'].map(
    (i) => i.id
  )

  const dateRanges = [[moment().subtract(24, 'hours')._d, moment()._d]]

  console.debugVerbose(8, 'performAutomaticSearch', {
    vehicleIdsCount: allVehicleIds.length,
    dateRanges,
  })

  await fetchAlertsAndUpdateProgressively({
    vehicleIds: allVehicleIds,
    dateRanges,
    isAutomaticSearch: true,
  })
}

const fetchAlertsAndUpdateProgressively = async (options = {}) => {
  if (
    searchCancelManager.thereArePendingOperations() &&
    !options.isAutomaticSearch
  ) {
    cancelSearchsAndResetState()
  }

  if (isUnmounted.value) {
    console.warn('Unmounted')
    return
  }

  const searchItem = searchCancelManager.createNew()

  let vehicleIds =
    options.vehicleIds || selection.value.selectedVehiclesIds || []
  let dateRanges =
    options.dateRanges || selection.value.selectedDateRanges || []

  if (dateRanges.length > 0) {
    dateRanges = dateRanges
      .map((range) => (range instanceof Array ? range[0] : range))
      .sort((a, b) => ((a._d || a).getTime() < (b._d || b).getTime() ? 1 : -1))
  }

  // Validation: Limit vehicle selection
  if (
    options.isAutomaticSearch !== true &&
    vehicleIds.length > getSearchSelectionLimit()
  ) {
    await store.dispatch('alert/addAlert', {
      type: 'warning',
      title: i18n.t('common.main_search.selection_limit_title'),
      text: i18n.t('common.main_search.selection_limit_text', {
        current: vehicleIds.length,
        limit: getSearchSelectionLimit(),
      }),
    })
    return
  }

  if (dateRanges.length === 0) {
    dateRanges = [[moment().startOf('day')._d, moment().endOf('day')._d]]
  }

  store.state.search_module.isSearchInProgress = true

  const startDate = dateRanges[0][0]
  const endDate = dateRanges[dateRanges.length - 1][1]

  //Temp fix before full migration to Vue 3 : use instance.proxy to access $date
  startDateFormatted.value =
    instance?.proxy?.$date.formatDatetimeWithSeconds(startDate)
  endDateFormatted.value =
    instance?.proxy?.$date.formatDatetimeWithSeconds(endDate)

  itemsSize.value = 0
  itemsCache.length = 0
  sortedItems.value = []

  await setMapDataset()

  loading.value = true

  nextTick(() => {
    //Switch to results view but wait next tick so that loading equals true shows a loader
    store.dispatch('search_module/setHasResults', true)
  })

  store.state.datatable.disablePaginationVisually = true

  fetchAlertsProgressive({
    vehicleIds: [...vehicleIds],
    startDate,
    endDate,
    dateRanges,
    types: filters.selectedAlertTypes
      .filter((item) => item && typeof item.id !== 'undefined')
      .map((item) => item.id),
    handleResults: async (results) => {
      rcount.value = rcount.value || 0
      rcount.value += results.length

      if (isUnmounted.value || !searchItem.shouldContinue()) {
        cancelSearchsAndResetState()
        return false
      }

      itemsSize.value += results.length
      if (results.length > 0) {
        tableProgressiveLoader.show('handler')

        itemsCache.value.push(...results)

        console.log('items cache : ', itemsCache.value)

        await updateItems(results, true)
      }

      if (!searchItem.shouldContinue()) {
        return cancelSearchsAndResetState()
      }

      if (itemsSize.value > 0) {
        await store.dispatch('search_module/setHasResults', true)
      }
    },
  }).finally(async () => {
    loading.value = false
    store.state.datatable.disablePaginationVisually = false
    store.state.search_module.isSearchInProgress = false

    tableProgressiveLoader.hide('finally')

    if (!searchItem.shouldContinue()) {
      return cancelSearchsAndResetState()
    }

    hasHackNoHackData()

    await setMapDataset()

    if (itemsSize.value === 0) {
      switchMode('table')
    }
  })
}

const onSearchModuleViewChange = (view) => {
  if (view === 'selection') {
    // User interaction on the input search will turn off automatic search
    isAutomaticSearch.value = false
    filterType.value = 'NONE'

    if (searchCancelManager.thereArePendingOperations()) {
      cancelSearchsAndResetState()
    }

    handleFilterChange(filters)
  }
}

const cancelSearchsAndResetState = () => {
  loading.value = false

  searchCancelManager.cancelAnyOperationInProgress()

  store.state.datatable.disablePaginationVisually = false

  menuCollapsed.value = false

  if (itemsSize.value === 0) {
    store.dispatch('search_module/setHasResults', false)
  }

  store.state.search_module.view = 'selection'
  store.state.search_module.isSearchInProgress = false

  store.commit('search_module/setValidButtonDisabled', true)

  tableProgressiveLoader.hide('reset-state')
}

const onSearchModuleClearSelection = () => {
  filters.selectedAlertTypes = []
}

const hasHackNoHackData = () => {
  hasAckNoAck.value = false

  let hasAck = false
  let hasNoAck = false

  if (sortedItems.value) {
    sortedItems.value.forEach((item) => {
      if (item.isAck) {
        hasAck = true
      } else {
        hasNoAck = true
      }
      if (hasAck && hasNoAck) {
        return false
      }
    })
  }

  hasAckNoAck.value = hasAck && hasNoAck
}

const selectAllAlertTypes = () => {
  filters.selectedAlertTypes = [...computedAlertTypes.value]
}

const deselectAllAlertTypes = () => {
  filters.selectedAlertTypes = []
}
</script>

<style lang="scss" scoped>
.filter_item {
  margin-top: 10px;
  padding: 0 20px 0 23px;
}

.filter_label {
  font: normal normal normal 10px/14px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
  margin: 0px;
}

.table-layout {
  height: calc(100% - 150px);
}

.info-tooltip {
  margin-left: 5px;
  margin-right: 5px;
  background: var(--color-main);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  text-align: center;
  color: white;
  line-height: 12px;
  font-size: 8px;
}

select.select {
  box-shadow: none;

  &:hover {
    border-color: #409aff;
  }

  &:not(.no-chevron) {
    background: white
      url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='transparent'/></svg>")
      no-repeat;
    background-position-x: calc(100% - 2px);
    background-position-y: 3px;
  }
}
</style>
