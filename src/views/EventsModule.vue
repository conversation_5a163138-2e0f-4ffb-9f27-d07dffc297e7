<template>
  <TLayout
    :sync-with-vuex="false"
    :sidebar="true"
    :menu="true"
    :menu-collapsed="menuCollapsed"
    :menu-full-collapse="true"
    :menu-toggle="true"
    :right-menu="true"
    :right-menu-bottom="mode === 'table_map'"
    @onMenuCollapsed="(value) => (menuCollapsed = value)"
  >
    <template #sidebar>
      <Sidebar></Sidebar>
    </template>

    <template #menu>
      <SearchWrapper
        class="pb-4"
        @search-input="onSearchModuleSelectionChange"
        @search-view-change="onSearchModuleViewChange"
        @search-clear="onSearchModuleClearSelection"
      >
        <template #search-module-filters>
          <div class="row p-0 m-0 px-4">
            <div
              v-show="operationAnomaliesTypes.length > 0"
              class="col-12 m-0 mt-2 p-0"
            >
              <div class="filter_item">
                <label class="filter-label">
                  <span>{{ $t('events.search_filters.anomaly_title') }}</span>
                </label>
                <BetterSelect
                  v-model="selectedAnomaliesTypes"
                  :options="operationAnomaliesTypesComputed"
                  :multiple="true"
                  :style-overrides="getBetterSelectStyleOverrides"
                />
              </div>
              <div class="d-flex justify-content-center">
                <ButtonWrapper
                  :transparent="true"
                  icon="mdi:checkbox-marked"
                  :custom-icon-style="{ fontSize: '20px' }"
                  @click="selectAllAnomaliesTypes"
                >
                  {{ $t('common.all') }}
                  <em
                    v-b-tooltip.hover.top
                    class="fas fa-info info-tooltip"
                    :title="$t('searchModule.select_all')"
                  ></em>
                </ButtonWrapper>
                <ButtonWrapper
                  :transparent="true"
                  icon="mdi:checkbox-blank-outline"
                  :custom-icon-style="{ fontSize: '20px' }"
                  @click="deselectAllAnomaliesTypes"
                >
                  {{ $t('common.none') }}
                  <em
                    v-b-tooltip.hover.top
                    class="fas fa-info info-tooltip"
                    :title="$t('searchModule.deselect_all')"
                  ></em>
                </ButtonWrapper>
              </div>
            </div>
            <div
              v-show="operationMessageTypes.length > 0"
              class="col-12 m-0 mt-2 p-0"
            >
              <div class="filter_item">
                <label class="filter-label">
                  <span>{{
                    $t('events.search_filters.operation_message_title')
                  }}</span>
                </label>
                <BetterSelect
                  v-model="selectedOperationMessageTypes"
                  :options="operationMessageTypesComputed"
                  :multiple="true"
                  :style-overrides="getBetterSelectStyleOverrides"
                />
              </div>
              <div class="d-flex justify-content-center">
                <ButtonWrapper
                  :transparent="true"
                  icon="mdi:checkbox-marked"
                  :custom-icon-style="{ fontSize: '20px' }"
                  @click="selectAllOperationMessageTypes"
                >
                  {{ $t('common.all') }}
                  <em
                    v-b-tooltip.hover.top
                    class="fas fa-info info-tooltip"
                    :title="$t('searchModule.select_all')"
                  ></em>
                </ButtonWrapper>
                <ButtonWrapper
                  :transparent="true"
                  icon="mdi:checkbox-blank-outline"
                  :custom-icon-style="{ fontSize: '20px' }"
                  @click="deselectAllOperationMessageTypes"
                >
                  {{ $t('common.none') }}
                  <em
                    v-b-tooltip.hover.top
                    class="fas fa-info info-tooltip"
                    :title="$t('searchModule.deselect_all')"
                  ></em>
                </ButtonWrapper>
              </div>
            </div>
            <div
              v-show="operationStatusTypes.length > 0"
              class="col-12 m-0 mt-2 p-0"
            >
              <div class="filter_item">
                <label class="filter-label">
                  <span>{{ $t('events.search_filters.status_title') }}</span>
                </label>
                <BetterSelect
                  v-model="selectedStatusTypes"
                  :options="operationStatusTypesComputed"
                  :multiple="true"
                  :style-overrides="getBetterSelectStyleOverrides"
                />
              </div>
              <div class="d-flex justify-content-center">
                <ButtonWrapper
                  :transparent="true"
                  icon="mdi:checkbox-marked"
                  :custom-icon-style="{ fontSize: '20px' }"
                  @click="selectAlloperationStatusTypes"
                >
                  {{ $t('common.all') }}
                  <em
                    v-b-tooltip.hover.top
                    class="fas fa-info info-tooltip"
                    :title="$t('searchModule.select_all')"
                  ></em>
                </ButtonWrapper>
                <ButtonWrapper
                  :transparent="true"
                  icon="mdi:checkbox-blank-outline"
                  :custom-icon-style="{ fontSize: '20px' }"
                  @click="deselectAlloperationStatusTypes"
                >
                  {{ $t('common.none') }}
                  <em
                    v-b-tooltip.hover.top
                    class="fas fa-info info-tooltip"
                    :title="$t('searchModule.deselect_all')"
                  ></em>
                </ButtonWrapper>
              </div>
            </div>
          </div>
        </template>

        <template #search-module-results>
          <div
            v-show="
              listItems.length === 0 &&
              !$store.state.search_module.isSearchInProgress
            "
            class="row m-0 p-0"
          >
            <div class="col-12">
              <span>{{ $t('events.no_results') }}</span>
            </div>
          </div>
          <EventsList
            v-show="listItems.length > 0"
            ref="il1"
            :items="listItems"
            :highlighted-selection-id="highlightedSelectionId"
            @toolbar="onToolbarAction"
          ></EventsList>
          <LoaderSpinner v-show="loading" class="mt-2"></LoaderSpinner>
        </template>
      </SearchWrapper>
    </template>

    <template #right_menu>
      <div v-if="mode === 'table'" class="table-layout">
        <EventsTable
          :mode="mode"
          :header-text="tableHeaderText"
          :show-round-columns="isAggregateEventGroupRoundEnabled"
          :show-bin-columns="isAggregateEventGroupBinEnabled"
          @mode="switchMode"
        ></EventsTable>
      </div>
      <LocationEventsMap
        v-if="mode === 'table_map' || mode === 'list'"
        ref="map"
      ></LocationEventsMap>
    </template>

    <template #right_menu_bottom>
      <EventsTable
        v-if="mode === 'table_map'"
        :mode="mode"
        :header-text="tableHeaderText"
        :show-round-columns="isAggregateEventGroupRoundEnabled"
        :show-bin-columns="isAggregateEventGroupBinEnabled"
        @mode="switchMode"
      ></EventsTable>
    </template>
  </TLayout>
</template>
<script>
import { useProgresiveDatatableLoader } from '@c/shared/DataTable/DatatableLoader.vue'
import Vue from 'vue'
import EventsTable from '@/components/events/EventsTable.vue'
import LocationEventsMap from '@/components/location/LocationEvents/LocationEventsMap.vue'
import EventsList from '@/components/events/EventsList.vue'
import Sidebar from '@c/shared/Sidebar/Sidebar.vue'
import TLayout from '@c/shared/TLayout/TLayout.vue'
import SearchWrapper from '@c/shared/SearchModule/SearchWrapper/SearchWrapper.vue'
import { mapGetters } from 'vuex'
import { getEventsSearchSelectionLimit } from '@/services/search-service.js'
import { getQueryStringValue } from '@/utils/querystring'
import moment from 'moment'
import i18n from '@/i18n'
import {
  fetchEvents,
  fetchEventsCumulation,
  getEventTypesCategories,
} from '@/services/events-service.js'
import { getEnvValue } from '@/services/env-service.js'
import { normalizeString } from '@/utils/string.js'

const canGroupEventsByAll = getEnvValue('events_enable_group_all', '1') === '1' //Allow user to select bin and round (group=all) (Enabled by default)

const tableProgressiveLoader = useProgresiveDatatableLoader()

export const EventsModuleIsolatedConfig = [
  'EventsModule',
  () =>
    /* webpackChunkName: "main"    */
    import('@/views/EventsModule.vue'),
  {
    props: {
      //Custom props
    },
    beforeEnter: (to, from, next) => {
      //Custom logic before routing in
      return !!next && next()
    },
  },
]

function eventsFilterMixin() {
  return {
    data() {
      return {
        //Filters data
        operationMessageTypes: [],
        operationAnomaliesTypes: [],
        operationStatusTypes: [],

        loading: false, //Controls the loader spinner for the list (search 1) (sythesis)
        searchAborted: false,
      }
    },
    computed: {
      isAggregateEventGroupBinEnabled() {
        return this.$store.getters['settings/getParameter'](
          'isAggregateEventGroupBinEnabled'
        )
      },
      isAggregateEventGroupRoundEnabled() {
        return this.$store.getters['settings/getParameter'](
          'isAggregateEventGroupRoundEnabled'
        )
      },
      selectedAggregateEventGroups() {
        if (
          this.isAggregateEventGroupBinEnabled &&
          this.isAggregateEventGroupBinEnabled &&
          canGroupEventsByAll
        ) {
          return 'all'
        }
        if (this.isAggregateEventGroupBinEnabled) return 'bin'
        if (this.isAggregateEventGroupRoundEnabled) return 'round'
        return ''
      },
      /**
       * Translate label on the fly
       */
      operationStatusTypesComputed() {
        const seenValues = new Set()

        return this.operationStatusTypes
          .filter((item) => {
            if (seenValues.has(item.value)) {
              return false
            }
            seenValues.add(item.value)
            return true
          })
          .map((item) => {
            return {
              id: item.value,
              name: getI18nLabelForEventTypeCategoryLabel(
                item.text,
                'event_types.status.'
              ),
            }
          })
      },
      operationAnomaliesTypesComputed() {
        return this.operationAnomaliesTypes.map((item) => {
          return {
            id: item.value,
            name: getI18nLabelForEventTypeCategoryLabel(
              item.text,
              'event_types.anomaly.'
            ),
          }
        })
      },
      operationMessageTypesComputed() {
        return this.operationMessageTypes.map((item) => {
          return {
            id: item.value,
            name: getI18nLabelForEventTypeCategoryLabel(
              item.text,
              'event_types.operation_message.'
            ),
          }
        })
      },
      ...mapGetters({
        getSelectedAnomaliesTypes: 'search_module/getSelectedAnomaliesTypes',
        getSelectedOperationMessageTypes:
          'search_module/getSelectedOperationMessageTypes',
        getSelectedStatusTypes: 'search_module/getSelectedStatusTypes',
      }),
      selectedAnomaliesTypes: {
        get() {
          return this.getSelectedAnomaliesTypes
        },
        set(value) {
          this.$store.commit('search_module/setSelectedAnomaliesTypes', value)
        },
      },
      //Custom search filters
      selectedOperationMessageTypes: {
        get() {
          return this.getSelectedOperationMessageTypes
        },
        set(value) {
          this.$store.commit(
            'search_module/setSelectedOperationMessageTypes',
            value
          )
        },
      },
      selectedStatusTypes: {
        get() {
          return this.getSelectedStatusTypes
        },
        set(value) {
          this.$store.commit('search_module/setSelectedStatusTypes', value)
        },
      },
    },
    methods: {
      /*       aggregateEventGroupSelect(e, type) {
        if (e.target.value && type === 'round' && !canGroupEventsByAll) {
          this.isAggregateEventGroupBinEnabled = false
        }
        if (e.target.value && type === 'bin' && !canGroupEventsByAll) {
          this.isAggregateEventGroupRoundEnabled = false
        }
      }, */
    },
    async created() {
      await this.$store.dispatch('location_module/isACitiMissionActiveClient')
      getEventTypesCategories((dataSubset) => {
        const transformEventTypeCategoryItem = (item) => ({
          value: item.code,
          text: item.label,
        })
        dataSubset.forEach((item) => {
          switch (item.type) {
            case 'operation_message':
              this.operationMessageTypes.push(
                transformEventTypeCategoryItem(item)
              )
              break
            case 'anomaly':
              this.operationAnomaliesTypes.push(
                transformEventTypeCategoryItem(item)
              )
              break
            case 'status':
              this.operationStatusTypes.push(
                transformEventTypeCategoryItem(item)
              )
              break
            default:
              break
          }
        })

        /* 
        //Generate a JSON with missing keys
        let json = {}
        this.operationAnomaliesTypes.forEach((item) => {
          let i18nCode = getI18nLabelForEventTypeCategoryLabel(item.text, 'event_types.anomaly.',true)
          let exists = i18n.te(i18nCode)
          if (!exists) {
            json[i18nCode] = ''
          }
        })
        console.warn('Missing event types operationAnomaliesTypes i18n codes')
        console.log(JSON.stringify(json, null, 4))*/
      })
    },
  }
}

function getI18nLabelForEventTypeCategoryLabel(
  text,
  prefix,
  returnCodeInstead = false
) {
  const input = text
  const options = {
    replaceSpaces: true,
    convertToLowercase: true,
    removeParentheses: true,
    removeAccents: true,
    prefix,
  }

  const i18nCode = normalizeString(input, options)
  if (returnCodeInstead) {
    return i18nCode
  }
  const exists = i18n.te(i18nCode)
  if (exists) {
    //console.warn('i18n code found', i18nCode, text, i18n.t(i18nCode))
    return i18n.t(i18nCode)
  } else {
    //console.warn('i18n code not found', i18nCode, text)
    return text
  }
}

/**
 * Computes "tableHeaderText"
 */
function tableHeaderMixin() {
  return {
    data() {
      return {
        //Necessary to compute table header text
        dateFromFormatted: '',
        dateToFormatted: '',
        //detailedItemsBy: external
        //detailedItemsByItem: external
      }
    },
    computed: {
      tableHeaderText() {
        if (this.detailedItemsBy === 'total') {
          return this.$t('events.table.header_text.total', {
            fromDate: this.dateFromFormatted,
            toDate: this.dateToFormatted,
          })
        }
        if (this.detailedItemsBy === 'date') {
          return this.$t('events.table.header_text.date', {
            date: this.detailedItemsByItem.dateItem.label,
          })
        }
        if (this.detailedItemsBy === 'date-aggregate') {
          return this.$t('events.table.header_text.date_aggregate', {
            date: this.detailedItemsByItem.dateItem.label,
            type: this.detailedItemsByItem.eventGroup.type,
            code: this.detailedItemsByItem.eventGroup.code,
          })
        }
        if (
          this.detailedItemsBy === 'subitem' &&
          this.activeSearchFormTabName === 'vehicle'
        ) {
          return this.$t('events.table.header_text.vehicle', {
            vehicleName: this.detailedItemsByItem.subItem.label,
            date: this.detailedItemsByItem.dateItem.label,
          })
        }
        if (
          this.detailedItemsBy === 'subitem' &&
          this.activeSearchFormTabName === 'circuit'
        ) {
          return this.$t('events.table.header_text.circuit', {
            circuitName: this.detailedItemsByItem.subItem.label,
            date: this.detailedItemsByItem.dateItem.label,
          })
        }

        if (
          this.detailedItemsBy === 'aggregate' &&
          this.activeSearchFormTabName === 'vehicle'
        ) {
          return this.$t('events.table.header_text.vehicle_aggregate', {
            vehicleName: this.detailedItemsByItem.subItem.label,
            date: this.detailedItemsByItem.dateItem.label,
            type: this.detailedItemsByItem.eventGroup.type,
            code: this.detailedItemsByItem.eventGroup.code,
          })
        }
        if (
          this.detailedItemsBy === 'aggregate' &&
          this.activeSearchFormTabName === 'circuit'
        ) {
          return this.$t('events.table.header_text.circuit_aggregate', {
            circuitName: this.detailedItemsByItem.subItem.label,
            date: this.detailedItemsByItem.dateItem.label,
            type: this.detailedItemsByItem.eventGroup.type,
            code: this.detailedItemsByItem.eventGroup.code,
          })
        }

        return ''
      },
    },
    methods: {
      /**
       * Uses this.items (event store getter)
       */
      getVehicleName(id) {
        return (
          ((this.items || []).find((i) => i.vehicleId == id) || {})
            .vehicleName || ''
        )
      },
      getCircuitName(vehicleId, circuitId) {
        let synthesisVehicleItem =
          (this.items || []).find((i) => i.vehicleId == vehicleId) || {}
        let circuitSynthesis =
          (
            synthesisVehicleItem.circuits ||
            synthesisVehicleItem.synthesis ||
            []
          ).find((s) => s.circuitId == circuitId) || {}
        return circuitSynthesis.circuitName || ''
      },
    },
  }
}

export default {
  name: 'EventsModule',
  componentType: 'container',
  components: {
    SearchWrapper,
    TLayout,
    EventsTable,
    LocationEventsMap,
    EventsList,
    Sidebar,
  },
  mixins: [
    eventsFilterMixin(),
    tableHeaderMixin(),
    Vue.$mixins.userRightsMixin,
  ],
  provide() {
    let self = this
    let searchFormTabs = ['disabled']

    if (
      this.hasFeatureRight('events_search_vehicle') ||
      (getQueryStringValue('test') === '1' && !this.$env.isProduction())
    ) {
      searchFormTabs.push({
        label: i18n.t(`common.Véhicule`),
        value: 'vehicle',
      })
    }

    if (
      this.hasFeatureRight('events_search_circuit') ||
      (getQueryStringValue('test') === '1' && !this.$env.isProduction())
    ) {
      searchFormTabs.push({
        label: i18n.t(`common.Circuit`),
        value: 'circuit',
      })
    }

    if (searchFormTabs.length > 1) {
      searchFormTabs.splice(0, 1)
    }

    return {
      datatablePdfExport: true, //
      vueDataTableDrawOptions: {
        //showLoader: true,
        asyncRender: {
          batchSize: 250,
          drawOnce: true,
          progresiveLoader: false,
        },
      },
      showCustomFiltersByDefault: false,
      /**
       * Provide handler to react to date picker predefined options change and limit date range selection if necessary
       * @function onSearchModuleDatePickerPredefinedSelectionClick
       * @see SMDatePicker.vue Date picker will inject this handler
       */
      onSearchModuleDatePickerPredefinedSelectionClick({
        length,
        limitDateSelectionRangeToNDays,
      }) {
        let selectedElementsIds = self.$store.getters[
          'search_module/getSelectedIdsByType'
        ](self.activeSearchFormTabName)
        let willCurrentDateSelectionExceedSelectionLimit =
          selectedElementsIds.length * length > getEventsSearchSelectionLimit()
        if (willCurrentDateSelectionExceedSelectionLimit) {
          window.alert(
            `La sélection pour la recherche a été réduite à ${getEventsSearchSelectionLimit()} éléments`
          )
          let maxDateRangeLength = Math.round(
            (getEventsSearchSelectionLimit() - 1) / selectedElementsIds.length -
              1
          )
          limitDateSelectionRangeToNDays(maxDateRangeLength)
        }
      },
      searchModuleCanToggleFreesearch: false,
      searchFormTabs,
      pdfExportFilenamePrefix: 'export_details_evet_',
    }
  },
  data() {
    return {
      mode: 'list',
      //search selection
      elementIdsUsedDuringLastSearch: [],
      datesUsedDuringLastSearch: [],
      //Synthesis dataset (see: event-service.js:fetchEventsCumulation)
      isFetchDetailsInProgress: false,
      //detailedItems (computed) (use can choose to view table data at different levels)
      detailedItemsBy: 'total', //total/date/date-aggregate/subitem/aggregate
      detailedItemsByItem: null, //metadata {dateItem:{},subItem:{},eventGroup:{}}
    }
  },
  computed: {
    ...mapGetters({
      activeSearchFormTabName: 'search_module/activeSearchFormTabName',
      selection: 'search_module/getSelection',
      items: 'events/items',
      detailedItems: 'events/detailedItems',
      getBetterSelectStyleOverrides:
        'search_module/getBetterSelectStyleOverrides',
    }),

    listItems() {
      // Create a copy of items to avoid mutating the store directly
      return [...this.items].sort((a, b) => {
        // Extract date from item labels which typically start with DD/MM/YYYY
        const extractDate = (item) => {
          if (!item || !item.label) return 0

          // Extract date using regex to get DD/MM/YYYY format
          const dateMatch = item.label?.match(/^(\d{2})\/(\d{2})\/(\d{4})/)
          if (dateMatch && dateMatch.length >= 4) {
            // Convert to date object for comparison (using YYYY-MM-DD format)
            const day = dateMatch[1]
            const month = dateMatch[2]
            const year = dateMatch[3]
            return new Date(`${year}-${month}-${day}`).getTime()
          }

          // Fallback to timestamp if available
          return item.timestamp || 0
        }

        // Get dates as timestamps for comparison
        const dateA = extractDate(a)
        const dateB = extractDate(b)

        // Sort in descending order (newest first)
        return dateB - dateA
      })
    },

    /**
     * We highlight the current selection (date/date-aggregate/subitem/aggregate)
     * @todo Support date/date-aggregate type
     */
    highlightedSelectionId() {
      let dateId = this.detailedItemsByItem?.dateItem?.id || 'x'
      let subItemId = this.detailedItemsByItem?.subItem?.id || 'x' //vehicle/circuit
      let aggregateId = this.detailedItemsByItem?.eventGroup?.id || 'x'
      return `${this.detailedItemsBy}_${dateId}_${subItemId}_${aggregateId}`
    },
    menuCollapsed: {
      get() {
        return this.$store.state.events.menuCollapsed
      },
      set(value) {
        this.$store.state.events.menuCollapsed = value
      },
    },
  },
  watch: {
    isAggregateEventGroupBinEnabled() {
      this.forceTableReload()
    },
    isAggregateEventGroupRoundEnabled() {
      this.forceTableReload()
    },
    /*  aggregateSettingsUpdated() {
      let currentmode = this.mode
      console.log('aggregateSettingsUpdated', currentmode)
      if (this.mode != 'list') {
        this.switchMode('list')
        this.switchMode(currentmode)
      }
    }, */
    /**
     * If table-map/list mode, show markers in the map as soon as data is ready.
     */
    detailedItems() {
      if (this._prepareMarkersTimeout) {
        clearTimeout(this._prepareMarkersTimeout)
      }
      this._prepareMarkersTimeout = setTimeout(() => {
        this.prepareMapMarkers()
        clearTimeout(this._prepareMarkersTimeout)
      }, 500)
    },
    /**
     * Toggle sidebar result view on/off (list)
     */
    items() {
      const hasResults = this.items.length !== 0
      this.$store.dispatch('search_module/setHasResults', hasResults)
    },
  },
  mounted() {
    this.$store.dispatch('app/setIsLayoutTableMapResizeable', true)
    //By default, map is empty
    this.clearMapMarkers()
    window._em = this

    this.$store.dispatch('selection_templates/setSelectedTemplate', null)
  },
  beforeDestroy() {
    console.log('EventsModule beforeDestroy', {
      message: 'Resetting search module state',
    })
    // Reset search module state when leaving the module
    // Using default resetStatePartial to keep reference data (options) but clear selections
    this.$store.dispatch('search_module/resetStore', {
      origin: 'events/beforeDestroy',
      hardReset: false, // explicitly set to false to be clear about intention
    })
  },
  destroyed() {
    this.searchAborted = true
    this.$store.dispatch('simpliciti_map/resetStore', null)
    this.$store.dispatch('app/setIsLayoutTableMapResizeable', false)

    //This module reset search_module store because it makes use of it
    this.$store.dispatch('search_module/resetStore', {
      origin: 'events/destroyed',
      hardReset: false, // explicitly set to false to be clear about intention
    })

    this.$store.dispatch('selection_templates/setSelectedTemplate', null)
  },
  methods: {
    forceTableReload(interval = 250) {
      let currentmode = this.mode
      if (this.mode !== 'list') {
        console.log(currentmode)
        this.switchMode('list')
        //Must wait nextTick finish before set new switchmode
        setTimeout(() => {
          this.switchMode(currentmode)
        }, interval)
      }
    },
    getFetchEventOptions() {
      let elementIds = []
      let dates = []
      let filters = {}

      if (this.selectedAggregateEventGroups) {
        filters.group = this.selectedAggregateEventGroups
      }

      if (this.detailedItemsBy === 'total') {
        dates = this.datesUsedDuringLastSearch
        elementIds = this.elementIdsUsedDuringLastSearch
      }
      if (this.detailedItemsBy === 'date') {
        dates = [this.detailedItemsByItem.dateItem.date]
        elementIds = this.elementIdsUsedDuringLastSearch
      }
      if (this.detailedItemsBy === 'date-aggregate') {
        /* Event by date grouped by code/type */
        dates = [this.detailedItemsByItem.dateItem.date]
        elementIds = this.elementIdsUsedDuringLastSearch
        filters.type = this.detailedItemsByItem.eventGroup.type
        filters.code = this.detailedItemsByItem.eventGroup.code
      }

      if (this.detailedItemsBy === 'subitem') {
        /* Vehicle */
        dates = [this.detailedItemsByItem.dateItem.date]
        elementIds = [this.detailedItemsByItem.subItem.id]
      }

      if (['total', 'date', 'subitem'].includes(this.detailedItemsBy)) {
        filters.code = [
          ...this.selectedOperationMessageTypes.map((item) => item.id),
          ...this.selectedAnomaliesTypes.map((item) => item.id),
          ...this.selectedStatusTypes.map((item) => item.id),
        ]
      }

      if (this.detailedItemsBy === 'aggregate') {
        /* Event by code/type */
        dates = [this.detailedItemsByItem.dateItem.date]
        elementIds = [this.detailedItemsByItem.subItem.id]
        filters.type = this.detailedItemsByItem.eventGroup.type
        filters.code = this.detailedItemsByItem.eventGroup.code
      }

      dates = dates
        .map((range) => (range instanceof Array ? range[0] : range))
        .sort((a, b) =>
          (a._d || a).getTime() < (b._d || b).getTime() ? 1 : -1
        )

      if (
        this.selectedAnomaliesTypes.length === 0 &&
        this.selectedStatusTypes.length === 0 &&
        this.selectedOperationMessageTypes.length === 0 &&
        filters.type === undefined
      ) {
        filters.type = 0
      }

      return {
        elementIds,
        dates,
        filters,
      }
    },
    /**
     * Fetch events
     * The user can click:
     *  - Buttons in the list - top toolbar (i.g table mode)
     *  - Buttons in the list - date label (i.g table mode)
     *  - Buttons in the list - date label -> event code/type (i.g table mode)
     * @param {*} options
     */
    async fetchEvents() {
      let options = this.getFetchEventOptions()
      let { elementIds, dates, filters } = options
      this.$store.commit('events/resetDetailedItems')

      if (this.isFetchDetailsInProgress) {
        console.warn('fetchEvents::skip (in progress)')
        return
      } else {
        this.isFetchDetailsInProgress = true
      }

      this.searchAborted = false
      tableProgressiveLoader.show('events-start')

      fetchEvents({
        elementType: this.activeSearchFormTabName,
        elementIds,
        dates,
        filters,
        handleResults: (results) => {
          if (this.searchAborted) {
            tableProgressiveLoader.hide('events-abort')
            return false //abort fetch operation
          } else {
            tableProgressiveLoader.show('events-handle-results')
          }
          this.$store.commit('events/updateDetailedItems', results)
        },
      }).finally(() => {
        this.isFetchDetailsInProgress = false
        tableProgressiveLoader.hide('events-finally')
      })
    },
    /**
     * There are three possible toolbars actions:
     * map: Switch to table+map
     * table: Siwtch to table
     */
    async onToolbarAction(options) {
      this.detailedItemsBy = options.type
      this.detailedItemsByItem = options.item

      //Async
      this.fetchEvents().finally(() => {
        this.isFetchDetailsInProgress = false
      })

      //Switch layout to map/table
      if (['map', 'table'].includes(options.action)) {
        this.switchMode(
          {
            map: 'table_map',
            table: 'table',
          }[options.action]
        )
      }
    },

    async clearMapMarkers() {
      await this.$store.dispatch('simpliciti_map/setDataset', {
        type: 'eventsMarkers',
        data: [],
      })
    },
    prepareMapMarkers() {
      //Limit map rendering
      if (this.detailedItems.length > 5000) {
        this.$store.dispatch('alert/addAlert', {
          type: 'info',
          title: this.$t('identification.map.render_limit_message_title'),
          text: this.$t('identification.map.render_limit_message_body', {
            length: this.detailedItems.length,
            maxCount: 5000,
          }),
          //text: `Affichage de 5000 éléments sur les ${this.detailedItems.length} résultats`,
        })
      }

      this.$store.dispatch('simpliciti_map/setDataset', {
        type: 'eventsMarkers',
        data: this.detailedItems.slice(0, 5000),
      })
    },

    /**
     * Fetch synthesis dataset
     * - Splits requests by vehicle/date (1-1)
     */
    async performsSearch() {
      const self = this
      this.clearMapMarkers()
      let dateRanges = this.selection.selectedDateRanges
      //vehiclers or circuits
      let selectedElementsIds = this.$store.getters[
        'search_module/getSelectedIdsByType'
      ](this.activeSearchFormTabName)

      //Test: Predefined selection (berto provence)
      if (getQueryStringValue('test') === '1' && !this.$env.isProduction()) {
        selectedElementsIds = [6447, 29206, 42701]
        dateRanges = [
          [
            moment('2022-04-29T00:00:00Z').minute(0).hour(0).second(0),
            moment('2022-04-29T00:00:00Z').minute(59).hour(23).second(59),
          ],
        ]
      }

      //Validation: Limit vehicles selection
      if (
        selectedElementsIds.length * (dateRanges.length || 1) >
        getEventsSearchSelectionLimit()
      ) {
        this.$alertPopup.showSelectionLimitWarning(
          selectedElementsIds.length * (dateRanges.length || 1),
          getEventsSearchSelectionLimit()
        )
        return
      }

      this.$store.commit('events/resetItems') //This will also triger the results view
      this.loading = true
      let len = dateRanges.length
      if (len === 0) {
        dateRanges = [
          [
            moment().hour(0).minute(0).second(0)._d,
            moment().hour(23).minute(59).second(59)._d,
          ],
        ]
        len = dateRanges.length
      }
      this.$store.state.search_module.isSearchInProgress = true
      //this.$loader && this.$loader.show()

      let dateFrom = dateRanges[0][0]
      let dateTo = dateRanges[len - 1][1]
      this.dateFromFormatted = moment(dateFrom).isValid()
        ? moment(dateFrom).format('DD/MM/YYYY HH:mm:ss')
        : ''
      this.dateToFormatted = moment(dateTo).isValid()
        ? moment(dateTo).format('DD/MM/YYYY HH:mm:ss')
        : ''

      let dates = dateRanges
        .map((range) => range[0])
        .sort((a, b) => (a.getTime() < b.getTime() ? 1 : -1))

      this.elementIdsUsedDuringLastSearch = selectedElementsIds
      this.datesUsedDuringLastSearch = dates

      this.$store.commit('events/resetItems')

      try {
        self.searchAborted = false
        await fetchEventsCumulation(selectedElementsIds, dates, {
          elementType: this.activeSearchFormTabName,
          filters: {
            ...(this.selectedAnomaliesTypes.length === 0 &&
            this.selectedStatusTypes.length === 0 &&
            this.selectedOperationMessageTypes.length === 0
              ? {
                  type: 0,
                }
              : {}),
            //We want to filter by event code (agregat.event.code equals ref.anomalies.code)
            code: [
              ...this.selectedAnomaliesTypes.map((item) => item.id),
              ...this.selectedStatusTypes.map((item) => item.id),
              ...this.selectedOperationMessageTypes.map((item) => item.id),
            ],
          },
          onDataReceived(items, pushToArrayMerge) {
            if (self.searchAborted) {
              return false
            }

            pushToArrayMerge(self.items, items)
          },
        })

        //Switch to results (even if no results from API, user should see the message indicating no results)
        this.$nextTick(() =>
          this.$store.dispatch('search_module/setHasResults', true)
        )
      } finally {
        //Hide loader in search module search button
        this.$store.state.search_module.isSearchInProgress = false
        //Hide big loader
        //this.$loader && this.$loader.hide()
        this.loading = false
      }
    },
    async onSearchModuleSelectionChange() {
      this.performsSearch()
    },
    onSearchModuleViewChange(view) {
      //React to search module view changes (selection / results)
      if (view === 'selection') {
        this.mode = 'list'
        this.searchAborted = true
      }
      if (view === 'results') {
        this.mode = 'table_map'
        this.onToolbarAction({ action: 'table_map', type: 'total' })
      }
    },
    onSearchModuleClearSelection() {
      //Clear custom filters
      this.selectedOperationMessageTypes = []
      this.selectedAnomaliesTypes = []
      this.selectedStatusTypes = []
    },
    switchMode(mode) {
      this.$nextTick(() => {
        this.mode = mode
      })
    },
    selectAlloperationStatusTypes() {
      this.selectedStatusTypes = [...this.operationStatusTypesComputed]
    },
    deselectAlloperationStatusTypes() {
      this.selectedStatusTypes = []
    },
    selectAllAnomaliesTypes() {
      this.selectedAnomaliesTypes = [...this.operationAnomaliesTypesComputed]
    },
    deselectAllAnomaliesTypes() {
      this.selectedAnomaliesTypes = []
    },
    selectAllOperationMessageTypes() {
      this.selectedOperationMessageTypes = [
        ...this.operationMessageTypesComputed,
      ]
    },
    deselectAllOperationMessageTypes() {
      this.selectedOperationMessageTypes = []
    },
  },
}
</script>
<style lang="scss" scoped>
.table-layout {
  height: calc(100% - 20px);
}

.filter-label {
  font: normal normal normal 11px/14px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
  margin: 0px;
}

.radio label {
  font: normal normal normal 11px/14px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
  margin: 0px;
}
.info-tooltip {
  margin-left: 5px;
  margin-right: 5px;
  background: var(--color-main);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  text-align: center;
  color: white;
  line-height: 12px;
  font-size: 8px;
}
</style>
