{"Catégorie": "<PERSON><PERSON><PERSON><PERSON>", "Début": "D<PERSON>but", "Fin": "Fin", "alert_toast.titles.danger": "<PERSON><PERSON><PERSON>", "alert_toast.titles.info": "Information", "alert_toast.titles.warning": "<PERSON><PERSON><PERSON>", "alert_types.absence_de_poids_bac_nombre": "Absence de poids bac (nombre)", "alert_types.absence_de_poids_bac_temps": "Absence de poids bac (temps)", "alert_types.arret_non_justifie": "Arret Non Justifie", "alert_types.bac_non_leve_sur_circuit": "Bac non levé sur circuit", "alert_types.circuit_execute_le_mauvais_jour": "Circuit exécuté le mauvais jour", "alert_types.collision_lc": "Collision (LC)", "alert_types.conducteur_identifie": "Conducteur identifi<PERSON>", "alert_types.conducteur_non_identifie": "Conducteur non identifié", "alert_types.contact_off": "Contact OFF", "alert_types.contact_on": "Contact ON", "alert_types.date_atteinte": "Date atteinte", "alert_types.entree_dans_la_zone": "Entrée dans la zone", "alert_types.entretien_capteurs_atteint": "Entretien capteurs at<PERSON>t", "alert_types.entretien_sur_moteur_tournant_fms": "Entretien sur Moteur tournant (FMS)", "alert_types.evenement": "Evenement", "alert_types.execution_de_circuit": "Execution de circuit", "alert_types.favoris": "<PERSON><PERSON><PERSON>", "alert_types.fdr_planifiee_non_debutee": "FDR planifiée non débutée", "alert_types.kilometrage_atteint": "Kilométrage atteint", "alert_types.lc_mauvaise_conduite_acceleration": "LC Mauvaise conduite (Accélération)", "alert_types.lc_mauvaise_conduite_changement_file": "LC Mauvaise conduite (Changement file)", "alert_types.lc_mauvaise_conduite_freinage": "LC Mauvaise conduite (Freinage)", "alert_types.lc_mauvaise_conduite_vitesse_excessive": "LC Mauvaise conduite (Vitesse excessive)", "alert_types.mission": "Mission", "alert_types.niveau_de_batterie": "Niveau de batterie", "alert_types.non_conformite_des_elements_de_checklists": "Non-conformité des éléments de checklists", "alert_types.perte_du_signal_gps": "Perte du Signal GPS", "alert_types.positions_gps_transmises_avec_un_delai_superieur_a_x_heures": "Positions GPS transmises avec un délai supérieur à X heures", "alert_types.sortie_de_zone": "Sortie de zone", "alert_types.taux_activite_capteurs_sur_circuit_insuffisant": "Taux d'activité capteurs sur circuit insuffisant", "alert_types.taux_bacs_identifies_sur_tournee": "Taux bacs identifiés sur tournée", "alert_types.taux_realisation_execution_de_circuit": "Taux réalisation exécution de circuit", "alert_types.taux_realisation_surface_par_zone_sur_circuit": "Taux réalisation surface par zone sur circuit", "alert_types.temps_activation_capteur_par_zone_sur_circuit": "Temps activation capteur par zone sur circuit", "alert_types.temps_contact_a_l_arret": "Temps contact à l'arrêt", "alert_types.temps_d_arret_contact_coupe": "<PERSON><PERSON> (contact coupé)", "alert_types.temps_d_utilisation": "Temps d'utilisation", "alert_types.temps_d_utilisation_capteur": "Temps d'utilisation capteur", "alert_types.temps_d_utilisation_moteur": "Temps d'utilisation moteur", "alert_types.temps_de_pause": "Temps de pause", "alert_types.temps_passe": "Te<PERSON> passé", "alert_types.temps_passe_dans_un_statut": "Temps Passé dans Un Statut", "alert_types.temps_passe_dans_un_statut_chrono": "Temps passé dans un statut chrono", "alert_types.tournee_planifiee_non_executee": "Tournée Planifiée non exécutée", "alert_types.vol_de_carburant": "Vol de carburant", "alerts.BAD_CREDENTIALS": "Nom d'utilisateur ou mot de passe incorrect.", "alerts.DATATABLE_MAX_ELEMENTS_LIMIT_HIT": "La réponse du serveur ({output} éléments), dépasse la limite établie ({expected} éléments). Réessayez de modifier les filtres.", "alerts.FILTERS_MAX_SELECTION": "Le nombre d'éléments de filtre sélectionnés doit être inférieur ou égal à {number}", "alerts.LOGIN_FAIL": "Identification impossible, veuil<PERSON>z vérifier votre saisie.", "alerts.NOT_ENOUGH_RIGHTS": "Votre compte ne dispose pas de tous les droits nécessaires. Veuillez contacter votre administrateur.", "alerts.PARENT_LOGIN_NOT_SUPPORTED": "Vous devez vous connecter avec un compte utilisateur appartenant au client", "alerts.actions_ack.fail": "Problème lors de l'exécution de l'action", "alerts.actions_ack.success": "Alerte acquittée avec succès", "alerts.actions_ack.title": "Acquittement de l'alerte", "alerts.filters.ack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alerts.filters.noack": "Non acquitté", "alerts.item.status_ack": "Prise en compte", "alerts.item.status_noack": "En attente", "alerts.list_item.ack_button_tooltip": "Acquitter l'alerte", "alerts.list_item.share_button_tooltip": "Partager l'alerte par mail", "alerts.list_item.show_button_tooltip": "Affichage Carto de l'alerte", "alerts.list_sorting.ack": "Acquittée en premier", "alerts.list_sorting.name": "Nom de l’alerte", "alerts.list_sorting.noack": "Non acquittée en premier", "alerts.list_sorting.none": "Aucun tri", "alerts.list_sorting.recent": "<PERSON><PERSON><PERSON> ré<PERSON>e", "alerts.list_sorting.title": "Trier par", "alerts.list_sorting.type": "Type d’alerte", "alerts.list_sorting.vehicle": "Véhicule", "alerts.list_sorting.vehicle_category": "Catégorie de véhicule", "alerts.main_datetime_picker_label": "Période", "alerts.main_datetime_picker_placeholder": "Sélectionner une date", "alerts.main_search.filters.alert_type_info": "Sélection multiple avec Ctrl+Click et Shift+Click", "alerts.main_search.results_filter.ack": "<PERSON><PERSON><PERSON> acquitt<PERSON>", "alerts.main_search.results_filter.noack": "Alerte non acquittée", "alerts.main_search.results_filter.none": "Aucun filtre", "alerts.map_maker.alert_address": "<PERSON><PERSON><PERSON>", "alerts.map_maker.alert_date": "Déclenchement", "alerts.map_maker.alert_name": "Nom de l'alerte", "alerts.map_maker.alert_status": "Prise en compte de l'alerte", "alerts.map_maker.alert_type": "Type d'alerte", "alerts.map_maker.alert_vehicle": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON>", "alerts.map_maker.no_value": "n/c", "alerts.map_marker.last_infos_title": "Dernières infos reçues", "alerts.map_marker.title": "Dé<PERSON> de l'alerte", "alerts.no_results": "Aucune donnée disponible pour cette période", "alerts.no_results_after_filter": "Il n'y a pas de résultats pour les filtres appliqués", "alerts.stats.ack": "Alertes acquitt<PERSON>", "alerts.stats.noack": "Alertes non acquittées", "alerts.table_column.date": "Date déclenchement", "alerts.table_column.name": "Nom de l'alerte", "alerts.table_column.status": "Statut alerte", "alerts.table_column.status_column": "Prise en compte", "alerts.table_column.time": "Heure déclenchement", "alerts.table_column.type": "Type d'alerte", "alerts.table_column.vehicle_name": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON>", "alerts.table_header_text_html": "<strong>Alertes</strong><span>du</span><strong>{fromDate}</strong><span>au</span><strong>{toDate}</strong>", "auth.contacter_assistance": "Contacter l'assistance", "blackspot.api.import_already_exists": "{count} point noir existe déjà et a été ignoré", "blackspot.api.import_already_exists_plural": "{count} points noirs existent déjà et ont été ignorés", "blackspot.api.import_failure": "Une erreur est survenue lors de l'importation des points noirs", "blackspot.api.import_success": "{savedCount}/{totalCount} points noirs ont été importés avec succès", "blackspot.api_errors.constraint.label_already_used": "Cet intitulé est déjà utilisé par un autre type de contrainte.", "blackspot.cancel_button": "Annuler", "blackspot.cannot_zoom_invalid_geometry": "Impossible de zoomer (géomé<PERSON>e invalide)", "blackspot.category": "<PERSON><PERSON><PERSON><PERSON>", "blackspot.constraint": "Contrainte", "blackspot.constraints-list.filter.form.name": "Nom du type", "blackspot.constraints.color": "<PERSON><PERSON><PERSON>", "blackspot.constraints.create_button": "Créer un type de point noir", "blackspot.constraints.icon": "Icône", "blackspot.constraints.image": "Image", "blackspot.constraints.label": "Intitulé", "blackspot.constraints.label_column": "Libellé", "blackspot.constraints.remove.associated.text": "Des points noirs sont associés à cette contrainte. Il n'est pas possible de la supprimer.", "blackspot.constraints.remove.associated.title": "Suppression impossible", "blackspot.constraints.remove_confirmation": "Supprimer ce type de contrainte de point noir ?", "blackspot.constraints.speed": "Vitesse limite", "blackspot.constraints.update_button": "Mettre à jour", "blackspot.constraints_return_text": "Retourner à la liste des types de contraintes", "blackspot.contraints.color": "<PERSON><PERSON><PERSON>", "blackspot.contraints.image": "Image", "blackspot.contraints.libellé": "Libellé", "blackspot.contraints.speed": "Vitesse", "blackspot.create_button": "Créer un point noir", "blackspot.description": "Description", "blackspot.enable_label": "<PERSON>r le point", "blackspot.generic_info_toast_title": "Information", "blackspot.import.csv_file_required": "Tous les fichiers doivent être au format CSV", "blackspot.import.csv_row": "Ligne", "blackspot.import.drop_or_select": "Déposez vos fichiers ici ou cliquez pour les sélectionner", "blackspot.import.generic_error_message": "Erreur d'importation", "blackspot.import.max_radius_validation": "Le rayon doit être inférieur ou égal à {radiusMeters} mètres", "blackspot.import.missing_required_values": "Le nom, la latitude, la longitude et le rayon sont des champs obligatoires", "blackspot.import.processing": "Traitement en cours…", "blackspot.import_button": "Importer un point noir", "blackspot.limitSpeed": "Vitesse limite", "blackspot.list.filter.form.constraint": "Type de point noir", "blackspot.list.filter.form.name": "Nom du point noir", "blackspot.list.filter.title": "Filtrer par", "blackspot.list.tooltip.edit": "Modifier", "blackspot.list.tooltip.remove": "<PERSON><PERSON><PERSON><PERSON>", "blackspot.list.tooltip.view": "Visualiser sur la cartographie", "blackspot.list_no_data": "<PERSON><PERSON><PERSON> don<PERSON>", "blackspot.map.buttons.draw_circle": "Définir le calque (Cercle)", "blackspot.map.buttons.edit_layers": "Editer des calques", "blackspot.map.buttons.move_layers": "Déplacer des calques", "blackspot.map.buttons.remove_layers": "Supprimer des calques", "blackspot.map.buttons.rotate_layers": "Tourner des calques", "blackspot.map.draw-marker": "Définir le calque (Segment)", "blackspot.map.draw-polygon": "Définir le calque (Polygone)", "blackspot.map.zoom-in": "<PERSON>mer", "blackspot.map.zoom-out": "Dézoomer", "blackspot.module_label": "Points noirs", "blackspot.name": "Nom", "blackspot.planning_add": "Ajouter une période d'activité", "blackspot.planning_new": "Nouvelle période d'activité", "blackspot.planning_title": "Période d'activité", "blackspot.remove_confirmation": "Supprimer ce point noir ?", "blackspot.return_text": "Retourner à la liste", "blackspot.segment_marker_add_tooltip": "Cliquez pour insérer une partie de segment", "blackspot.segment_part_removal_validation": "Seules les parties de début ou de fin d'un segment peuvent être supprimées", "blackspot.tabs.blackspot": "Points noirs", "blackspot.tabs.blackspot_constraints": "Types", "blackspot.upload.section.notice.0": "<PERSON><PERSON><PERSON>", "blackspot.upload.section.notice.1": "Un ou plusieurs fichier(s) .CSV utilisant un point-virgule pour délimiter les champs ainsi que les colonnes suivantes :", "blackspot.upload.section.title": "Importer des points noirs", "blackspot.validate_button": "Valider", "blackspot.validation.consecutive_segment_parts": "Les parties du segment doivent être consécutives les unes aux autres", "blackspot.validation.defaultSpeed": "La vitesse ne peut excéder la vitesse associée au point noir de type « {name} » de {speed} km/h.", "blackspot.validation.max_segment_distance_meters": "La distance maximale autorisée pour un segment est de {meters} mètres", "blackspot.validation.max_segment_parts": "Le nombre maximum de parties d'un segment est de {count}", "blackspot.validation.validity_periods_overlap": "Les périodes de validité ne peuvent pas se chevaucher", "blackspot.violations.duplicated_name": "Il existe déjà un point noir du même nom.", "buttons.cancel": "Retour", "buttons.cancel_alternative": "Annuler", "buttons.center_map_results": "Centrer les résultats sur la carte", "buttons.close": "<PERSON><PERSON><PERSON>", "buttons.confirm": "Confirmer", "buttons.connection": "Se connecter", "buttons.preview": "<PERSON><PERSON><PERSON><PERSON>", "buttons.remove": "<PERSON><PERSON><PERSON><PERSON>", "buttons.reset": "Réinitialiser", "buttons.return": "Retour", "buttons.save": "Enregistrer", "buttons.sign_in": "Sign-In", "buttons.valid": "Valider", "can_sensors.accelerateur": "Accélérateur", "can_sensors.batterie": "<PERSON><PERSON>ie", "can_sensors.canServiceDistanceMeters": "Distance", "can_sensors.conso": "Consommation", "can_sensors.cruise": "Cruise", "can_sensors.embrayage": "Embrayage", "can_sensors.freinage": "<PERSON><PERSON><PERSON>", "can_sensors.fuel": "Carburant", "can_sensors.fuel_perc": "Carburant (%)", "can_sensors.km_fms": "Km FMS", "can_sensors.niveau_carburant": "Carburant", "can_sensors.vehicleDistance": "Distance véhicule", "can_sensors.vrm": "<PERSON><PERSON><PERSON><PERSON>", "citifret.datatable.emptyTable": "Pas de rés<PERSON>at", "citifret.orders": "Commandes", "common.Add_Favorite": "<PERSON><PERSON>er un favori", "common.Adresse": "<PERSON><PERSON><PERSON>", "common.Alertes": "<PERSON><PERSON><PERSON>", "common.Annuler": "Annuler", "common.Arrivée": "Arrivée", "common.Arrét": "<PERSON><PERSON><PERSON><PERSON>", "common.Catégorie": "<PERSON><PERSON><PERSON><PERSON>", "common.Chauffeur": "<PERSON><PERSON><PERSON>", "common.Circuit": "Circuit", "common.Conducteur": "Conducteur", "common.Dateheure": "Date & Heure", "common.Distance": "Distance", "common.Départ": "<PERSON><PERSON><PERSON><PERSON>", "common.Filtrer": "<PERSON><PERSON><PERSON>", "common.Filtres": "Filtres", "common.Numéro": "<PERSON><PERSON><PERSON><PERSON>", "common.Rechercher": "<PERSON><PERSON><PERSON>", "common.Réalisation": "Réalisation", "common.Réalisé": "<PERSON><PERSON><PERSON><PERSON>", "common.TempsReception": "Temps reception", "common.Valider": "Valider", "common.Vitesse": "Vitesse", "common.Véhicule": "Véhicule", "common.Zones": "Zones", "common.all": "Tous", "common.av_speed": "Vitesse moy.", "common.average_speed": "Vitesse moyenne", "common.calculationMethod_names.bicycle": "<PERSON><PERSON><PERSON>", "common.calculationMethod_names.car": "Voiture", "common.calculationMethod_names.delivery_truck": "<PERSON><PERSON><PERSON>", "common.calculationMethod_names.emergency": "Urgence", "common.calculationMethod_names.motorcycle": "Moto", "common.calculationMethod_names.pedestrian": "<PERSON><PERSON><PERSON>", "common.calculationMethod_names.public_bus": "Bus", "common.calculationMethod_names.taxi": "Taxi", "common.calculationMethod_names.truck": "Camion", "common.capteurs": "Capteurs", "common.categorie": "<PERSON><PERSON><PERSON><PERSON>", "common.categoryx": "<PERSON><PERSON><PERSON><PERSON> {text}", "common.categoryx_abbr": "Cat. {text}", "common.circuit_category": "Catégorie de circuit", "common.clear_results": "Effacer les résultats", "common.code": "Code", "common.confirmation": "Confirmation", "common.date": "Date", "common.days.friday": "<PERSON><PERSON><PERSON><PERSON>", "common.days.monday": "<PERSON><PERSON>", "common.days.saturday": "<PERSON><PERSON>", "common.days.sunday": "<PERSON><PERSON><PERSON>", "common.days.thursday": "<PERSON><PERSON>", "common.days.tuesday": "<PERSON><PERSON>", "common.days.wednesday": "<PERSON><PERSON><PERSON><PERSON>", "common.debut": "D<PERSON>but", "common.disabled": "Inactif", "common.dist": "Dist.", "common.distance": "Distance", "common.duree": "<PERSON><PERSON><PERSON>", "common.duree_arret": "<PERSON><PERSON><PERSON>", "common.duree_contact": "Du<PERSON>e contact", "common.duree_effective": "Du<PERSON>e effective", "common.duree_trajet": "<PERSON><PERSON><PERSON> trajet", "common.enabled": "Actif", "common.fin": "Fin", "common.flux": "Flux", "common.from": "De", "common.from_time_abbr": "<PERSON><PERSON>", "common.hour": "<PERSON><PERSON>", "common.ident": "Ident.", "common.infinite_pagination_info": "Affichage de l'élément 1 à {loadedCount} sur {totalCount} éléments", "common.invalid_position": "Position non valide", "common.lat": "Latitude", "common.layout.modes.table_and_map_button_tooltip": "Affichage Tableau et Carto", "common.layout.modes.table_button_tooltip": "Affichage tableau", "common.list": "Liste", "common.lng": "Longitude", "common.loading_in_progress": "Chargement en cours...", "common.main_search.selection_limit_text": "Trop d'éléments sélectionnés ({current}/{limit})", "common.main_search.selection_limit_title": "Sélection de recherche", "common.maneuver": "Manœuvre", "common.map.basemaps.title": "Fonds cartographiques", "common.map.basemaps.title_tooltip": "Cliquez pour ouvrir la liste des fonds cartographiques", "common.map.options.all": "Tous", "common.map.options.chrono": "Imputations chrono", "common.map.options.circuit_legend.not_done_label": "Tronçons non réalisés", "common.map.options.circuit_legend_title": "Activité", "common.map.options.identification": "Identification Bac", "common.map.options.none": "Aucun", "common.map.options.show.all": "<PERSON><PERSON><PERSON><PERSON> tout", "common.map.options.show.none": "Masquer tout", "common.map.options.title": "Options d'affichage", "common.map.options.trip_history__label": "Itinéraire", "common.map.popup.address_locate": "Localiser une adresse", "common.map.popup.address_natural_geolocation": "Géocodage naturel", "common.map.popup.alert_title": "<PERSON><PERSON><PERSON> d<PERSON>", "common.map.popup.alert_title_module": "Dé<PERSON> de l'alerte", "common.map.popup.circuit_reference_visualization": "Visualisation circuit de référence", "common.map.popup.coordinates_locate": "Localiser une Latitude/Longitude", "common.map.popup.event": "<PERSON><PERSON><PERSON><PERSON>", "common.map.popup.predefined_views": "Vues prédéfinies", "common.map.popup.realtime.title": "Informations du véhicule", "common.map.popup.received_infos": "Informations reçues", "common.map.popup.reference_circuit_preview": "Visualisation circuit de référence", "common.map_toolbox.toggle_button_tooltip": "Outils cartographique", "common.matriculation": "Immatriculation", "common.matriculation_abbr": "<PERSON><PERSON><PERSON>", "common.message": "Message", "common.minutes": "minutes", "common.nb_arret": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "common.no": "Non", "common.no_results": "Pas de rés<PERSON>at", "common.no_results_after_filter": "Aucun résultat ne correspond aux filtres appliqués", "common.no_results_alternative": "<PERSON><PERSON><PERSON> donnée disponible", "common.nom": "Nom", "common.none": "Aucun", "common.not_available": "n/c", "common.open_close_all": "Ouvrir/fermer tout", "common.percentage": "<PERSON><PERSON>", "common.place": "<PERSON><PERSON>", "common.position": "Position", "common.received_info": "Informations reçues", "common.remove": "<PERSON><PERSON><PERSON><PERSON>", "common.results": "Résultats", "common.results_toolbar.filter_button_tooltip": "Filtrer les résultats", "common.results_toolbar.show_on_map_button_tooltip": "Affichage Carto", "common.save_select": "Sauvegarder la sélection", "common.status": "Status", "common.sweep": "Informations Nettoiement", "common.the": "Le", "common.title": "Titre", "common.to": "À", "common.to_time_abbr": "<PERSON><PERSON>", "common.unident": "Désident.", "common.vehicle_category": "Catégorie de véhicule", "common.vehicle_data": "Données véhicule", "common.vehicle_date_no_results": "Pas d'information disponible pour ce véhicule à cette date", "common.vehicule": "Véhicule", "common.version": "Version", "common.yes": "O<PERSON>", "common.Événements": "Événements", "container.alert.change.saved": "Modification enregistrée", "container.alert.error.happened": "Une erreur est survenue", "container.alert.registered.container": "Conteneur enregistré", "container.button.create": "Ajouter un conteneur", "container.button.update": "Mettre à jour", "container.buttons.back_to_containers_list": "Retour à la liste des conteneurs", "container.buttons.cancel": "Annuler", "container.buttons.valid": "Valider", "container.confirm.unarchive": "Pour confirmer le désarchivage cliquez sur OK", "container.error.select_container": "Veuillez sélectionner un conteneur", "container.error.use_not_right": "Vous n'avez pas les droits nécessaires", "container.form.address": "<PERSON><PERSON><PERSON>", "container.form.address.uneditable": "Non modifiable, utilisation en cours.", "container.form.address_carto": "Sélectionnez un point sur la carte", "container.form.archive.error.required": "Le motif est obligatoire", "container.form.archive.error.toolong": "Le motif est trop long. <PERSON><PERSON><PERSON> le <PERSON> {max} caractères", "container.form.archive.placeholder": "{max} caractères maximum", "container.form.archive.reason": "Raison de l'archivage", "container.form.archive.title": "Archivage du conteneur", "container.form.archived": "Afficher les conteneurs archivés", "container.form.bicomp": "Bi compartimenté", "container.form.code": "Code", "container.form.commissioning_date": "Date de mise en service", "container.form.create.error.alphanum": "Ce champ ne doit pas contenir de caractères spéciaux ni d'espace", "container.form.create.error.codeexist": "Ce code existe déjà pour un autre conteneur", "container.form.create.error.integer": "Ce champ doit être un nombre entier", "container.form.create.error.notenull": "Ce champ doit être un nombre différent de 0", "container.form.create.error.required": "Ce champ est requis", "container.form.create.error.toolong": "Ce champ doit contenir {max} caractères maximum", "container.form.ext_ref": "Référence externe", "container.form.flow": "Flux", "container.form.flow1": "Flux du premier compartiment", "container.form.flow2": "Flux du second compartiment", "container.form.geolocated": "Afficher les conteneurs non géolocalisés", "container.form.name": "Nom du conteneur", "container.form.restricted": "Conteneur en cours d'utilisation, vous n'avez pas les droits pour modifier ce champ", "container.form.state": "Etat", "container.form.unarchive.address": "Renseignez l'adresse", "container.form.unarchive.error.required": "L'adresse est obligatoire", "container.form.unarchive.title": "Desarchiver le conteneur", "container.form.use": "Utilisation", "container.form.using": "Utilisation", "container.form.using.title": "Nouvelle utilisation de ", "container.form.using_date": "Date et heure", "container.form.vehicle": "Véhicule", "container.form.volume": "Volume", "container.form.warning_container_in_progress": "Le véhicule est en cours de collecte, la modification du statut peut engendrer des incohérences de reporting. À n’utiliser qu’en cas de panne de tablette", "container.le": "le", "container.list.archived": "Archivé", "container.list.archived_containers": "Conteneurs archivés", "container.list.no_item": "Aucun conteneur dans cette liste", "container.list.not_located": "Non géolocalisé", "container.list.notgeolocated_containers": "Conteneurs non géolocalisés", "container.list.unable_state": "Etat non encore défini", "container.list_dropdown_filter_label": "Filtrer par", "container.map.popup.flow": "Flux", "container.map.popup.last_tour": "<PERSON><PERSON><PERSON> tournée", "container.map.popup.last_vehicle_name": "Dernier véhicule", "container.map.popup.name": "Nom du conteneur", "container.map.popup.status": "Statut", "container.map.popup.title": "Conteneur", "container.module.unlocated_containers": "conteneur non géolocalisé | conteneurs non géolocalisés", "container.module.unlocated_containers.info": "information saisie manuellement par le chauffeur", "container.module.unlocated_containers.unlocated": "Absence de localisation", "container.no": "Non", "container.shadowbox.title": "Liste des conteneurs non géolocalisés", "container.table.address": "<PERSON><PERSON><PERSON>", "container.table.biComp": "Bi compartimenté", "container.table.code": "Code du conteneur", "container.table.commissioning": "Date de mise en service", "container.table.complement": "Informations complémentaires", "container.table.flow": "Flux de chaque compartiment", "container.table.flow1": "Flux 1", "container.table.flow2": "Flux 2", "container.table.last_tour": "<PERSON><PERSON><PERSON> tournée", "container.table.last_vehicle_name": "Dernier véhicule", "container.table.name": "Nom du conteneur", "container.table.reference": "Ref externe", "container.table.state": "Etat", "container.table.use": "Utilisation", "container.table.volume": "Volume", "container.table.withdrawal": "Date de retrait", "container.table.withdrawal.reason": "<PERSON><PERSON><PERSON> de <PERSON>trait", "container.undefined": "Non défini", "container.yes": "O<PERSON>", "container_module.state.label.tooltip": "Le filtrage portera uniquement sur les contenants avec un état affecté lors de l'import", "container_module.state_label.in_service": "En service", "container_module.state_label.in_stock": "En stock", "container_module.state_label.out_of_service": "Hors service", "containers.module.last_collection.export.file.bin_total_available_items": "Total des contenants du parc", "containers.module.last_collection.export.file.bin_zone_available_items": "Total des contenants sur la zone", "containers.module.last_collection.export.file.column_title.bin_type": "Type de bac", "containers.module.last_collection.export.file.column_title.bin_volume": "Volume", "containers.module.last_collection.export.file.column_title.chip_number": "Numéro de <PERSON>ce", "containers.module.last_collection.export.file.column_title.collect_address": "<PERSON>ress<PERSON> de collecte", "containers.module.last_collection.export.file.column_title.collect_status": "Statut de la collecte", "containers.module.last_collection.export.file.column_title.collected_at": "Collecté le", "containers.module.last_collection.export.file.column_title.commissioning_date": "Mise en service", "containers.module.last_collection.export.file.column_title.datetime_last_collect": "Date et heure de la dernière collecte", "containers.module.last_collection.export.file.column_title.flux": "Flux", "containers.module.last_collection.export.file.column_title.round_last_collect": "Circuit de la dernière collecte", "containers.module.last_collection.export.file.column_title.round_name": "Nom du circuit", "containers.module.last_collection.export.file.column_title.state": "État", "containers.module.last_collection.export.file.column_title.tank_number": "Numéro de cuve", "containers.module.last_collection.export.file.column_title.vehicle_last_collect": "Véhicule de la dernière collecte", "containers.module.last_collection.export.file.column_title.vehicle_name": "Nom du véhicule", "containers.module.last_collection.export.file_prefix": "Export_contenant", "containers.tabs.general": "Informations générales", "containers.tabs.operational": "Informations opérationnelles", "containers_module.buttons.filter": "<PERSON><PERSON><PERSON>", "containers_module.collectedAtTimes.between24hAnd48h": "Collectés entre 24 et 48h", "containers_module.collectedAtTimes.between48hAnd72h": "Collectés entre 48 et 72h", "containers_module.collectedAtTimes.lessThan24h": "Collectés il y a moins de 24h", "containers_module.collectedAtTimes.moreThan72h": "Collectés il y a plus de 72h", "containers_module.collectedAtTimes.neverCollected": "Jamais collectés", "containers_module.collections_planned_at_day": "Collectes planifiées ce jour", "containers_module.date_selected": "Date", "containers_module.form.clear-address": "Eff<PERSON><PERSON> l'adresse", "containers_module.generic_toast_title": "Cette zone contient un grand nombre de contenants", "containers_module.last_collection.between_24h_and_48h": "Collectés entre 24 et 48h", "containers_module.last_collection.between_48h_and_72h": "Collectés entre 48 et 72h", "containers_module.last_collection.export": "Export", "containers_module.last_collection.export.current_state_value_1": "Contenant collecté sur la journée", "containers_module.last_collection.export.current_state_value_2": "Contenant non prévu non collecté", "containers_module.last_collection.export.current_state_value_3": "Contenant à collecter aujourd'hui", "containers_module.last_collection.export.current_state_value_4": "Contenant non collecté", "containers_module.last_collection.export.fail_message": "Erreur lors de l'export du fichier csv", "containers_module.last_collection.export_file_prefix": "Export_contenant", "containers_module.last_collection.less_than_24h": "Collectés il y a moins de 24h", "containers_module.last_collection.more_than_72h": "Collectés il y a plus de 72h", "containers_module.last_collection.never": "Jamais collectés", "containers_module.last_collection_date": "Date de dernière collecte", "containers_module.map_lasso_complex_polygon_warning": "Veuillez sélectionner une zone avec une forme plus simple", "containers_module.map_search_here_button": "Filtrer dans cette zone", "containers_module.map_search_more_button": "Voir plus", "containers_module.marker_popup.empty_value": "Non renseigné", "containers_module.marker_popup.export_buttons.anomalie_history": "Historique des anomalies", "containers_module.marker_popup.export_buttons.lift_history": "Historique de collecte", "containers_module.marker_popup.infos.chipNumber": "Numéro <PERSON> (Identifiant)", "containers_module.marker_popup.infos.commissioning_date": "Mise en service", "containers_module.marker_popup.infos.state": "État", "containers_module.marker_popup.infos.tankNumber": "Numéro de cuve", "containers_module.marker_popup.infos.title": "Informations contenant", "containers_module.marker_popup.infos.type": "Type", "containers_module.marker_popup.infos.volume": "Volume", "containers_module.marker_popup.last_infos.address": "<PERSON>ress<PERSON> de collecte", "containers_module.marker_popup.last_infos.last_five_collections": "Les 5 dernières collectes", "containers_module.marker_popup.last_infos.last_five_collections.tooltip": "Date: {datetime} - Circuit: {circuitName} - Vehicule {vehicleName}", "containers_module.marker_popup.last_infos.title": "Dernières informations", "containers_module.marker_popup.no_result_provided": "Aucun résultat", "containers_module.marker_popup.state_value_0": "En stock", "containers_module.marker_popup.state_value_1": "En service", "containers_module.marker_popup.state_value_2": "Hors service", "containers_module.return_to_selection": "Retourner à la selection", "containers_module.search_too_many_elements_at_zoom_level": "Veuillez zoomer et/ou filtrer pour afficher leurs positions.", "containers_module.selection.disable_lasso": "<PERSON><PERSON><PERSON> le mode Las<PERSON>", "containers_module.selection.labels.address": "Adresse postale", "containers_module.selection.labels.container_flux": "Flux contenant", "containers_module.selection.labels.cuve": "Numéro de cuve", "containers_module.selection.labels.predefined_views": "Vues prédéfinies", "containers_module.selection.labels.puce": "Numéro de <PERSON>ce", "containers_module.selection.labels.state": "État contenant", "containers_module.selection.labels.type": "Type contenant", "containers_module.selection.labels.zone": "Zone(s)", "containers_module.selection.map_selection_button": "Selection sur Carto", "containers_module.selection.reset_button": "Réinitialiser", "containers_module.settings.section_autorefresh.select_title": "Intervalle d'actualisation", "containers_module.settings.section_autorefresh.title": "Actualisation automatique", "containers_module.settings.title": "Administration des contenants", "containers_module.stateId.1": "Collecté", "containers_module.stateId.2": "Non collecté non prévu", "containers_module.stateId.3": "À collecter aujourd'hui", "containers_module.stateId.4": "Non collecté non prévu", "containers_module.stats.anomalies_count_in_zone": "Anomalies contenants", "containers_module.stats.collected_bacs": "Contenants collectés sur la journée", "containers_module.stats.collected_columns": "colonnes collectées", "containers_module.stats.delayed_columns": "colonnes en retard", "containers_module.stats.not_collected_count": "Contenants non collectés", "containers_module.stats.not_collected_not_planned_count": "Contenants non prévus non collectés", "containers_module.stats.to_be_collected_count": "Contenants à collecter aujourd'hui", "containers_module.stats.today_pending_collected_columns": "colonnes à collecter aujourd’hui", "containers_module.stats.total_containers_count": "Total des contenants du parc", "containers_module.stats.total_containers_zone_count": "Total des contenants sur la zone", "crud_operation_messages.create_fail": "Échéc de la création", "crud_operation_messages.create_success": "Création réussie", "crud_operation_messages.edit_fail": "Échéc de l'édition", "crud_operation_messages.edit_success": "Mise à jour réussie", "crud_operation_messages.remove_fail": "Échéc de la suppression", "crud_operation_messages.remove_success": "Suppression réussie", "dashboard.lastUpdateDatetime": "Date de la dernière mise à jour", "dashboard.last_updated_at": "{date} à {time}", "dashboard.widgets.active_circuits_perc.legend": "Taux de réalisation moyen des circuits en cours", "dashboard.widgets.active_circuits_perc.title": "Taux de réalisation des circuits en cours", "dashboard.widgets.active_gps.legend": "Nombre de véhicules actifs sur les dernières 24 heures par tranche horaire d’activité", "dashboard.widgets.active_gps.title": "Véhicules actifs", "dashboard.widgets.finished_circuits_perc.legend": "Taux de réalisation moyen des circuits clôturés", "dashboard.widgets.finished_circuits_perc.title": "Taux de réalisation des circuits clôturés ( calculé à {time} )", "dashboard.widgets.realtime_vehicle_status.contact_off": "Contact Off", "dashboard.widgets.realtime_vehicle_status.contact_off_72": "Off depuis 72h", "dashboard.widgets.realtime_vehicle_status.legend": "Statut temps réel des véhicules avec nombre de véhicules concernés selon le type de statut", "dashboard.widgets.realtime_vehicle_status.move": "En mouvement", "dashboard.widgets.realtime_vehicle_status.stop": "A l’arrêt", "dashboard.widgets.realtime_vehicle_status.title": "Statut temps réel", "dashboard.widgets.top_anomalies.legend": "Evènements remontés par les équipes d’exploitation par type", "dashboard.widgets.top_anomalies.title": "Évènements", "datatable.all_text": "<PERSON>ut", "datatable.aria.sortAscending": ": activer pour trier la colonne par ordre croissant", "datatable.aria.sortDescending": ": activer pour trier la colonne par ordre décroissant", "datatable.clear_selection_text": "<PERSON><PERSON><PERSON><PERSON>", "datatable.column_filters.placeholder": "<PERSON><PERSON><PERSON>", "datatable.emptyTable": "Aucune donnée disponible dans le tableau", "datatable.first_text": "Premier", "datatable.info": "Affichage de l'élément _START_ à _END_ sur _TOTAL_ éléments", "datatable.infoEmpty": "Affichage de l'élément 0 à 0 sur 0 éléments", "datatable.infoFiltered": "(filtré de _MAX_ éléments au total)", "datatable.last_text": "<PERSON><PERSON>", "datatable.lengthMenu": "Afficher _MENU_ éléments", "datatable.linesPerPage": "Lignes par page", "datatable.loadingRecords": "Chargement en cours…", "datatable.next_text": "Suivant", "datatable.of_text": "de", "datatable.page_text": "page", "datatable.paginate.all": "Toutes", "datatable.paginate.first": "Premier", "datatable.paginate.last": "<PERSON><PERSON>", "datatable.paginate.next": "Suivant", "datatable.paginate.previous": "Précédent", "datatable.pooling_limit_title": "Limitation de la réponse", "datatable.prev_text": "Précédent", "datatable.processing": "Traitement en cours…", "datatable.rows_per_page": "Lignes par page", "datatable.search": "Filtrer :", "datatable.search_placeholder": "Search Table", "datatable.selection_text": "lignes sélectionnées", "datatable.zeroRecords": "Aucun élément à afficher", "datepicker.days.0": "Di", "datepicker.days.1": "<PERSON>", "datepicker.days.2": "Ma", "datepicker.days.3": "Me", "datepicker.days.4": "Je", "datepicker.days.5": "Ve", "datepicker.days.6": "Sa", "datepicker.months.0": "<PERSON><PERSON>", "datepicker.months.1": "<PERSON><PERSON><PERSON>", "datepicker.months.10": "Novembre", "datepicker.months.11": "Decembre", "datepicker.months.2": "Mars", "datepicker.months.3": "Avril", "datepicker.months.4": "<PERSON>", "datepicker.months.5": "Juin", "datepicker.months.6": "<PERSON><PERSON><PERSON>", "datepicker.months.7": "Aout", "datepicker.months.8": "Septembre", "datepicker.months.9": "Octobre", "datepicker.timefrom_after_timeto": "L'heure de début doit être antérieure à l'heure de fin", "diagnostics.analysis.averageCo2": "Moyenne de rejet CO2", "diagnostics.analysis.averageConsumption": "<PERSON><PERSON> moyenne", "diagnostics.analysis.chrono.amplitudeLabel": "Amplitude", "diagnostics.analysis.chrono.serviceTimeLabel": "Temps de service", "diagnostics.analysis.contactOff": "Extinction", "diagnostics.analysis.contactOn": "<PERSON><PERSON> en route", "diagnostics.analysis.contactOnDuration": "Durée contact ON", "diagnostics.analysis.effective_duration": "Du<PERSON>e effective", "diagnostics.analysis.fuelLevel": "Niveau de carburant", "diagnostics.analysis.moveDuration": "<PERSON><PERSON>e de d<PERSON>", "diagnostics.analysis.overview_vehicle_prefix": "Sur le vehicule", "diagnostics.analysis.referenceConsumption": "<PERSON><PERSON> de référence", "diagnostics.analysis.time_range": "Période", "diagnostics.analysis.title_consommation": "Consommation", "diagnostics.analysis.title_details": "Relevé des informations", "diagnostics.analysis.title_sensors": "Informations Capteurs", "diagnostics.analysis.title_wash": "Informations nettoiement", "diagnostics.analysis.totalCo2": "Total rejet CO2", "diagnostics.analysis.totalConsumption": "Conso total", "diagnostics.bottom.calendar_next_day": "<PERSON><PERSON> suivant", "diagnostics.bottom.calendar_prev_day": "<PERSON><PERSON>", "diagnostics.bottom.calendar_tooltip": "Afficher le formulaire de recherche", "diagnostics.bottom.chart_options_button_tooltip": "Choisir les graphiques à afficher", "diagnostics.bottom.period_label": "Période&nbsp;du&nbsp;:", "diagnostics.bottom.period_label_vehicle": "sur le véhicule", "diagnostics.bottom.replay.next": "Avancer", "diagnostics.bottom.replay.play": "<PERSON><PERSON><PERSON><PERSON>", "diagnostics.bottom.replay.prev": "<PERSON><PERSON><PERSON>", "diagnostics.bottom.replay.stop": "<PERSON><PERSON><PERSON><PERSON>", "diagnostics.bottom.slider_tooltip": "Choisir la plage horaire à afficher sur les graphiques", "diagnostics.chart.brush_toggle_button_title": "Activer/Désactiver la sélection d'une plage horaire", "diagnostics.chart.category.canBatteryPerc": "<PERSON><PERSON>ie", "diagnostics.chart.category.canConsumptionLiters": "Consommation litres", "diagnostics.chart.category.canRPM": "RPM", "diagnostics.chart.category.chrono": "Chrono", "diagnostics.chart.category.distanceMeters": "Distance (m)", "diagnostics.chart.category.fuelLevelLiters": "Niveau de carburant (L)", "diagnostics.chart.category.fuelLevelPerc": "Niveau de carburant (%)", "diagnostics.chart.category.normal_gps_positions": "Position GPS", "diagnostics.chart.category.sensorCanBatteryPerc": "<PERSON><PERSON>ie", "diagnostics.chart.category.sensorCanBrakePedal": "Pédale de frein", "diagnostics.chart.category.sensorCanConsumptionLiters": "Consommation litres", "diagnostics.chart.category.sensorCanThrottle": "Accélération", "diagnostics.chart.category.sensorcanfuelliters": "Niveau de carburant", "diagnostics.chart.category.speExcessiveSpeed": "Vitesse excessive", "diagnostics.chart.category.speHarshAcceleration": "Accélération brusque", "diagnostics.chart.category.speHarshBraking": "Freinage brusque", "diagnostics.chart.category.speHarshCornering": "Manœuvre soudaine", "diagnostics.chart.category.speed": "Vitesse", "diagnostics.chart.category.sweepBrushCentral": "Brosse centrale active", "diagnostics.chart.category.sweepBrushCentralCount": "Activations brosse centrale", "diagnostics.chart.category.sweepBrushLeft": "Brosse gauche active", "diagnostics.chart.category.sweepBrushLeftCount": "Activations brosse gauche", "diagnostics.chart.category.sweepBrushRight": "Brosse droite active", "diagnostics.chart.category.sweepBrushRightCount": "Activations brosse droite", "diagnostics.chart.category.sweepBrushSp": "Vitesse de la brosse", "diagnostics.chart.category.sweepFan": "Travail de la turbine", "diagnostics.chart.category.sweepFanCount": "Travaux de la turbine", "diagnostics.chart.category.sweepFanSp": "Vitesse de la turbine", "diagnostics.chart.category.sweepHopRaised": "Vidage de la cuve", "diagnostics.chart.category.sweepHopRaisedCount": "Vidages de la cuve", "diagnostics.chart.category.sweepHpPump": "Pompe haute pression", "diagnostics.chart.category.sweepLevel": "Niveau d'eau dans la cuve", "diagnostics.chart.category.sweepPump": "Pompe d'humectage", "diagnostics.chart.category.sweepPumpCount": "Activations pompe d'humectage", "diagnostics.chart.category.sweepRearDoor": "Porte arrière fermée", "diagnostics.chart.category.sweepRearDoorCount": "Fermetures porte arrière", "diagnostics.chart.category.sweepWaterCentral": "Humectage brosse centrale", "diagnostics.chart.category.sweepWaterCentralCount": "Humectages brosse centrale", "diagnostics.chart.category.sweepWaterFront": "Humectage brosse avant", "diagnostics.chart.category.sweepWaterFrontCount": "Humectages brosse avant", "diagnostics.chart.category.sweepWaterLaterals": "Humectage brosse laterals", "diagnostics.chart.category.sweepWaterLateralsCount": "Humectages brosses latérales", "diagnostics.chart.category.sweepWaterLeft": "Humectage brosse gauche", "diagnostics.chart.category.sweepWaterLeftCount": "Humectages brosse gauche", "diagnostics.chart.category.sweepWaterRight": "Humectage brosse droite", "diagnostics.chart.category.sweepWaterRightCount": "Humectages brosse droite", "diagnostics.chart.category.vehicleDistance": "Distance véhicule", "diagnostics.chart.category.washFlowRangeBackL": "Débit d'eau arrière gauche", "diagnostics.chart.category.washFlowRangeBackLAvg": "<PERSON><PERSON><PERSON> moyen d'eau arrière gauche", "diagnostics.chart.category.washFlowRangeBackR": "Débit d'eau arrière droite", "diagnostics.chart.category.washFlowRangeBackRAvg": "<PERSON><PERSON><PERSON> moyen d'eau arrière droite", "diagnostics.chart.category.washFlowRangeFrontL": "Débit d'eau avant gauche", "diagnostics.chart.category.washFlowRangeFrontLAvg": "<PERSON><PERSON><PERSON> moyen d'eau avant gauche", "diagnostics.chart.category.washFlowRangeFrontR": "Débit d'eau avant droite", "diagnostics.chart.category.washFlowRangeFrontRAvg": "<PERSON><PERSON><PERSON> moyen d'eau avant droite", "diagnostics.chart.category.washFlowRangePole": "<PERSON><PERSON><PERSON> d'eau lance", "diagnostics.chart.category.washFlowRangePoleAvg": "<PERSON><PERSON><PERSON> moyen d'eau lance", "diagnostics.chart.category.washFlowRangeRamp": "<PERSON><PERSON>bit d'eau rampe", "diagnostics.chart.category.washFlowRangeRampAvg": "<PERSON><PERSON><PERSON> moyen d'eau rampe", "diagnostics.chart.category.washFlowRateBackL": "Débit d'eau arrière gauche", "diagnostics.chart.category.washFlowRateBackR": "Débit d'eau arrière droite", "diagnostics.chart.category.washFlowRateFrontL": "Débit d'eau avant gauche", "diagnostics.chart.category.washFlowRateFrontR": "Débit d'eau avant droite", "diagnostics.chart.category.washFlowRatePole": "<PERSON><PERSON><PERSON> d'eau lance", "diagnostics.chart.category.washFlowRateRamp": "<PERSON><PERSON>bit d'eau rampe", "diagnostics.chart.category.washLevel": "Niveau d'eau", "diagnostics.chart.category.washPressure": "Pression de l'eau", "diagnostics.chart.speed_alert_tooltip": "Alerte vitesse", "diagnostics.chart.toolbar.moveleft": "Déplacer les graphiques vers la gauche", "diagnostics.chart.toolbar.moveright": "Déplacer les graphiques vers la droite", "diagnostics.chart.toolbar.zoomfit": "Réinitialiser le zoom sur les graphiques", "diagnostics.chart.toolbar.zoomin": "Zoomer les graphiques", "diagnostics.chart.toolbar.zoomout": "Dézoomer les graphiques", "diagnostics.chart.units.conso100km": "L/100km", "diagnostics.chart.units.liters": "L", "diagnostics.chart.units.vrm": "Tours/mn", "diagnostics.chart_settings_view.ana": "Analogiques", "diagnostics.chart_settings_view.back_label": "Retourner aux informations de la période", "diagnostics.chart_settings_view.can_label": "Informations CAN", "diagnostics.chart_settings_view.can_specific_label": "Informations conduite", "diagnostics.chart_settings_view.chrono": "Chronotachygraphe", "diagnostics.chart_settings_view.mandatory_label": "Informations GPS", "diagnostics.chart_settings_view.sweep_wash": "Informations de nettoyage", "diagnostics.chart_settings_view.tor": "Informations capteurs", "diagnostics.comparison_hint": "Sélectionnez une ou deux positions pour afficher le panneau d'analyse de segment.", "diagnostics.datepicker_tooltip": "Il est possible d'effectuer une recherche sur 24h", "diagnostics.no_results": "Aucune donnée disponible pour ce véhicule et cette date", "diagnostics.search.datetimepicker_label": "Sélectionner une date et heure de début", "ecoconduite.Absorbable par": "Absorbable par", "ecoconduite.Accélération": "Accélération", "ecoconduite.Aucune donnée disponible dans le tableau": "Aucune donnée disponible dans le tableau", "ecoconduite.Benchmark": "Benchmark", "ecoconduite.Comparer les Véhicules": "Comparer les Véhicules", "ecoconduite.Consommation": "Consommation", "ecoconduite.Date du rapport": "Date du rapport", "ecoconduite.Différence": "<PERSON>ff<PERSON><PERSON><PERSON>", "ecoconduite.Exporter au format PDF": "Exporter au format PDF", "ecoconduite.Exporter au format XLS": "Exporter au format XLS", "ecoconduite.Freinage": "<PERSON><PERSON><PERSON>", "ecoconduite.Lignes par page": "Lignes par page", "ecoconduite.Moyenne": "<PERSON><PERSON><PERSON>", "ecoconduite.Note conduite": "Note conduite", "ecoconduite.RejectCO2": "Rejet de CO²", "ecoconduite.Rejet": "Rejet", "ecoconduite.Rejet Moyen": "<PERSON><PERSON>", "ecoconduite.Référence": "Référence", "ecoconduite.Régime Moteur": "<PERSON><PERSON><PERSON><PERSON>", "ecoconduite.chaque jour": "<PERSON><PERSON> jour", "ecoconduite.chauffeur": "<PERSON><PERSON><PERSON>", "ecoconduite.datepicker.by_day": "Par jour", "ecoconduite.datepicker.by_period": "Par <PERSON><PERSON><PERSON>", "ecoconduite.details.distance.title": "Distances", "ecoconduite.details.distance.total": "Totale", "ecoconduite.details.distance.while_accelerating": "En accélération", "ecoconduite.details.distance.while_breaking": "En freinage", "ecoconduite.details.distance.while_declutching": "En débrayage", "ecoconduite.details.distance.while_free_wheeling": "En roue libre", "ecoconduite.details.distance.while_working": "En travail", "ecoconduite.details.elapsed_time": "<PERSON><PERSON><PERSON>", "ecoconduite.details.engine_brakes": "<PERSON><PERSON><PERSON>", "ecoconduite.details.engine_eco_drive": "En conduite éco", "ecoconduite.details.engine_off": "<PERSON><PERSON><PERSON>", "ecoconduite.details.engine_on": "<PERSON><PERSON><PERSON> tournant", "ecoconduite.details.engine_on_off_total": "Arrêts moteur tournant (> 180s.)", "ecoconduite.details.engine_rotation_over": "En surrégime", "ecoconduite.details.engine_rotation_under": "En sous-régime", "ecoconduite.details.excessive_speed_up": "Accélération excessive", "ecoconduite.details.free_wheel": "Roue libre", "ecoconduite.details.fuel_consuming.average_consuming": "<PERSON><PERSON> moyenne", "ecoconduite.details.fuel_consuming.c02_else": "soit", "ecoconduite.details.fuel_consuming.cO2_rejects": "Rejet CO2", "ecoconduite.details.fuel_consuming.consumption_while_moving": "Conso en mouvement", "ecoconduite.details.fuel_consuming.measured_lost_consumption": "Conso perdue mesurée", "ecoconduite.details.fuel_consuming.title": "Consommation de carburant", "ecoconduite.details.fuel_consuming.total": "Conso totale", "ecoconduite.details.fuel_consuming.while_working_consuming": "Conso en travail", "ecoconduite.details.period": "Activité", "ecoconduite.details.phases.accelerates": "Accélération", "ecoconduite.details.phases.brakes": "<PERSON><PERSON><PERSON>", "ecoconduite.details.phases.declutch": "Débrayages", "ecoconduite.details.phases.free_wheel": "Roue libre", "ecoconduite.details.phases.overload": "Surrégime", "ecoconduite.details.phases.stops": "Arrêts (v = 0)", "ecoconduite.details.phases.stops_engine_braked": "Arrêts avec frein de parking", "ecoconduite.details.phases.stops_engine_on": "Arrêts moteur tournant (arrêt > 60 s. + frein de parking)", "ecoconduite.details.phases.title": "Phases", "ecoconduite.details.slowed_down": "<PERSON><PERSON><PERSON> (pas de déplacement, pas de travail)", "ecoconduite.details.specific": "Sans travail / Freinage / Embrayage (v > 0)", "ecoconduite.details.speed": "Déplacement (vitesse > 0)", "ecoconduite.details.speed_accelerating.average_accelerating_pedal_push": "Ecrasement moyen accélerateur", "ecoconduite.details.speed_accelerating.average_acceleration": "Accélération moyenne (mesure kinétic)", "ecoconduite.details.speed_accelerating.average_braking_pedal_push": "Ecrasement moyen pédale de frein", "ecoconduite.details.speed_accelerating.average_engine_rotation": "<PERSON><PERSON><PERSON><PERSON> moteur moyen", "ecoconduite.details.speed_accelerating.average_speed": "Vitesse moyenne (quand v > 0)", "ecoconduite.details.speed_accelerating.title": "Vitesse et accélération", "ecoconduite.details.speed_ups": "Accélération", "ecoconduite.details.tab.charts": "Graphique", "ecoconduite.details.tab.details": "Détail", "ecoconduite.details.tab.map": "<PERSON><PERSON>", "ecoconduite.details.tab.rank": "Note", "ecoconduite.details.title": "<PERSON>é<PERSON> de la période", "ecoconduite.details.total": "Total", "ecoconduite.details.while_driving": "En conduite", "ecoconduite.details.working": "Travail", "ecoconduite.empty": "Aucune données pour la sélection.", "ecoconduite.error": "Une erreur s'est produite, contactez l'administrateur", "ecoconduite.graph.title": "Diagnostique Eco Conduite", "ecoconduite.map.empty": "Pas de données de positions disponibles", "ecoconduite.map.legend": "Consommation moyenne (l/100km, FMS)", "ecoconduite.map.reference": "Référence", "ecoconduite.map.title": "Représentation cartographique de la consommation", "ecoconduite.no_date_selected": "<PERSON><PERSON><PERSON><strong>date</strong> sélectionnée.", "ecoconduite.période": "Période", "ecoconduite.rank.anticipated": "Anticipation", "ecoconduite.rank.braking": "<PERSON><PERSON><PERSON>", "ecoconduite.rank.coef": "coef", "ecoconduite.rank.endpoints_title": "Définition des seuils et variables", "ecoconduite.rank.engine_over": "Surrégime", "ecoconduite.rank.engine_under": "Sous régime", "ecoconduite.rank.flex_acceleration": "Souplesse accélération", "ecoconduite.rank.global": "Globale", "ecoconduite.rank.infos.accs.desc": "Identification du temps ou le seuil défini est dépassé. On divise cette durée par la durée où l'accélérateur est actionné et on multiplie le tout par 10.", "ecoconduite.rank.infos.accs.explain1": "Temps en déplacement en accélération = 3h15 = 195 minutes", "ecoconduite.rank.infos.accs.explain2": "Temps en déplacement en accélération excessive = 45 minutes", "ecoconduite.rank.infos.accs.explain3": "(45/195) x 10 = 2,5", "ecoconduite.rank.infos.accs.explain4": "Note = 10 - 2,5 = 7,5 / 10", "ecoconduite.rank.infos.accs.title": "Gestion des accélérations", "ecoconduite.rank.infos.anticipation.desc": "On détermine une 'durée de déplacement en roue libre'", "ecoconduite.rank.infos.anticipation.explain1": "Temps en déplacement en accélération = 3h15 = 195 minutes", "ecoconduite.rank.infos.anticipation.explain2": "Temps en déplacement en roue libre = 2h15 = 135 minutes", "ecoconduite.rank.infos.anticipation.explain3": "Note = (135/195) x 10 = 6,9 / 10", "ecoconduite.rank.infos.anticipation.title": "Anticipations en conduite", "ecoconduite.rank.infos.brakes.desc": "La note est obtenue en divisant le nombre de phase d'arrêt par le nombre de phrase de freinage et multipliant le tout par 10.", "ecoconduite.rank.infos.brakes.explain1": "66 phases de freinages et 48 phases d'arrêts", "ecoconduite.rank.infos.brakes.explain2": "Note = (48 / 66) x 10 = 7,3/10", "ecoconduite.rank.infos.brakes.title": "Gestion des freinages", "ecoconduite.rank.infos.engine_over.desc": "Identification du temps ou le seuil défini est dépassé. On divise cette durée par la durée où le moteur est en sur-régime et on multiplie le tout par 10.", "ecoconduite.rank.infos.engine_over.explain1": "Temps en déplacement = 3h15 = 195 minutes", "ecoconduite.rank.infos.engine_over.explain2": "Temps en déplacement en sur-régime = 45 minutes", "ecoconduite.rank.infos.engine_over.explain3": "(45/195) x 10 = 2,5", "ecoconduite.rank.infos.engine_over.explain4": "Note = 10 - 2,5 = 7,5 / 10", "ecoconduite.rank.infos.engine_over.title": "Gestion des surrégimes moteur", "ecoconduite.rank.infos.engine_under.desc": "dentification du temps ou le seuil défini est dépassé. On divise cette durée par la durée où le moteur est en sous-régimes et on multiplie le tout par 10.", "ecoconduite.rank.infos.engine_under.explain1": "Temps en déplacement = 3h15 = 195 minutes", "ecoconduite.rank.infos.engine_under.explain2": "Temps en déplacement en sous-régimes = 45 minutes", "ecoconduite.rank.infos.engine_under.explain3": "(45/195) x 10 = 2,5", "ecoconduite.rank.infos.engine_under.explain4": "Note = 10 - 2,5 = 7,5 / 10", "ecoconduite.rank.infos.engine_under.title": "Gestion des sous-régimes moteur", "ecoconduite.rank.infos.example": "Exemple", "ecoconduite.rank.infos.stops.desc": "Identification du temps ou le véhicule est en contact ON avec le frein de parc. Comptabilisation du temps au-delà d'un seuil défini.", "ecoconduite.rank.infos.stops.example": "Note = 10 x 0,99^(1161/30) = 6,8 / 10", "ecoconduite.rank.infos.stops.example_per_60": "Pour 60mn -> Note 3/10", "ecoconduite.rank.infos.stops.example_per_90": "Pour 90mn -> Note 1,6/10", "ecoconduite.rank.infos.stops.explain": "Pour 19mn21 (1161s) de temps total d'arrêt moteur tournant en plus du seuil autorisé", "ecoconduite.rank.infos.stops.title": "Gestion des arrêts", "ecoconduite.rank.initcoef": "Réinitialiser les coefs", "ecoconduite.rank.stops": "<PERSON><PERSON><PERSON><PERSON>", "ecoconduite.rank.title": "Note", "ecoconduite.ranking_details": "<PERSON><PERSON><PERSON> de la note", "ecoconduite.search_fail": "Nous sommes désolés, nous ne sommes pas en mesure de récupérer ces informations pour le moment. Veuillez réessayer ultérieurement.", "ecoconduite.search_selection.title": "Filtres", "ecoconduite.surregime": "Surrégime", "ecoconduite.table_column.avg_speed": "Vit.", "ecoconduite.table_column.avg_speed_tooltip": "Vitesse moyenne", "ecoconduite.table_column.avg_vehicle_acceleration_percentage": "Acc.", "ecoconduite.table_column.avg_vehicle_acceleration_percentage_tooltip": "% d'écrasement moyen de la pédale d'accélérateur", "ecoconduite.table_column.avg_vehicle_breaking_percentage": "<PERSON><PERSON><PERSON>", "ecoconduite.table_column.avg_vehicle_breaking_percentage_tooltip": "% d'écrasement moyen de la pédale de frein", "ecoconduite.table_column.co2": "CO2", "ecoconduite.table_column.co2_tooltip": "Rejet CO2", "ecoconduite.table_column.consumption": "Consommation totale", "ecoconduite.table_column.consumption_avg_global": "Globale", "ecoconduite.table_column.consumption_avg_move": "Mvt.", "ecoconduite.table_column.consumption_avg_work": "Trav.", "ecoconduite.table_column.consumption_tooltip": "Tot.", "ecoconduite.table_column.distance": "Dist.", "ecoconduite.table_column.ranking": "Note", "ecoconduite.table_column.time_vehicle_eco": "Eco", "ecoconduite.table_column.time_vehicle_eco_tooltip": "Temps passé en conduite éco", "ecoconduite.table_column.time_vehicle_high_rpm": "Sur", "ecoconduite.table_column.time_vehicle_high_rpm_tooltip": "% du temps passé en sur régime avec véhicule en déplacement", "ecoconduite.table_column.time_vehicle_low_rpm": "Sous", "ecoconduite.table_column.time_vehicle_low_rpm_tooltip": "% du temps passé en sous régime avec véhicule en déplacement", "ecoconduite.table_column.time_vehicle_move_while_breaking": "Tps freinage", "ecoconduite.table_column.time_vehicle_move_while_breaking_tooltip": "Temps total véhicule en déplacement avec écrasement de la pédale de frein", "ecoconduite.table_column.time_vehicle_slow": "<PERSON><PERSON><PERSON>", "ecoconduite.table_column.time_vehicle_slow_tooltip": "<PERSON><PERSON> ralenti", "ecoconduite.table_column.time_vehicle_stop": "<PERSON><PERSON><PERSON><PERSON>", "ecoconduite.table_column.time_vehicle_stop_tooltip": "Temps total véhicule arrêté avec moteur tournant", "ecoconduite.table_column.vehicle_breaking_stop_count": "<PERSON><PERSON>/<PERSON>", "ecoconduite.table_column.vehicle_breaking_stop_count_tooltip": "Nombre freinage / Nombre d’arrêts", "ecoconduite.table_column.waste_in_liters": "<PERSON><PERSON><PERSON><PERSON>", "ecoconduite.table_column.waste_in_liters_tooltip": "Litre Gâchis", "error.browser_private_mode": "L'application ne fonctionne pas en mode privé sur ce navigateur", "error.responsive_minimum_width": "Un écran d'une largeur minimale de 360 pixels est requis", "event_types.status.annulation_guidage_bav": "Annulation guidage BAV", "event_types.status.collecte_bav": "Collecte BAV", "event_types.status.debut_guidage_bav": "Debut guidage BAV", "event_types.status.fin_guidage_bav": "Fin guidage BAV", "event_types.status.ignore_bav": "Ignore BAV", "event_types.status.releve_bav": "Relève BAV", "events.anomaly_label.bav_collect": "COLLECTE_BAV", "events.anomaly_label.bav_ignore": "IGNORE_BAV", "events.anomaly_label.bav_releve": "RELEVE_BAV", "events.anomaly_label.guidance_cancellation": "ANNULATION_GUIDAGE_BAV", "events.anomaly_label.guidance_end": "FIN_GUIDAGE_BAV", "events.anomaly_label.guidance_start": "DEBUT_GUIDAGE_BAV", "events.anomaly_label.other": "<PERSON><PERSON>", "events.events_table.work_done_label": "Travail effectué", "events.filters_tootip": "Appuyez sur Shift+Click pour sélectionner plusieurs éléments et Ctrl+Click pour désélectionner.", "events.no_results": "Aucune donnée disponible pour cette période", "events.ot_number": "Numéro de référence", "events.ot_status": "Statut de l'OT", "events.reported_anomaly": "Anomalie signalée", "events.search_filters.anomaly_title": "Afficher les anomalies", "events.search_filters.bin_group_label": "Numéro de puce et Côté de levée", "events.search_filters.extra_information_title": "Afficher les informations", "events.search_filters.operation_message_title": "Afficher les évènements d'exploitation", "events.search_filters.round_group_label": "Nom du circuit", "events.search_filters.status_title": "Afficher les statuts", "events.table.header_text.circuit": "Résultats du circuit {circuitName} du {date}", "events.table.header_text.circuit_aggregate": "Résultats du circuit {circuitName} du {date} (regroupés par type {type }et code {code})", "events.table.header_text.date": "Résultats du {date}", "events.table.header_text.date_aggregate": "Résultats du {date} (regroupés par type {type} et code {code})", "events.table.header_text.total": "Résultats du {fromDate} au {toDate}", "events.table.header_text.vehicle": "Résultats du véhicule {vehicleName} du {date}", "events.table.header_text.vehicle_aggregate": "Résultats du véhicule {vehicleName} du {date} (regroupés par type {type} et code {code})", "events.table_column.circuit_name": "Circuit", "events.table_column.puce_number": "N° Puce", "events.table_column.side": "<PERSON><PERSON><PERSON>", "events.table_column_side_arm": "Bras", "events.table_column_side_bicomp": "Bicomp", "events.table_column_side_combine": "Combiné", "events.table_column_side_left": "G<PERSON><PERSON>", "events.table_column_side_pedestrian": "<PERSON><PERSON>", "events.table_column_side_right": "<PERSON><PERSON><PERSON>", "export_feature.boolean_value_no": "non", "export_feature.boolean_value_yes": "oui", "export_feature.create_circuit_export_button": "Générer un circuit à partir de l'historique", "export_feature.create_circuit_form.circuitCategory": "Catégorie du circuit", "export_feature.create_circuit_form.circuitName": "Nom du circuit", "export_feature.create_circuit_form.defaultActivity": "Activité par défaut", "export_feature.create_circuit_form.transportType": "Mode de transport", "export_feature.create_circuit_modal_title": "Création d'un circuit à partir de l'historique", "export_feature.csv_export": "Export CSV", "export_feature.file_export_fail": "Échec de l'exportation", "export_feature.file_export_in_progress": "Exportation du fichier en cours", "export_feature.file_export_success": "Exportation effectuée avec succès", "export_feature.file_loading": "Chargement du fichier, veuille<PERSON> patienter", "export_feature.history_vehicle.csv_column.address": "<PERSON><PERSON><PERSON>", "export_feature.history_vehicle.csv_column.big_bin": "Grand Bac", "export_feature.history_vehicle.csv_column.city": "<PERSON><PERSON><PERSON>", "export_feature.history_vehicle.csv_column.contact": "Contact", "export_feature.history_vehicle.csv_column.door_opening": "Ouverture Porte", "export_feature.history_vehicle.csv_column.fix": "Fix", "export_feature.history_vehicle.csv_column.gps_quality": "Qualité GPS", "export_feature.history_vehicle.csv_column.incident": "Incident", "export_feature.history_vehicle.csv_column.incident_code": "Codeincident", "export_feature.history_vehicle.csv_column.latitude": "Latitude", "export_feature.history_vehicle.csv_column.left_bin": "Bac Gau<PERSON>", "export_feature.history_vehicle.csv_column.left_riper": "<PERSON><PERSON><PERSON>", "export_feature.history_vehicle.csv_column.longitude": "Longitude", "export_feature.history_vehicle.csv_column.message": "Message", "export_feature.history_vehicle.csv_column.message_code": "Codemessage", "export_feature.history_vehicle.csv_column.parc_number": "N° parc", "export_feature.history_vehicle.csv_column.reverse": "<PERSON><PERSON>", "export_feature.history_vehicle.csv_column.right_bin": "<PERSON><PERSON>", "export_feature.history_vehicle.csv_column.right_riper": "<PERSON><PERSON><PERSON>", "export_feature.history_vehicle.csv_column.satellite_number": "Nombre de Satellite", "export_feature.history_vehicle.csv_column.speed": "Vitesse (km/h)", "export_feature.history_vehicle.csv_column.start": "Demarrage", "export_feature.history_vehicle.csv_column.time": "<PERSON><PERSON>", "export_feature.history_vehicle.csv_column.tor": "TOR Décimal", "export_feature.history_vehicle.csv_column.weigh": "PESEE", "export_feature.history_vehicle.file_prefix": "historique", "export_feature.kml_export_button": "Export KML", "export_feature.pendulum_export_button": "Pendulage", "export_feature.shape_export_button": "Export Shape", "export_feature.table_prefixes.alerts": "alertes", "export_feature.table_prefixes.location": "localisation", "export_feature.table_prefixes.locationAlerts": "localisation-details-alertes", "export_feature.table_prefixes.locationCircuits": "localisation-circuits", "export_feature.table_prefixes.locationCrew": "localisation-equipage", "export_feature.table_prefixes.locationDetailsChrono": "localisation-details-chrono", "export_feature.table_prefixes.locationDetailsCircuit": "localisation-execution-circuit", "export_feature.table_prefixes.locationEvents": "localisation-details-evenements", "export_feature.table_prefixes.locationHistory": "localisation-details-historique", "export_feature.table_prefixes.locationIdentification": "localisation-details-identification", "export_feature.table_prefixes.locationMissions": "localisation-missions", "export_feature.table_prefixes.locationSensors": "localisation-capteurs", "export_feature.toast_title": "Exportation", "export_feature_create_circuit_progress_toast": "Circuit en cours de génération…", "favorites.actions.add_search": "Ajouter la recherche aux favoris", "favorites.actions.delete": "<PERSON><PERSON><PERSON><PERSON>", "favorites.actions.edit": "Editer", "favorites.actions.form.cancel": "Annuler", "favorites.actions.form.frequency": "Temporalité", "favorites.actions.form.name": "Nom du favori", "favorites.actions.form.search": "Recherche", "favorites.actions.form.submit": "Valider", "favorites.helpers.no_data": "Aucun favori à afficher", "favorites.strings.current_month": "Mois en cours", "favorites.strings.current_week": "<PERSON><PERSON><PERSON> en cours", "favorites.strings.day_before_yesterday": "Avant-hier", "favorites.strings.previous_month": "<PERSON><PERSON>", "favorites.strings.previous_week": "<PERSON><PERSON><PERSON>", "favorites.strings.yesterday": "<PERSON>er", "geocoding.autocomplete.deselect_hint": "Cliquez pour désélectionner", "geocoding.autocomplete.select_hint": "Cliquez pour sélectionner", "geocoding.autocomplete.selected_label": "<PERSON><PERSON>", "geocoding.buttons.table_mode": "Affichage tableau", "geocoding.instructions.AND": "puis", "geocoding.instructions.AT": "À", "geocoding.instructions.AT_EXIT": "à la", "geocoding.instructions.DIRECTION": "en direction de", "geocoding.instructions.DURING": "durant environ", "geocoding.instructions.FERRY": "ferry", "geocoding.instructions.FERRY_CHANGE": "changer de ferry", "geocoding.instructions.FERRY_ENTER": "pre<PERSON><PERSON> le ferry", "geocoding.instructions.FERRY_EXIT": "quitter le ferry", "geocoding.instructions.FOLLOW": "suivre ", "geocoding.instructions.FOLLOW_SIGN": "suivre les panneaux ", "geocoding.instructions.FOR_ROAD": "pour", "geocoding.instructions.IN": "dans environ", "geocoding.instructions.INSTRUCTIONS": "Instructions", "geocoding.instructions.LEFT": "tourner à gauche", "geocoding.instructions.LEFT_BEAR": "rester à gauche", "geocoding.instructions.LEFT_LEFT": "tourner à gauche puis immédiatement à gauche", "geocoding.instructions.LEFT_RIGHT": "tourner à gauche puis immédiatement à droite", "geocoding.instructions.LEFT_SHARP": "tourner à gauche", "geocoding.instructions.LEFT_SLIGHT": "tourner à gauche", "geocoding.instructions.MOTORWAY": "autoroute", "geocoding.instructions.MOTORWAY_CHANGE": "changer d'autoroute", "geocoding.instructions.MOTORWAY_ENTER": "entrer sur l'autoroute", "geocoding.instructions.MOTORWAY_EXIT": "prendre la sortie ", "geocoding.instructions.MOTORWAY_MERGE": "continuer sur la jonction d'autoroute", "geocoding.instructions.ON_ROAD": "sur", "geocoding.instructions.RIGHT": "tourner à droite", "geocoding.instructions.RIGHT_BEAR": "rester à droite", "geocoding.instructions.RIGHT_LEFT": "tourner à droite puis immédiatement à gauche", "geocoding.instructions.RIGHT_RIGHT": "tourner à droite puis immédiatement à droite", "geocoding.instructions.RIGHT_SHARP": "tourner à droite", "geocoding.instructions.RIGHT_SLIGHT": "tourner à droite", "geocoding.instructions.ROUNDABOUT": "prendre le rond point", "geocoding.instructions.ROUNDABOUT_ENTER": "prendre le rond point", "geocoding.instructions.ROUNDABOUT_EXIT": " et sortir à la  ", "geocoding.instructions.ROUNDABOUT_EXIT_NUMBER": "e sortie", "geocoding.instructions.ROUTING_SENTENCE_1": "L'itinéraire fait", "geocoding.instructions.ROUTING_SENTENCE_2": "pour", "geocoding.instructions.ROUTING_SENTENCE_3": " de vitesse moyenne", "geocoding.instructions.STOP": "s'arreter ", "geocoding.instructions.STRAIGHT": "continuer", "geocoding.instructions.TEMPS": "Temps", "geocoding.instructions.TO": "vers", "geocoding.instructions.UNKNOWN": "continuer", "geocoding.instructions.USE_PASSAGE": "traverser le passage ", "geocoding.instructions.USE_RAMP": "prend<PERSON> la rampe ", "geocoding.instructions.USE_STAIRS": "prend<PERSON> les escaliers ", "geocoding.instructions.U_TURN": "faire demi-tour", "geocoding.locate_address.select__option_form": "<PERSON><PERSON>", "geocoding.locate_address.select__option_free": "Saisie libre", "geocoding.locate_address.select__option_latlng": "Latitude/Longitude", "geocoding.locate_address_form_select.placeholder": "<PERSON>sie d'adresse par formulaire", "geocoding.locate_address_form_select.tooltip": "Cliquez pour rechercher", "geocoding.map_marker_label": "Position de recherche", "geocoding.map_options.instructions": "Calcul d'itinéraire", "geocoding.not_address_found": "<PERSON><PERSON><PERSON> adresse trouvée", "geocoding.routing.add_address": "Ajouter une étape", "geocoding.routing.avoidFerries": "Eviter les ferries", "geocoding.routing.avoidMotorways": "Eviter les autoroutes", "geocoding.routing.avoidTolls": "E<PERSON><PERSON> les péages", "geocoding.routing.client_zone_select.deselect_hint": "Cliquez pour désélectionner", "geocoding.routing.client_zone_select.no_options": "Chargement en cours…", "geocoding.routing.client_zone_select.no_results": "Il n'y a pas de résultats", "geocoding.routing.client_zone_select.reverse_address_fail": "L'adresse n'a pas pu être récupérée ({zoneName})", "geocoding.routing.client_zone_select.select_hint": "Cliquez pour sélectionner", "geocoding.routing.client_zone_select.selected": "<PERSON><PERSON>", "geocoding.routing.client_zone_select.tooltip": "Localiser une adresse (Choix par zone)", "geocoding.routing.fastest": "Trajet le plus rapide", "geocoding.routing.hazardous_materials.all": "<PERSON>ut", "geocoding.routing.hazardous_materials.explosive": "Explosif", "geocoding.routing.hazardous_materials.none": "Aucun", "geocoding.routing.hazardous_materials.water": "<PERSON><PERSON>", "geocoding.routing.list_go_back_to_search": "Retourner à la recherche", "geocoding.routing.shortest": "Trajet le plus court", "geocoding.routing.speedFeature": "Pondération de vitesse", "geocoding.routing.speedFeature.factor": "Facteur", "geocoding.routing.speedFeature.level": "Niveau", "geocoding.routing.transportType": "Type de véhicule", "geocoding.routing.transport_type.car": "Voiture", "geocoding.routing.transport_type.truck": "Camion", "geocoding.routing.vehicleFeature": "Caractéristiques du véhicule", "geocoding.routing.vehicleFeature_axleWeight": "Poids des axes", "geocoding.routing.vehicleFeature_hazardousMaterials": "Produits dangereux", "geocoding.routing.vehicleFeature_height": "<PERSON><PERSON>", "geocoding.routing.vehicleFeature_length": "<PERSON><PERSON><PERSON>", "geocoding.routing.vehicleFeature_maxSpeed": "Vitesse maximum", "geocoding.routing.vehicleFeature_weight": "Poids", "geocoding.routing.vehicleFeature_width": "<PERSON><PERSON>", "geocoding.simpliciti_locate_address.client_zone.placeholder": "Choix d’une zone dans la liste de zones", "geocoding.simpliciti_locate_address.form.placeholder": "<PERSON>sie d'adresse par formulaire", "geocoding.simpliciti_locate_address.free.placeholder": "<PERSON><PERSON> d'adresse libre", "geocoding.tool_title.locate_address": "Localiser une adresse", "geocoding.tool_title.locate_address_latlng": "Localiser une adresse (Latitude/Longitude)", "geocoding.tool_title.locate_address_natural": "Localiser une adresse (Saisie libre)", "geocoding.tool_title.predefined_views": "Vues prédéfinies", "geocoding.tool_title.routing": "Calculer un itinéraire", "helpers.alerts.no_data": "Aucune information à afficher, veuillez vérifier les catégories et dates sélectionnées", "helpers.tooltips.no_data": "Aucune info à afficher", "i18n.selector_label.default": "Veuillez choisir une option", "i18n.selector_label.en": "<PERSON><PERSON><PERSON>", "i18n.selector_label.es": "Espagnol", "i18n.selector_label.fr": "Français", "i18n.selector_label.nl": "Néerlandais", "i18n.selector_label.title": "<PERSON><PERSON>", "identification.filters_is_blacklist": "Blacklist", "identification.filters_is_high_point": "Point haut", "identification.filters_is_identified": "Identifié", "identification.filters_is_stopped": "Stoppé", "identification.filters_memory_chip_number_placeholder": "Insérer le numéro", "identification.filters_memory_chip_number_title": "Numéro de puce mémoire", "identification.filters_puce_input_placeholder": "Insérer le numéro", "identification.filters_puce_title": "Numéro de <PERSON>ce", "identification.filters_title": "Bacs", "identification.filters_value_no": "Non", "identification.filters_value_tous": "Tous", "identification.filters_value_unknown": "Non communiqué", "identification.filters_value_yes": "O<PERSON>", "identification.list_section.title": "Liste de Levées", "identification.map.render_limit_message_body": "Affichage de {maxCount} éléments sur les {length} résultats", "identification.map.render_limit_message_title": "Limite de rendu de la carte", "identification.no_results": "Aucune donnée disponible pour cette période", "identification.synthesis_section.label_aboveweight_total": "Nb. poids en sur charge", "identification.synthesis_section.label_authorized_levees_count": "Nb. <PERSON>", "identification.synthesis_section.label_blacklisted_count": "<PERSON><PERSON><PERSON>", "identification.synthesis_section.label_collected_levees_count": "<PERSON><PERSON><PERSON><PERSON>", "identification.synthesis_section.label_high_point_levees_count": "Nb. Point haut", "identification.synthesis_section.label_identified_levees_count": "<PERSON><PERSON><PERSON>", "identification.synthesis_section.label_levees_count": "<PERSON>b<PERSON>", "identification.synthesis_section.label_non_high_point_levees_count": "Nb. non Point haut", "identification.synthesis_section.label_not_authorized_levees_count": "Nb. non Autorisé", "identification.synthesis_section.label_not_collected_levees_count": "Nb. non Collecté", "identification.synthesis_section.label_not_identified_levees_count": "Nb. non Identifié", "identification.synthesis_section.label_stoped_levees_count": "<PERSON>b<PERSON>", "identification.synthesis_section.label_total_weight": "Poids total", "identification.synthesis_section.label_underweight_total": "Nb. poids en sous charge", "identification.synthesis_section.no_circuit": "Aucun circuit sélectionné", "identification.synthesis_section.title": "Synthèse par circuit", "identification.table.column_side.arm": "Bras", "identification.table.column_side.bicomp": "Bicomp", "identification.table.column_side.combined": "Combiné", "identification.table.column_side.left": "G<PERSON><PERSON>", "identification.table.column_side.other": "<PERSON><PERSON>", "identification.table.column_side.pedestrian": "<PERSON><PERSON><PERSON>", "identification.table.column_side.right": "<PERSON><PERSON><PERSON>", "identification.table.header_text.circuit": "Résultats du véhicule {vehicleName}, circuit {circuitName} du {date}", "identification.table.header_text.date": "Résultats du {date}", "identification.table.header_text.total": "Résultats du {fromDate} au {toDate}", "identification.table.header_text.vehicle": "Résultats du véhicule {vehicleName} du {date}", "identification.table_item_popup.bans.file": "<PERSON><PERSON><PERSON>", "identification.table_item_popup.bans.file_version": "Version du", "identification.table_item_popup.bans.identifier": "Identifiant de la liste d'interdiction", "identification.table_item_popup.bans.no_results": "Pas de liste d'interdiction chargée", "identification.table_item_popup.bans.puce": "Puce en liste d'interdiction", "identification.table_item_popup.bans.reason": "<PERSON><PERSON><PERSON>", "identification.table_item_popup.bans.title": "Liste d'interdictions", "identification.table_item_popup.blacklist_history_details_section_title": "Tableau des détails de l'historique de la liste d'interdiction", "identification.table_item_popup.driver_behavior.access_blocage_lc": "Accès blocage LC", "identification.table_item_popup.driver_behavior.choix_chauffeur_blocage_lc": "Choix chauffeur blocage LC", "identification.table_item_popup.driver_behavior.title": "Comportement lève-conteneur", "identification.table_item_popup.effectiveAt": "Date d'effectivite", "identification.table_item_popup.export.export_details_button": "Exporter la levée", "identification.table_item_popup.export.last_export_date": "Date du dernier export", "identification.table_item_popup.export.last_export_name": "Nom du dernier fichier d'export", "identification.table_item_popup.export.title": "Export des levées", "identification.table_item_popup.fileName": "Nom du fichier", "identification.table_item_popup.founder_puce_number": "<PERSON><PERSON><PERSON>", "identification.table_item_popup.hide_blacklist_history_details_table": "Cacher la table", "identification.table_item_popup.last_info_section_title": "Dernières infos reçues", "identification.table_item_popup.memory_puce_number": "Num. <PERSON>", "identification.table_item_popup.no_results": "<PERSON><PERSON><PERSON> donnée disponible", "identification.table_item_popup.override.current.round": "Nouveau circuit", "identification.table_item_popup.override.current.vehicle": "Nouveau véhicule", "identification.table_item_popup.override.edited_at": "le", "identification.table_item_popup.override.edited_by": "par", "identification.table_item_popup.override.edited_title": "Modification", "identification.table_item_popup.override.initial.round": "Circuit initial", "identification.table_item_popup.override.initial.vehicle": "Véhicule initial", "identification.table_item_popup.override.title": "Nouvelle affectation", "identification.table_item_popup.reason": "<PERSON><PERSON><PERSON>", "identification.table_item_popup.start_date": "le {date} à {time}", "identification.table_item_popup.title": "<PERSON><PERSON><PERSON> de la levée", "identification.table_item_popup.view_blacklist_history_details_table": "Voir", "iframe_forbid_message": "Vous n’êtes pas autorisée à exécuter cette application via un iframe.", "latestPassedVehicles.address_marker_center_button": "<PERSON>r sur le marqueur d'adresse", "latestPassedVehicles.alerts.max_radius_exceeded": "Le rayon de recherche ne peut excéder 50 mètres (rayon sélectionné: {maxRadius} mètres)", "latestPassedVehicles.alerts.radius_not_a_number": "Le rayon de recherche doit être un entier", "latestPassedVehicles.alerts.radius_not_selected": "Veuillez sélectionner un rayon de recherche", "latestPassedVehicles.back_to_search_selection": "Retour à la sélection de véhicules et dates", "latestPassedVehicles.dateranges_max_period_exceed": "La durée maximale entre les dates ne peut excéder trois mois (durée sélectionné: {count})", "latestPassedVehicles.dates.placeholder": "Choisir une date pour la recherche", "latestPassedVehicles.details_header": "Historique du trajet de {vehicleName} au {date}", "latestPassedVehicles.labels.address": "<PERSON><PERSON><PERSON>", "latestPassedVehicles.labels.dates": "Date", "latestPassedVehicles.labels.radius": "Rayon de recherche (en mètres)", "latestPassedVehicles.labels.vehicles": "Vehicule", "latestPassedVehicles.table.go_to_vehicle_trip_history": "Voir l'itinéraire du véhicule", "latestPassedVehicles.title": "Derniers véhicules passés", "latestPassedVehicles.vehicle_filter_placeholder": "Rechercher un véhicule", "location.chrono.list_details_address": "<PERSON><PERSON><PERSON>", "location.chrono.list_details_distance": "Distance", "location.chrono.list_details_duration": "<PERSON><PERSON><PERSON>", "location.chrono.list_details_service": "Service", "location.chrono.list_details_title": "Détails", "location.chrono.synthesis_amplitude": "Amplitude", "location.chrono.synthesis_availability": "Disponibilité et travail cumulé", "location.chrono.synthesis_available_time": "Temps de disponibilité", "location.chrono.synthesis_avg_speed": "Vitesse moyenne", "location.chrono.synthesis_distance": "Distance parcourue", "location.chrono.synthesis_driving_time": "Temps de conduite", "location.chrono.synthesis_end_date": "Fin de journée", "location.chrono.synthesis_max_rest_time": "Temps de repos durée max", "location.chrono.synthesis_max_total_rest_time": "Temps de repos durée totale", "location.chrono.synthesis_service_time": "Temps de service", "location.chrono.synthesis_start_date": "<PERSON><PERSON><PERSON> de <PERSON>", "location.chrono.synthesis_start_max_duration": "Début durée max", "location.chrono.synthesis_stops_count": "Nombre d'arrêts", "location.chrono.synthesis_title": "Synthèse", "location.chrono.synthesis_work_time": "Temps de travail", "location.circuit.button_load_gps_positions": "Charger les positions", "location.circuit.button_load_trip_history": "Charger les trajets et les positions", "location.details.circuit_tab.filter.all": "Tous", "location.details.circuit_tab.filter.execution": "Exécution du circuit", "location.details.circuit_tab.filter.ignored": "Tronçons ignorés", "location.details.circuit_tab.filter.not_done": "Tronçons prévus non faits", "location.details.circuit_tab.label_action": "Action", "location.details.circuit_tab.label_distance": "Distance", "location.details.circuit_tab.label_done_perc": "<PERSON><PERSON><PERSON><PERSON>", "location.details.circuit_tab.label_duration": "<PERSON><PERSON><PERSON>", "location.details.circuit_tab.label_step_number": "Tronçon N°", "location.details.circuit_tab.table.action": "Action", "location.details.circuit_tab.table.distance": "Distance (m)", "location.details.circuit_tab.table.from_address": "<PERSON><PERSON><PERSON>", "location.details.circuit_tab.table.from_datetime": "Date/Heure début", "location.details.circuit_tab.travel_time": "<PERSON><PERSON><PERSON> du trajet", "location.details.citiid_label": "Véhicule équipé en Citi'ID", "location.details.messages_tab.edit_predefined_message_form_title": "Modifier le message prédéfini", "location.details.messages_tab.messages": "Messages", "location.details.messages_tab.no_messages": "Aucun nouveau message", "location.details.messages_tab.predefined_messages_title": "Message prédéfinis", "location.details.vehicle_tab.chorno_datetime": "Date heure", "location.details.vehicle_tab.chorno_status": "Etat chrono", "location.details.vehicle_tab.chorno_title": "Chronotachygraphe", "location.details.vehicle_tab.datetime": "Date heure", "location.details.vehicle_tab.driver_card_number": "<PERSON><PERSON> conducteur", "location.details.vehicle_tab.driver_name": "Nom", "location.details.vehicle_tab.driver_phone": "Mobile", "location.details.vehicle_tab.driver_title": "Conducteur", "location.details.vehicle_tab.last_identification_date": "Identification", "location.details.vehicle_tab.position": "Position", "location.details.vehicle_tab.sensors_title": "Retours capteurs", "location.details.vehicle_tab.speed": "Vitesse", "location.details.vehicle_tab.vehicle": "Véhicule utilisé", "location.exports.real_time.kml.tooltip": "Cliquez ici pour exporter les véhicules/chauffeurs sélectionnés au format KML", "location.history_tab.item.contact_off": "Contact OFF", "location.history_tab.item.contact_on": "Contact ON", "location.history_tab.item.to_address": "Adresse&nbsp;arrivée", "location.history_tab.item.traveled_distance": "Distance parcourue", "location.history_tab.map.linestring_tooltip_moving": "en mouvement", "location.history_tab.map.linestring_tooltip_slow": "au ralenti", "location.history_tab.map.linestring_tooltip_stop": "à l'arrêt", "location.history_tab.polyline_popup.title": "Détails de l'étape", "location.history_tab.position.address": "<PERSON><PERSON>", "location.history_tab.table.column_from_address": "Adresse&nbsp;d<PERSON><PERSON>t", "location.history_tab.table.steps_for": "Trajets pour", "location.history_tab.table.steps_for_vehicle_for_date_join_by": "du", "location.history_tab.table_map_button": "Charger les positions et le tableau", "location.identification.popup.circuit": "Circuit", "location.identification.popup.date": "Date", "location.identification.popup.details_founder_puce": "<PERSON><PERSON> fondeur", "location.identification.popup.details_is_blacklisted": "<PERSON><PERSON><PERSON>", "location.identification.popup.details_is_high_point": "Point Haut", "location.identification.popup.details_is_identified": "Identifié", "location.identification.popup.details_is_stopped": "Stoppé", "location.identification.popup.details_memory_puce": "<PERSON><PERSON> m<PERSON>", "location.identification.popup.details_title": "Identification du bac", "location.identification.popup.details_weight": "Poids", "location.identification.popup.position": "Position", "location.identification.popup.vehicle": "Véhicule", "location.main_datetime_picker_placeholder": "Choisir une date pour la recherche historique", "location.main_results.list.info_icon_tooltip": "Circuit(s)", "location.main_results.polyline_popup.title": "<PERSON><PERSON><PERSON> du trajet", "location.main_results.toolbar.autorefresh_disabled": "Rafraîchissement automatique désactivé", "location.main_results.toolbar.autorefresh_enabled": "Rafraîchissement automatique activé", "location.results.item.current_circuit_text": "{circuitName} en cours depuis {date}", "location.search_button.history": "Recherche Historique", "location.search_button.realtime": "Recherche temps réel", "location.trip_history.table.distance": "Distance (Km)", "location_module.alerts.address": "<PERSON><PERSON><PERSON>", "location_module.alerts.date": "Date", "location_module.alerts.message": "Message", "location_module.alerts.no_alert": "Aucune alerte disponible pour ce véhicule.", "location_module.alerts.time": "<PERSON><PERSON>", "location_module.alerts.type": "Type", "location_module.buttons.close": "<PERSON><PERSON><PERSON>", "location_module.buttons.list_mode": "Affichage liste", "location_module.buttons.table_and_map": "Affichage Tableau et Carto", "location_module.buttons.table_and_map_events": "Affichage Tableau et Carto", "location_module.buttons.table_mode": "Affichage tableau", "location_module.buttons.table_mode_events": "Mode tableau", "location_module.chrono.address": "<PERSON><PERSON><PERSON>", "location_module.chrono.available": "Dispo", "location_module.chrono.distance": "Distance (km)", "location_module.chrono.driver": "<PERSON><PERSON><PERSON>", "location_module.chrono.driving": "Conduite", "location_module.chrono.index": "N°", "location_module.chrono.period": "<PERSON><PERSON><PERSON>", "location_module.chrono.resting": "Repos", "location_module.chrono.service": "Cumul service", "location_module.chrono.speed": "Vitesse (km/h)", "location_module.chrono.startDate": "D<PERSON>but", "location_module.chrono.type": "Etat", "location_module.chrono.working": "Travail", "location_module.chrono.zone": "Zone", "location_module.datatable.archivedMissions": "Missions Archevées", "location_module.datatable.badge": "Badge", "location_module.datatable.cardNumber": "<PERSON><PERSON>", "location_module.datatable.circuitDriver": "Membre équipage", "location_module.datatable.circuitName": "Code circuit", "location_module.datatable.completionRate": "Taux réalisation", "location_module.datatable.current_status": "Statut&nbsp;en&nbsp;cours", "location_module.datatable.driverPlace": "Site", "location_module.datatable.drivers": "Equipage", "location_module.datatable.driversCount": "Nombre personnes", "location_module.datatable.identification": "Identification", "location_module.datatable.planifiedMissions": "Missions Planifiées", "location_module.datatable.sensors": "Remontées capteurs", "location_module.datatable.serviceDuration": "Durée Service", "location_module.datatable.startDate": "<PERSON><PERSON>", "location_module.datatable.status": "Etat", "location_module.datatable.statusLabel": "Libellé <PERSON>", "location_module.datatable.zonesclient": "Zones client", "location_module.event_filter.menu_title": "Filtrer les résultats", "location_module.events.address": "<PERSON><PERSON><PERSON>", "location_module.events.city": "Ville", "location_module.events.comment": "Commentaire", "location_module.events.date": "Date", "location_module.events.name": "Intitulé", "location_module.events.no_event": "Aucun événement disponible pour ce véhicule.", "location_module.events.ot_number": "Numéro de l'OT", "location_module.events.ot_status": "Statut de l'OT", "location_module.events.time": "<PERSON><PERSON>", "location_module.events.vehicle": "Véhicule", "location_module.identification_bacs.bac_collected": "Bac collecté", "location_module.identification_bacs.bac_uncollected": "Bac non collecté", "location_module.identification_bacs.list.puce": "<PERSON><PERSON> fondeur", "location_module.identification_bacs.table.chair_type": "<PERSON><PERSON>", "location_module.identification_bacs.table.is_blacklisted": "<PERSON><PERSON><PERSON>", "location_module.identification_bacs.table.is_highpoint": "Point haut", "location_module.identification_bacs.table.is_identified": "Identifié", "location_module.identification_bacs.table.is_stopped": "Stoppé", "location_module.identification_bacs.table.memory_puce": "<PERSON><PERSON>", "location_module.identification_bacs.table.puce": "<PERSON><PERSON>", "location_module.identification_bacs.table.weight": "Poids", "location_module.last_circuit.no_data": "Aucune circuit disponible pour ce véhicule.", "location_module.main_results.Dist.": "Dist.", "location_module.main_results.Km Réalisation": "Km Réalisation", "location_module.messages.message_length_limit": "Le message dépasse 180 caractères et il risque d'être coupé", "location_module.no_results": "Il n'y a pas de résultat disponible", "location_module.no_results.alert": "Aucune alerte disponible pour ce véhicule", "location_module.no_results.chrono": "Aucun chronotachygraphe disponible pour ce véhicule", "location_module.no_results.circuit": "Aucun circuit disponible pour ce véhicule", "location_module.no_results.events": "Aucun évènement disponible pour ce véhicule", "location_module.no_results.ident": "Aucune donnée disponible pour ce véhicule à cette période", "location_module.no_results.messages": "Aucun message disponible pour ce véhicule", "location_module.no_results.trip_history": "Aucun historique disponible pour ce véhicule", "location_module.no_results_after_filter": "Aucun résultat ne correspond aux filtres appliqués", "location_module.old_data": "Les informations sont datées de plus de 24 heures", "location_module.popup.position_marker.title": "Position reçue", "location_module.results_sorting.contact_on": "Actifs en premier", "location_module.results_sorting.datetime_asc": "Dates plus récentes en premier", "location_module.results_sorting.datetime_desc": "Dates plus anciennes en premier", "location_module.results_sorting.default": "Aucun tri", "location_module.results_sorting.title": "Trier par", "location_module.search_filters.active_circuit": "Circuit en cours", "location_module.search_filters.active_mission": "Mission en cours", "location_module.search_filters.chrono_available": "Chrono Dispo", "location_module.search_filters.chrono_driving": "Chrono Conduite", "location_module.search_filters.chrono_resting": "Chrono Repos", "location_module.search_filters.chrono_working": "Chrono Travail", "location_module.search_filters.contact_off": "Contact Off ", "location_module.search_filters.contact_on__move": "Contact mis - véhicule en mouvement", "location_module.search_filters.contact_on__slow": "Contact mis - véhicule au ralenti ", "location_module.search_filters.contact_on__stop": "Contact mis - véhicule à l’arrêt", "location_module.search_filters.none": "Aucun Fi<PERSON>re", "location_module.segment_analysis.select_second_position": "Sélectionnez la deuxième position", "location_module.segment_analysis.select_second_position_again": "Pour modifier la deuxième position, cliquez à nouveau sur une position de la carte", "location_module.segment_analysis.title": "Analyse d'un segment", "location_module.submenu.links_tooltips.alerts": "<PERSON><PERSON><PERSON>", "location_module.submenu.links_tooltips.chrono": "Chronotachygraphe", "location_module.submenu.links_tooltips.circuit": "Circuit", "location_module.submenu.links_tooltips.events": "Évènements", "location_module.submenu.links_tooltips.history": "Itinéraire", "location_module.submenu.links_tooltips.identification": "Identification Bac", "location_module.submenu.links_tooltips.messages": "Message", "location_module.submenu.links_tooltips.position_analysis": "Analyse d'un segment", "location_module.submenu.links_tooltips.vehicle": "Détails du véhicule", "location_module.submenu.vehicle.diagnostic": "Voir le diagnostic", "location_module.trip_step": "Parcours", "location_module.updated_at": "à", "login.company": "Société", "login.company.error": "Veuillez entrer la Soci&eacute;t&eacute;", "login.fail": "Identification impossible, veuil<PERSON>z v&eacute;rifier votre saisie.", "login.login_as.client_table_title": "Sélection d'un client", "login.login_as.column_last_activity_at": "Dernière activité", "login.login_as.column_last_connection_at": "Dernière connexion", "login.login_as.tooltip.as_client": "Connexion sous {clientName}", "login.login_as.tooltip.as_client_user": "Se connecter en tant que {username}", "login.login_as.tooltip.view_users_list": "Liste des utilisateurs de {clientName}", "login.login_as.users_table_title": "Liste des utilisateurs", "login.login_fail_title": "Authentification", "login.passsword_reset.modal.message_html": "Veuillez saisir vos informations.<br />Un mail contenant un lien vous sera envoy<PERSON> afin de saisir un nouveau mot de passe.", "login.password": "Mot de passe", "login.password.error": "Veuillez entrer un mot de passe valide", "login.password_reset.fail": "Une erreur est survenue.<br/>Veuillez contacter le service client.", "login.password_reset.invalid": "L'utilisateur et/ou la société n'a pas été trouvé. Veuillez contacter le service client.", "login.password_reset.link_title": "Mot de passe oublié ?", "login.password_reset.modal.close_button": "<PERSON><PERSON><PERSON>", "login.password_reset.modal.company_placeholder": "Société", "login.password_reset.modal.identifier_placeholder": "Identifiant", "login.password_reset.modal.reset_button": "Réinitialiser", "login.password_reset.question": "Mot de passe oublié ?", "login.password_reset.success": "Un lien de réinitialisation de mot de passe a été envoyé par email.", "login.placeholders.company": "Société", "login.placeholders.password": "Mot de passe", "login.placeholders.username": "Identifiant", "login.sign_in_with_google": "Se connecter avec Google", "login.username": "Identifiant", "login.username.error": "Veuillez entrer votre Identifiant", "login.validate_button": "Connexion", "map_options.location_circuit_containers": "Contenant", "map_options.trip_history_arrows": "Flèches de direction", "messages_module.crud_update_button": "Mettre à jour", "messages_module.dateranges_max_period_exceed": "La durée maximale entre les dates ne peut excéder 1 mois (durée sélectionné: {count})", "messages_module.planned_messages.edit_validation_check_date_text": "Vous ne pouvez pas modifier un message planifié qui est déjà en cours d'exécution", "messages_module.planned_messages.frequency_label": "<PERSON><PERSON><PERSON>", "messages_module.planned_messages.is_planned_message_label": "Message récurrent", "messages_module.planned_messages.remove_confirmation": "Supprimer le message récurrent ?", "messages_module.planned_messages.table_column.day": "Jours", "messages_module.planned_messages.table_column.end_date": "Date de fin", "messages_module.planned_messages.table_column.start_date": "Date de début", "messages_module.planned_messages.toggle_all_days_label": "Actif tous les jours", "messages_module.planned_messages.validate_button": "Planifier le message", "messages_module.predefined_messages.create_link": "<PERSON><PERSON><PERSON> message prédéfini", "messages_module.predefined_messages.remove_confirmation": "Supprimer le message prédéfini ?", "messages_module.predefined_messages.same_title_validation": "Il existe déjà un message prédéfini avec le même titre", "messages_module.recurrence_days_codes.friday": "V", "messages_module.recurrence_days_codes.monday": "L", "messages_module.recurrence_days_codes.saturday": "S", "messages_module.recurrence_days_codes.sunday": "D", "messages_module.recurrence_days_codes.thursday": "J", "messages_module.recurrence_days_codes.tuesday": "M", "messages_module.recurrence_days_codes.wednesday": "M", "messages_module.recurrence_frequency_codes.dayly": "Quotidienne", "messages_module.recurrence_frequency_codes.monthly": "<PERSON><PERSON><PERSON>", "messages_module.recurrence_frequency_codes.odd_week": "<PERSON><PERSON><PERSON> impaire", "messages_module.recurrence_frequency_codes.pair_week": "<PERSON><PERSON><PERSON> paire", "messages_module.recurrence_frequency_codes.weekly": "Hebdomadaire", "messages_module.search_messages.datepicker.placeholder": "Entrer une date", "messages_module.search_messages.labels.date": "Période", "messages_module.search_messages.labels.vehicles": "Vehicules", "messages_module.search_messages.validate_button": "Rechercher les messages", "messages_module.search_messages.vehicle_filter_input": "Vehicule", "messages_module.send_message.back_link": "Retour à la sélection de véhicule", "messages_module.send_message.link_label": "Envoyer un message", "messages_module.send_message.message_control.label": "Message", "messages_module.send_message.predefined_messages_select.label": "Messages prédéfinis", "messages_module.send_message.send_fail": "Échec de l'envoi du message", "messages_module.send_message.send_success": "Message envoy<PERSON> avec succès", "messages_module.send_message.validate_button": "Envoyer le message", "messages_module.sent_view.column.message": "Message", "messages_module.sent_view.column.status": "Statut", "messages_module.sent_view.header_text.each_day": "{vehiclesCount} véhicules pour: {dates}", "messages_module.sent_view.header_text.range": "{vehiclesCount} véhicules du {fromDate} au {toDate}", "messages_module.sent_view.message_read": "Message lu", "messages_module.sent_view.message_status_label_0": "Message non envoyé", "messages_module.sent_view.message_status_label_1": "Message transmis au mobile", "messages_module.sent_view.message_status_label_2": "Message envoyé, attente accusé de réception", "messages_module.sent_view.message_status_label_3": "Message transmis à mobile sans accusé de réception", "messages_module.sent_view.message_status_label_4": "En attente connexion mobile", "messages_module.sent_view.message_status_label_5": "Message non envoyé, annulation client", "messages_module.sent_view.message_status_label_6": "Message non envoyé, message ecrasé", "messages_module.sent_view.message_status_label_7": "Mobile injoignable", "messages_module.sent_view.message_status_label_8": "Message lu", "messages_module.sent_view.message_unread": "Message non lu", "messages_module.tabs.planned_messages": "Messages récurrents", "messages_module.tabs.predefined_messages": "Messages prédéfinis", "messages_module.tabs.sent_messages": "Messages envoyés", "module_names.admin": "Admin", "module_names.alerts": "<PERSON><PERSON><PERSON>", "module_names.analysis": "Analyses", "module_names.circuit": "Circuit", "module_names.citi_board": "Citi'Board", "module_names.citi_mission": "Citi'Mission", "module_names.citifret": "Citi'Fret", "module_names.citipav": "Citi'PAV", "module_names.containers": "Conteneurs", "module_names.containers_module": "Module contenant", "module_names.dashboard": "Tableau de bord", "module_names.diagnostics": "Diagnostics", "module_names.ecoconduite": "Ecoconduite", "module_names.editour": "Editour", "module_names.eventsCartography": "Cartographie des événements", "module_names.georedv2": "Geored V2", "module_names.history": "Historique", "module_names.messages": "Messagerie", "module_names.objects": "Gestion des objets", "module_names.questions": "Aide en ligne", "module_names.realtime": "Localisation", "module_names.sensors": "Identification", "module_names.zones": "Zones", "nearby_events.feature_radius_alert_message": "Le rayon doit être inférieur ou égal à 1000 mètres", "nearby_events.feature_title": "Evénements proches", "nearby_events.form_input_title": "Rayon de recherche en mètres (max 1000)", "nearby_events.table_column_address": "<PERSON><PERSON><PERSON>", "nearby_events.table_column_anomaly_name": "Intitulé", "nearby_events.table_column_distance_km": "Distance (Km)", "nearby_events.table_column_distance_m": "Distance (mètres)", "nearby_events.table_column_vehicle_name": "Véhicule", "nearby_events.table_title": "Liste d'évènements proches (rayon de {m} mètres)", "nearby_events.tooltips.hide_event": "Masquer l'événement", "nearby_events.tooltips.show_event": "Afficher l'événement", "nearby_items.time_frame.placeholder": "Afficher tous les évènements depuis...", "nearby_items.time_frame.since_24_hours": "Depuis 24 heures", "nearby_items.time_frame.since_3_days": "Depuis 3 jours", "nearby_items.time_frame.since_48_hours": "De<PERSON>is 48 heures", "nearby_items.time_frame.since_5_days": "Depuis 5 jours", "nearby_items.time_frame.since_7_days": "Depuis 7 jours", "nearby_vehicles.feature_title": "Véhicules proches", "nearby_vehicles.form_input_title": "Rayon de recherche en Km", "nearby_vehicles.form_input_title_m": "Rayon de recherche en mètres (max 1000)", "nearby_vehicles.map_menu_context_option": "Rechercher les véhicules proches", "nearby_vehicles.table_back_link_title": "Retour à la recherche d'adresse", "nearby_vehicles.table_column_distance_km": "Distance&nbsp;(Km)", "nearby_vehicles.table_column_distance_m": "Distance&nbsp;(mètres)", "nearby_vehicles.table_title": "Liste de vehicules proches (rayon de {km}Km)", "nearby_vehicles.table_title_m": "Liste de vehicules proches (rayon de {m}m)", "nearby_vehicles.tooltips.hide_vehicle": "Masquer le vehicule", "nearby_vehicles.tooltips.show_vehicle": "Aff<PERSON>r le vehicule", "nearby_vehicles_and_events.map_menu_context_option": "Rechercher les véhicules et événements proches", "operationalStatistics.buttons.clearCache": "Vider le cache local", "operationalStatistics.buttons.exportCSV": "Exporter en CSV", "operationalStatistics.buttons.poolData": "Charger les données", "operationalStatistics.column.avgSpeed": "Vitesse Moy. (km/h)", "operationalStatistics.column.circuit": "Circuit", "operationalStatistics.column.date": "Date", "operationalStatistics.column.distance": "Distance (km)", "operationalStatistics.column.duration": "<PERSON><PERSON><PERSON>", "operationalStatistics.column.endTime": "Heure Fin", "operationalStatistics.column.maxSpeed": "Vitesse Max. (km/h)", "operationalStatistics.column.startTime": "<PERSON><PERSON>", "operationalStatistics.column.vehicle": "Véhicule", "operationalStatistics.data.clearCacheButton": "Vider le cache local", "operationalStatistics.data.error": "Une erreur est survenue lors du chargement des données.", "operationalStatistics.data.exportButton": "Exporter en CSV", "operationalStatistics.data.localStorageFull": "L'espace de stockage local est plein. Veuillez vider le cache ou contacter l'administrateur.", "operationalStatistics.data.noData": "Aucune donnée à afficher. Veuillez appliquer des filtres et charger les données.", "operationalStatistics.data.partialDataError": "Certaines données n'ont pas pu être chargées. Les résultats affichés peuvent être incomplets.", "operationalStatistics.data.poolingDetails": "Détails: {details}", "operationalStatistics.data.poolingInProgress": "Chargement des données en cours... ({progress}%)", "operationalStatistics.errors.apiError": "Une erreur est survenue lors de la récupération des données", "operationalStatistics.errors.cacheClearFailed": "Échec de la suppression du cache.", "operationalStatistics.errors.exportFailed": "Échec de l'exportation.", "operationalStatistics.errors.noDataToExport": "Aucune donnée à exporter.", "operationalStatistics.exports.csvTooltip": "Exporter en CSV", "operationalStatistics.exports.excelTooltip": "Exporter en Excel", "operationalStatistics.filters.apply": "Appliquer les filtres", "operationalStatistics.filters.circuitCategories": "Catégories de circuits", "operationalStatistics.filters.circuitCategoriesPlaceholder": "Sélectionner des catégories", "operationalStatistics.filters.circuits": "Circuits", "operationalStatistics.filters.circuitsPlaceholder": "Sélectionner des circuits", "operationalStatistics.filters.dateRange": "Plage de dates", "operationalStatistics.filters.reset": "Réinitialiser les filtres", "operationalStatistics.filters.selectCircuitCategories": "Sélectionner des catégories de circuits", "operationalStatistics.filters.selectCircuits": "Sélectionner des circuits", "operationalStatistics.filters.selectDateRange": "Sélectionner une plage de dates", "operationalStatistics.filters.selectVehicles": "Sélectionner des véhicules", "operationalStatistics.filters.title": "Filtres", "operationalStatistics.filters.vehicles": "Véhicules", "operationalStatistics.filters.vehiclesPlaceholder": "Sélectionner des véhicules", "operationalStatistics.gridHeaders.activityAmplitude": "Temps de réalisation", "operationalStatistics.gridHeaders.activityAmplitudeShort": "Temps réalisation", "operationalStatistics.gridHeaders.actualDistance": "Km de collecte", "operationalStatistics.gridHeaders.actualDistanceShort": "Km collecte", "operationalStatistics.gridHeaders.anomalyCount": "Nombre d'anomalie", "operationalStatistics.gridHeaders.anomalyCountShort": "Nb anomalies", "operationalStatistics.gridHeaders.binsCollected": "Nombre de bacs collectés", "operationalStatistics.gridHeaders.binsCollectedLeft": "Nombre de bacs collectés bac gauche", "operationalStatistics.gridHeaders.binsCollectedLeftShort": "Nb bacs collectés bac gauche", "operationalStatistics.gridHeaders.binsCollectedRight": "Nombre de bacs collectés bac droit", "operationalStatistics.gridHeaders.binsCollectedRightShort": "Nb bacs collectés bac droit", "operationalStatistics.gridHeaders.binsCollectedShort": "Nb bacs collectés", "operationalStatistics.gridHeaders.circuitCategory": "Catégorie de circuit", "operationalStatistics.gridHeaders.circuitCategoryShort": "Catégorie circuit", "operationalStatistics.gridHeaders.circuitName": "Circuit", "operationalStatistics.gridHeaders.clientId": "ID Client", "operationalStatistics.gridHeaders.driverName": "<PERSON><PERSON><PERSON>", "operationalStatistics.gridHeaders.executionDate": "Date", "operationalStatistics.gridHeaders.fdrId": "ID FDR", "operationalStatistics.gridHeaders.hlpDistance": "Km de HLP", "operationalStatistics.gridHeaders.hlpDistanceShort": "Km HLP", "operationalStatistics.gridHeaders.hlpTime": "Temps de HLP", "operationalStatistics.gridHeaders.hlpTimeShort": "Temps HLP", "operationalStatistics.gridHeaders.liftNumberLeft": "Nombre de levées côté gauche", "operationalStatistics.gridHeaders.liftNumberLeftShort": "Nb levées cô<PERSON> gauche", "operationalStatistics.gridHeaders.liftNumberRight": "Nombre de levées côté droit", "operationalStatistics.gridHeaders.liftNumberRightShort": "Nb levées c<PERSON> droit", "operationalStatistics.gridHeaders.missionDistance": "Km de service", "operationalStatistics.gridHeaders.missionDistanceShort": "Km service", "operationalStatistics.gridHeaders.realizationRate": "Taux de réalisation", "operationalStatistics.gridHeaders.realizationRateShort": "Taux réalisation", "operationalStatistics.gridHeaders.roundExecutionId": "ID Exécution Tournée", "operationalStatistics.gridHeaders.roundId": "ID Tournée", "operationalStatistics.gridHeaders.roundName": "Nom de la tournée", "operationalStatistics.gridHeaders.roundNameShort": "Nom tournée", "operationalStatistics.gridHeaders.serviceTime": "Temps de service", "operationalStatistics.gridHeaders.serviceTimeShort": "Temps service", "operationalStatistics.gridHeaders.stopTime": "Temps de collecte", "operationalStatistics.gridHeaders.stopTimeShort": "Temps collecte", "operationalStatistics.gridHeaders.theoreticalDistance": "Km circuit théorique", "operationalStatistics.gridHeaders.tippingCount": "Nombre de vidage", "operationalStatistics.gridHeaders.tippingCountShort": "Nb vidages", "operationalStatistics.gridHeaders.vehicleId": "ID Véhicule", "operationalStatistics.gridHeaders.vehicleName": "Véhicule", "operationalStatistics.gridHeaders.weightCollected": "Tonnage total", "operationalStatistics.messages.cacheCleared": "Cache local vidé avec succès.", "operationalStatistics.messages.exportSuccess": "Exportation CSV réussie.", "operationalStatistics.messages.fetching": "Chargement des données en cours", "operationalStatistics.messages.noFiltersSelected": "Aucun filtre sélectionné. Veuillez sélectionner des filtres et charger les données.", "operationalStatistics.messages.poolingCancelled": "Chargement des données annulé.", "operationalStatistics.messages.poolingCancelling": "Annulation en cours...", "operationalStatistics.messages.poolingComplete": "Chargement des données terminé.", "operationalStatistics.subtitle": "Visualisation des données d'exploitation J-1", "operationalStatistics.tab.withCircuits": "Avec Circuits", "operationalStatistics.tab.withoutCircuits": "Sans Circuits (Global)", "operationalStatistics.tabs.circuitView": "Vue circuit", "operationalStatistics.tabs.disabledCircuitTabTooltip": "Veuillez sélectionner au moins un circuit pour activer l'onglet Vue circuit", "operationalStatistics.tabs.disabledOperationalTabTooltip": "Veuillez lancer la recherche pour activer l'onglet Vue opérationnelle", "operationalStatistics.tabs.operationalView": "Vue opérationnelle", "operationalStatistics.title": "Statistiques Opérationnelles", "operationalStatistics.validation.circuitsRequired": "Veuillez sélectionner au moins un circuit", "operationalStatistics.validation.dateRangeContainsToday": "La plage de dates ne peut pas inclure aujourd'hui", "operationalStatistics.validation.dateRangeExceedsOneYear": "La plage de dates ne peut pas excéder 1 an", "operationalStatistics.validation.dateRangeRequired": "Veuillez sélectionner une plage de dates", "operationalStatistics.validation.required": "Attention", "operationalStatistics.validation.vehiclesRequired": "Veuillez sélectionner au moins un véhicule", "popup.form_builder.address_geocoding": "<PERSON><PERSON><PERSON>*", "popup.form_builder.category": "<PERSON><PERSON><PERSON><PERSON>", "popup.form_builder.city": "Ville*", "popup.form_builder.country": "Pays*", "popup.form_builder.department": "Département", "popup.form_builder.latitude": "Latitude*", "popup.form_builder.longitude": "Longitude*", "popup.form_builder.postal_code": "Code postal", "popup.form_builder.predefined_view": "Nom de la nouvelle vue", "popup.form_builder.region": "Région", "popup.form_builder.required_field": "Ce champ est requis", "popup.form_builder.routing_end": "Destination", "popup.form_builder.routing_start": "<PERSON><PERSON><PERSON><PERSON>", "popup.form_builder.sections.address_locate_section_title": "Localiser une adresse", "popup.form_builder.sections.circuit_last_passages_section_title": "Liste des 5 derniers passages", "popup.form_builder.sections.circuit_legend_section_title": "Légende", "popup.form_builder.sections.circuit_list_section_title": "Liste des circuits", "popup.form_builder.sections.circuit_stretches_section_title": "Liste des tronçons", "popup.form_builder.sections.results_address_vehicle_section_title": "Adresses trouvées", "popup.form_builder.sections.vehicle_section_title": "Véhicules proches", "popup.form_builder.street_name": "Rue", "popup.form_builder.street_number": "<PERSON><PERSON><PERSON><PERSON>", "popup.form_builder.vehicle_radius": "Rayon de recherche en Km", "popup.realtime.bus_can.consumption_title": "Index consommation :", "popup.realtime.bus_can.title": "Infos Bus CAN", "popup.realtime.bus_can.vrm_title": "<PERSON><PERSON><PERSON><PERSON> moteur :", "popup.realtime.sensor.title": "Capteur", "popup.realtime.vehicle.last_position_section_title": "Dernière position reçue", "popup.realtime.vehicle.title": "Historique des positions", "popup.realtime.vehicle.vehicle_section_category": "Catégorie :", "popup.realtime.vehicle.vehicle_section_registration": "Immatriculation :", "popup.realtime.vehicle.vehicle_section_title": "Véhicule", "predefined_views.create_duplicated_name": "Il existe déjà une vue prédéfinie portant ce même nom.", "predefined_views.create_maxviews_limit": "Vous avez atteint le nombre maximal de vues prédéfinies", "predefined_views.create_title": "Nom de la nouvelle vue", "predefined_views.delete_confirmation": "Êtes-vous sûr de vouloir supprimer la vue prédéfinie '{name}' ?", "predefined_views.edit_title": "Mise à jour de la vue prédéfinie", "predefined_views.list_filter_placeholder": "<PERSON><PERSON><PERSON>", "predefined_views.list_title": "Liste des vues prédéfinies", "predefined_views.new_view_placeholder": "Nom de la vue", "realtime.actions.display_all": "<PERSON><PERSON> afficher", "realtime.circuits.detail.action": "Action", "realtime.circuits.detail.circuit_exec": "Exécution de circuit", "realtime.circuits.detail.dateheure_debut": "Date / Heure de début", "realtime.circuits.detail.depart": "<PERSON><PERSON><PERSON><PERSON>", "realtime.circuits.detail.distance": "Distance (m)", "realtime.circuits.detail.num": "<PERSON><PERSON><PERSON><PERSON>", "realtime.circuits.detail.realise": "<PERSON><PERSON><PERSON><PERSON>", "realtime.circuits.detail_info.debut": "D<PERSON>but", "realtime.circuits.detail_info.distance": "Distance", "realtime.circuits.detail_info.fin": "Fin", "realtime.circuits.detail_info.vehicule": "Véhicule", "realtime.circuits.driver_detail.adresse_arrivee": "<PERSON><PERSON><PERSON> d'a<PERSON>", "realtime.circuits.driver_detail.adresse_depart": "<PERSON><PERSON><PERSON>", "realtime.circuits.driver_detail.arret": "<PERSON><PERSON><PERSON><PERSON>", "realtime.circuits.driver_detail.arrivee": "Arrivée", "realtime.circuits.driver_detail.depart": "<PERSON><PERSON><PERSON><PERSON>", "realtime.circuits.driver_detail.distance": "Distance", "realtime.circuits.driver_detail.duree": "<PERSON><PERSON><PERSON>", "realtime.circuits.driver_detail_info.debut": "D<PERSON>but", "realtime.circuits.driver_detail_info.dist": "Dist", "realtime.circuits.driver_detail_info.fin": "Fin", "realtime.circuits.driver_detail_info.vehicule": "Véhicule", "realtime.infos.adresse": "<PERSON><PERSON><PERSON> :", "realtime.infos.categorie": "Catégorie :", "realtime.infos.contact": "Contact :", "realtime.infos.dateheure": "Date / Heure :", "realtime.infos.fonction": "Fonction :", "realtime.infos.status": "Etat :", "realtime.infos.vitesse": "Vitesse :", "realtime.strings.activation_capteurs": "Activation Capteurs", "realtime.strings.anomalie": "<PERSON><PERSON><PERSON>", "realtime.strings.circuits_theoriques_realises": "Circuits théoriques réalisés", "realtime.strings.evenement": "Evénement", "realtime.strings.identification": "Identification", "realtime.strings.position_gps": "Position GPS", "realtime.strings.sens_circulation": "Sens de circulation", "realtime.strings.traces": "Tracés", "reference_circuit.circuit_activities": "Activités", "reference_circuit.circuit_step_last_executions_not_available": "Aucun historique d'exécution de circuit trouvé pour la section de circuit sélectionnée", "reference_circuit.clear_map_button": "Vider la carte", "reference_circuit.fixed_window": "An<PERSON>r la fenêtre", "reference_circuit.messages.maximum_items_number_exceeded": " Trop d'éléments sélectionnés ({selectedNumber}/{authorizedNumber})", "reference_circuit.search.filter_placeholder": "Circuit", "reference_circuit.search.validate": "Valider", "reference_circuit.search_label": "<PERSON><PERSON><PERSON>", "reference_circuit.step_history_table.endDatetime": "Date de fin", "reference_circuit.step_history_table.startDatetime": "Date de début", "reference_circuit.step_history_table.title": "Liste de 5 derniers passages du tronçon {sectionId} du circuit {circuitName}", "reference_circuit.step_history_table.vehicleName": "Véhicule", "reference_circuit.title": "Circuit de référence", "reference_circuit.tooltips.hide_all_circuits": "Masquer les circuits", "reference_circuit.tooltips.hide_circuit": "Masquer le circuit", "reference_circuit.tooltips.show_all_circuits": "Afficher les circuits", "reference_circuit.tooltips.show_circuit": "Afficher le circuit", "reference_circuit.tooltips.show_circuit_execution_step_history": "Afficher les passages", "reference_circuit.tooltips.show_circuit_steps": "Afficher les tronçons", "reference_circuit.tree_view_bottom_information": "{number} éléments sélectionnés", "reference_circuit.tree_view_bottom_information.maximum_items_number": "(<PERSON><PERSON> {number})", "reference_circuit.unfixed_window": "Décrocher la fenêtre", "reference_circuit.version_details_table.activity": "Activité", "reference_circuit.version_details_table.address": "<PERSON><PERSON><PERSON><PERSON>", "reference_circuit.version_details_table.alert": "<PERSON><PERSON><PERSON>", "reference_circuit.version_details_table.kmCalculation": "Dist. C", "reference_circuit.version_details_table.kmTheoretical": "Dist. T", "reference_circuit.version_details_table.maneuver": "Mnvr", "reference_circuit.version_details_table.number": "<PERSON><PERSON><PERSON><PERSON>", "reference_circuit.version_details_table.title": "Liste des tronçons du circuit {circuitName}", "reference_circuit.versions_table.back_to_circuit_details_table": "Retour aux détails de la version du circuit", "reference_circuit.versions_table.back_to_circuits_table": "Retour au tableau des circuits", "reference_circuit.versions_table.back_to_search_selection": "Retour à la sélection du circuit", "reference_circuit.versions_table.category": "<PERSON><PERSON><PERSON><PERSON>", "reference_circuit.versions_table.distance": "Distance", "reference_circuit.versions_table.flux_name": "Flux", "reference_circuit.versions_table.name": "Nom", "reference_circuit.versions_table.title": "Circuits sélectionnés", "reference_circuit.versions_table.version": "Version", "release_notes.back_link": "Retourner à la liste", "release_notes.big_popup_title": "<PERSON><PERSON><PERSON> de <PERSON>uf", "release_notes.learn_more": "En savoir plus", "release_notes.mark_as_read": "Marquer comme lu", "release_notes.no_available_news": "Pas de note de version disponible", "release_notes.sidebar_link": "Notes de version", "reset_password.alert_title": "Réinitialisation du mot de passe", "reset_password.confirm_password": "Confirmer le mot de passe", "reset_password.confirm_password.error": "Le mot de passe est différent", "reset_password.fail": "Changement de mot de passe impossible, veuillez vérifier votre saisie.", "reset_password.invalid_token": "Le token d'authentification est invalide ou expiré.", "reset_password.password": "Nouveau mot de passe", "reset_password.password.error": "Veuillez entrer un mot de passe valide", "reset_password.password_tooltip": "Minimum 12 caractères, dont au moins un chiffre, une majuscule, une minuscule et un caractère spécial (%, :, $, *, #, @)", "reset_password.success": "Votre mot de passe a été mis à jour avec succès.", "reset_password.validate_button": "Modifier votre mot de passe", "searchModule.actions.auto": "Auto", "searchModule.actions.displayAll": "Voir tout", "searchModule.actions.filter": "Filtre", "searchModule.actions.manuel": "<PERSON>", "searchModule.buttons.advanced_search": "Recherche avancée", "searchModule.buttons.free_search": "Recherche libre", "searchModule.buttons.save_current_selection": "Sauvegarder la sélection actuelle", "searchModule.date_picker.selection_label": "Mode de sélection", "searchModule.date_picker.time_slots": "<PERSON>lage horaire", "searchModule.deselect_all": "<PERSON><PERSON>", "searchModule.errors.noTabSelected": "Aucun {selectedTabName} selectionné", "searchModule.errors.unavailable": "Nous sommes désolés, nous ne sommes pas en mesure de récupérer ces informations pour le moment. Veuillez réessayer ultérieurement.", "searchModule.exports.title": "Export", "searchModule.exports.typeCSV": "CSV", "searchModule.exports.typePDF": "PDF", "searchModule.exports.typeXLS": "EXCEL", "searchModule.items.acheve": "<PERSON><PERSON><PERSON>", "searchModule.items.adresse": "<PERSON><PERSON><PERSON>", "searchModule.items.badge": "Badge", "searchModule.items.carte": "<PERSON><PERSON>", "searchModule.items.categorie": "<PERSON><PERSON><PERSON><PERSON>", "searchModule.items.chauffeur": "<PERSON><PERSON><PERSON>", "searchModule.items.codeCircuit": "Code Circuit", "searchModule.items.conducteur": "Conducteur", "searchModule.items.cumul": "Cumul", "searchModule.items.dateheure": "Date & Heure", "searchModule.items.dureeService": "Durée Service", "searchModule.items.etat": "Etat", "searchModule.items.etatLibelle": "Libellé état", "searchModule.items.heureDebut": "<PERSON><PERSON>", "searchModule.items.identification": "Identification", "searchModule.items.installes": "Installés", "searchModule.items.membresEquip": "Membres équipage", "searchModule.items.nbPersonnes": "Nombre personnes", "searchModule.items.planifie": "Planifiées", "searchModule.items.remontees": "Remontées", "searchModule.items.serviceHebdo": "Service Hebdomadaire", "searchModule.items.serviceMens": "Service Mensuel", "searchModule.items.site": "Site", "searchModule.items.status": "Statut en cours", "searchModule.items.tauxReal": "Taux Réalisation", "searchModule.items.tempsReception": "<PERSON><PERSON> ré<PERSON>", "searchModule.items.vehicule": "Véhicule", "searchModule.items.vitesse": "Vitesse", "searchModule.items.vitesseMoyenne": "Vitesse <PERSON>", "searchModule.items.zones": "Zones client", "searchModule.modal.delete_template_description": "Êtes-vous sûr de vouloir supprimer la sélection '{name}' ? Une fois supprimée, celle-ci ne pourra pas être récupérée.", "searchModule.modal.delete_template_title": "Suppression de sélection", "searchModule.modal.save_template_description": "<PERSON>eu<PERSON>z renseigner le nom de la sauvegarde de sélection en cours de création", "searchModule.modal.save_template_title": "Création de sélection", "searchModule.modal.template_name_placeholder": "Nom", "searchModule.placeholder.filter": "<PERSON><PERSON><PERSON>", "searchModule.select_all": "<PERSON><PERSON>", "searchModule.strings.a": "à", "searchModule.strings.lines": "lignes", "searchModule.strings.sur": "sur", "searchModule.tabs.chrono": "Chronotachygraphe", "searchModule.tabs.circuits": "Circuits", "searchModule.tabs.drivers": "Equipage", "searchModule.tabs.location": "Localisation", "searchModule.tabs.missions": "Missions", "searchModule.tabs.rse": "RSE", "searchModule.tabs.sensors": "Capteurs", "searchModule.templates.no_selection": "Aucune sélection", "searchModule.templates.no_template_selected": "Recherche sauvegardée", "search_module.back_to_location": "Retourner sur la localisation", "search_module.date_picker.mode_each_day": "<PERSON><PERSON> jour", "search_module.date_picker.mode_range": "Période", "search_module.date_picker.time_end": "Fin", "search_module.date_picker.time_mode.all_day": "Appliquer la plage horaire chaque jour", "search_module.date_picker.time_mode.first_end_day": "Appliquer la plage horaire sur le premier et le dernier jour", "search_module.date_picker.time_start": "D<PERSON>but", "search_module.date_picker.validate": "Valider", "search_module.extra_filters.collapsed_title": "Plus de filtre", "search_module.extra_filters.not_collapsed_title": "Moins de filtre", "search_module.results_view.switch_back_to_search": "Afficher le formulaire de recherche", "search_module.search_type_title.form": "Recherche assistée", "search_module.search_type_title.free": "Recherche libre", "search_module.search_type_title.map": "Recherche carto", "search_module.search_view.last_results_button_tooltip": "Résultats de la dernière recherche", "search_module.shorcuts.current_month": "Mois en cours", "search_module.shorcuts.last_month": "<PERSON><PERSON>", "search_module.shorcuts.last_seven_days": "7 derniers jours", "search_module.shorcuts.today": "<PERSON><PERSON><PERSON><PERSON>", "search_module.shorcuts.yesterday": "<PERSON>er", "search_module.templates.create_success_text": "La sélection '{name}' a été créée avec succès", "search_module.templates.create_success_title": "Sélection créée", "search_module.templates.delete_confirm": "Êtes-vous sûr de vouloir supprimer la sélection '{name}' ?", "search_module.templates.delete_error_text": "Erreur lors de la suppression de la sélection '{name}'", "search_module.templates.delete_error_title": "<PERSON><PERSON>ur de <PERSON>", "search_module.templates.delete_success_text": "La sélection '{name}' a été supprimée avec succès", "search_module.templates.delete_success_title": "Sélection supprimée", "search_module.templates.delete_tooltip": "Supprimer la sélection", "search_module.templates.save_error_text": "Erreur lors de la sauvegarde de la sélection '{name}'", "search_module.templates.save_error_title": "<PERSON><PERSON><PERSON> <PERSON> sauve<PERSON>", "search_module.templates.update_success_text": "La sélection '{name}' a été mise à jour avec succès", "search_module.templates.update_success_title": "Sélection mise à jour", "session.token_expired_message_contents": "Votre session a expiré. Veuillez vous reconnecter", "session.token_expired_message_title": "Authentification", "settings.account.client_label": "Client", "settings.account.login_label": "Utilisa<PERSON>ur", "settings.account_section.current_session.title": "Session actuelle", "settings.cache.clear_cache.btn_text": "Effacer le cache des données", "settings.cache.clear_cache.confirm_message": "Êtes-vous sûr de vouloir effacer le cache des données ? Cela videra le cache et réinitialisera les valeurs des paramètres aux valeurs par défaut.", "settings.cache.title": "<PERSON><PERSON>", "settings.changes_saved_success": "Changements sauvegardés", "settings.display_section.map_subsection.title": "Cartographie", "settings.events.displayInformations": "Afficher les informations", "settings.events.events": "Évènements", "settings.events.searchResults": "Résultat de la recherche", "settings.location.defaultTabOpened": "Ouverture menu par défaut", "settings.location.defaultTabOpened.automatic": "Automatique", "settings.location.realtime": "<PERSON><PERSON> réel", "settings.location.realtime.autorefresh.time_interval": "Intervalle d'actualisation", "settings.location.realtime.autorefresh.title": "Actualisation automatique", "settings.map.baselayer_section.layer_names.OSM": "OSM", "settings.map.baselayer_section.layer_names.cBaseLayerAuvergne": "Auvergne", "settings.map.baselayer_section.layer_names.cBaseLayerCG2A": "CG2A", "settings.map.baselayer_section.layer_names.cBaseLayerCUB": "Bordeaux Métropole", "settings.map.baselayer_section.layer_names.cBaseLayerClientDefault": "Carte de base", "settings.map.baselayer_section.layer_names.cBaseLayerIGNBDOrtho": "IGN BDOrtho", "settings.map.baselayer_section.layer_names.cBaseLayerIGNBDParcel": "IGN BDParcel", "settings.map.baselayer_section.layer_names.cBaseLayerIGNGeoGridPlan": "IGN GeoGridPlan", "settings.map.baselayer_section.layer_names.cBaseLayerSabatier": "Simpliciti 1", "settings.map.baselayer_section.layer_names.cBaseLayerSabatier2": "Simpliciti 2", "settings.map.baselayer_section.layer_names.cBaseLayerSabatier3": "Simpliciti 3", "settings.map.cartography_subsection_title": "Fonds de carte", "settings.map.geocoding.natural_geocoding_provider.google": "Google", "settings.map.geocoding.natural_geocoding_provider.osm": "OSM", "settings.map.geocoding.natural_geocoding_provider.title": "Fournisseur de géocodage naturel", "settings.map.geocoding.title": "Géocodage", "settings.map.layers_section.title": "Couches à afficher sur la carte", "settings.map.layers_section.zone_types_select_all": "<PERSON><PERSON>", "settings.map.layers_section.zone_types_title": "Types des zones à afficher sur la carte", "settings.map.layers_section.zone_types_unselect_all": "<PERSON><PERSON>", "settings.map.layers_section.zones_checkbox_label": "Zones", "settings.map.predefined_view_default_basemap_text": "Choix du calque de base", "settings.map.predefined_view_description": "La vue prédéfinie permet d'afficher de préférence une zone de la carte", "settings.map.predefined_view_select_none_text": "(Aucune)", "settings.map.predefined_view_zoom_level_text": "Choix de niveau de zoom par défaut", "settings.sections.account": "Gestion du compte", "settings.sections.display": "Affichage", "settings.sections.location": "Localisation", "settings.zones.cartography.title": "Cartographie", "settings.zones.cartography.zoneRendering.access_point": "Point d'accès de la zone", "settings.zones.cartography.zoneRendering.geometry": "Géométrie de la zone", "settings.zones.cartography.zoneRendering.icon": "Icône + texte de la zone", "settings.zones.cartography.zoneRendering.title": "Représentation de la zone", "settings.zones.cartography.zoneRendering.value_required_constraint": "Au moins une option doit être cochée", "settings.zones.title": "Zones", "shared.address_autocomplete.no_results": "Il n'y a pas de résultats", "shared.address_autocomplete.placeholder": "Tapez pour rechercher", "sidebar.button_logout": "Se déconnecter", "sidebar.button_settings": "Préférences", "sidebar.tooltip.login_as": "Sélection d'un autre client", "sidebar.tooltip.logout": "Déconnexion", "sidebar.tooltip.settings": "Préférences utilisateur", "switchable_popups.label.identification": "Puce : ", "zones.api_errors.zone_name_already_exists": "Ce nom est déjà utilisé pour un zone de cet utilisateur", "zones.api_errors.zone_type_name_already_exists": "Ce nom est déjà utilisé pour un type de zone de cet utilisateur", "zones.buttons.back_to_zones_list": "Retourner à la liste des zones", "zones.buttons.create_zone": "Créer une zone", "zones.buttons.create_zone_type": "Créer un type de zone", "zones.buttons.save": "Valider", "zones.columns.categoryName": "<PERSON><PERSON><PERSON><PERSON>", "zones.columns.color": "<PERSON><PERSON><PERSON>", "zones.columns.formattedAddress": "<PERSON><PERSON><PERSON>", "zones.columns.geometry_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zones.columns.invalid_zone": "Coordonnées GPS erronées ", "zones.columns.invalid_zone_details": "La zone ne peut pas être affichée : lat/lng invalide ou polygone manquant", "zones.columns.isAnalysis": "Analyse", "zones.columns.isDumping": "Vidage", "zones.columns.isInternal": "Interne", "zones.columns.isShared": "Partagé", "zones.columns.typeName": "Type", "zones.draw_access_layer_reverse_geocoding_fail": "<PERSON><PERSON><PERSON> adresse trouvée", "zones.filter.isSpecificBehavior": "Droits Citi Mission", "zones.filter.isSpecificBehavior_tooltip": "Cochez la case pour n'afficher que les zones de droits Citi Mission.", "zones.icon_tooltip.disallow_edit_other_user_zone": "Vous ne pouvez pas éditer cette zone créée par un autre utilisateur", "zones.icon_tooltip.disallow_remove_other_user_zone": "Zone partagée par {userLogin}", "zones.icon_tooltip.edit_zone": "Cliquez pour éditer les paramètres de la zone", "zones.icon_tooltip.edit_zone_affix": "partagée par {userLogin}", "zones.icon_tooltip.remove_zone": "Cliquez pour supprimer cette zone", "zones.list_dropdown_filter_label": "Filtrer par", "zones.list_no_results": "Il n'y a pas de résultats", "zones.map.buttons.draw_circle": "Définir le calque (Cercle)", "zones.map.buttons.draw_polygon": "Définir le calque (Polygone)", "zones.map.buttons.edit_layers": "Éditer des calques", "zones.map.buttons.move_layers": "Déplacer des calques", "zones.map.buttons.remove_layers": "Supprimer des calques", "zones.map.buttons.rotate_layers": "Tourner des calques", "zones.map.buttons.set_access_point": "Définir point d'accès", "zones.map.buttons.set_zone_layer": "Éditer la géométrie", "zones.map.filter_area.title": "Filtrer Zone", "zones.module_restricted_message": "Vous devez vous connecter avec un compte utilisateur appartenant à {clientName}", "zones.remove.remove_zones_message": "Supprimer la zone?", "zones.remove_errors.zone_has_associated_list": "Impossible de supprimer cette zone car elle est associée à une liste de zone", "zones.remove_errors.zone_type_has_associated_list": "Impossible de supprimer ce type de zone car il est associé à une liste de zone", "zones.tabs.types": "Types", "zones.tabs.zones": "Zones", "zones.zone_geometry_type_p_label": "Polygone", "zones.zone_geometry_type_r_label": "Rayon", "zones.zone_type.remove_modal_confirmation_text": "Supprimer la zone?", "zones.zone_type.remove_modal_move_zones_text": "Type dans lequel les zones seront déplacées", "zones.zone_type.remove_modal_title": "Supprimer le type de zone", "zones.zone_type.remove_modal_zones_count_text": "Nombre de zones de type {name}: {count}", "zones.zone_type_form.icon_label": "Icone", "zones.zone_type_icons_labels.drapeau_bleu": "<PERSON><PERSON><PERSON> bleu", "zones.zone_type_icons_labels.drapeau_orange": "Drapeau orange", "zones.zone_type_icons_labels.drapeau_rouge": "Drapeau rouge", "zones.zone_type_icons_labels.drapeau_vert": "Drapeau vert", "zones.zone_type_icons_labels.icone_clientbleu": "Client bleu", "zones.zone_type_icons_labels.icone_clientrouge": "Client rouge", "zones.zone_type_icons_labels.icone_clientvert": "Client vert", "zones.zone_type_icons_labels.icone_depot": "Icone depot", "zones.zone_type_icons_labels.icone_entrepot": "Icone entrepot", "zones.zone_type_icons_labels.icone_fournisseur": "Icone fournisseur", "zones.zone_type_icons_labels.icone_garage": "Icone garage", "zones.zone_type_icons_labels.icone_interdit": "Icone interdit", "zones.zone_type_icons_labels.icone_livraison": "Icone liv<PERSON>son", "zones.zone_type_icons_labels.icone_maison": "Icone maison", "zones.zones_form.analysis_group_title": "Paramètres d'analyses", "zones.zones_form.back_link_dirty_confirmation": "Les modifications seront perdues si vous ne cliquez pas sur le bouton Valider, êtes-vous sûr ?", "zones.zones_form.isAnalysis": "Utiliser dans l'analyse", "zones.zones_form.isAnalysis_tooltip": "Faire apparaître la zone dans les rapports et les outils d'analyses", "zones.zones_form.isInternal": "Zone interne", "zones.zones_form.isInternal_isAnalysisDisabled_tooltip": "La zone n'est pas utilisée dans les analyses, vous ne pouvez pas activer l'option zone interne", "zones.zones_form.isInternal_tooltip": "Si la zone est utilisée dans les analyses, elle peut être considérée comme zone interne", "zones.zones_form.isShared": "Partager la zone", "zones.zones_form.isSharedEdition": "Autoriser les modifications", "zones.zones_form.isSharedEdition.isSharedDisabled_tooltip": "La zone n'est pas partagée, vous ne pouvez pas activer son édition par tous les utilisateurs", "zones.zones_form.isSharedEdition_tooltip": "La zone sera éditable par tous les utilisateurs du compte client", "zones.zones_form.isShared_tooltip": "La zone sera visible par tous les utilisateurs du compte client", "zones.zones_form.isSpecificBehavior": "Droits Citi Mission", "zones.zones_form.isSpecificBehavior_tooltip": "Si vous cochez la case, cette zone sera exclusivement utilisée pour les droits Citi Mission et ne sera pas visible sur la cartographie par les utilisateurs.", "zones.zones_form.share_group_title": "Partage", "operationalStatistics.gridHeadersGroups.generalInfos": "<PERSON><PERSON><PERSON>", "operationalStatistics.gridHeaders.generalInfos.0.executionStart": "DH début circuit", "operationalStatistics.gridHeaders.generalInfos.1.executionStart": "DH début", "operationalStatistics.gridHeaders.generalInfos.0.executionEnd": "DH fin circuit", "operationalStatistics.gridHeaders.generalInfos.1.executionEnd": "DH fin", "operationalStatistics.gridHeaders.generalInfos.day": "Jour", "operationalStatistics.gridHeaders.generalInfos.tooltips.0.start": "Date et heure de début de circuit", "operationalStatistics.gridHeaders.generalInfos.tooltips.1.start": "Date et heure de début de l'itineraire", "operationalStatistics.gridHeaders.generalInfos.tooltips.0.end": "Date et heure de fin de circuit", "operationalStatistics.gridHeaders.generalInfos.tooltips.1.end": "Date et heure de fin de l'itineraire", "operationalStatistics.gridHeaders.generalInfos.tooltips.day": "Jour d'exécution du circuit (premier jour si circuit sur plusieurs jours)", "operationalStatistics.gridHeadersGroups.crew": "Equipage", "operationalStatistics.gridHeaders.generalInfos.circuitName": "Circuit", "operationalStatistics.gridHeaders.generalInfos.vehicleName": "Véhicule", "operationalStatistics.gridHeaders.generalInfos.circuitFlux": "Flux circuit", "operationalStatistics.gridHeaders.crew.declaredDriver": "<PERSON><PERSON><PERSON>", "operationalStatistics.gridHeaders.crew.identifiedDriver": "<PERSON><PERSON>eur identifié", "operationalStatistics.gridHeaders.crew.plannedDriver": "<PERSON><PERSON><PERSON>", "operationalStatistics.gridHeaders.crew.ripers": "R<PERSON><PERSON><PERSON>", "operationalStatistics.gridHeaders.crew.tooltips.declaredDriver": "Nom(s) du ou des chauffeurs déclarés via l'application mobile", "operationalStatistics.gridHeaders.crew.tooltips.identifiedDriver": "Chauffeur identifié via un lecteur de badge, un terminal, un lecteur de carte ou un chronotachygraphe", "operationalStatistics.gridHeaders.crew.tooltips.plannedDriver": "Nom(s) du ou des chauffeurs planifiés", "operationalStatistics.gridHeaders.crew.tooltips.ripers": "Nom(s) du ou des ripeur déclarés via l'application mobile", "operationalStatistics.gridHeadersGroups.round": "Données circuit", "operationalStatistics.gridHeaders.round.realizationRate": "Tx réal", "operationalStatistics.gridHeaders.round.realizationRateIgnored": "Tx réal (tr #ign)", "operationalStatistics.gridHeaders.round.serviceTime": "Tps service", "operationalStatistics.gridHeaders.round.serviceKm": "Km service", "operationalStatistics.gridHeaders.round.collectionTime": "Tps collecte", "operationalStatistics.gridHeaders.round.collectionKm": "Km collecte", "operationalStatistics.gridHeaders.round.hlpTime": "Tps HLP", "operationalStatistics.gridHeaders.round.hlpKm": "Km HLP", "operationalStatistics.gridHeaders.round.reverseTime": "Tps marche arrière", "operationalStatistics.gridHeaders.round.reverseKm": "Km marche arrière", "operationalStatistics.gridHeaders.round.breakTime": "Tps pause", "operationalStatistics.gridHeaders.round.stopTime": "Tps arret", "operationalStatistics.gridHeaders.round.stopNumber": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "operationalStatistics.gridHeaders.round.tooltips.realizationRate": "Taux de réalisation avec l'option \"Afficher les tronçons ignorés\" désactivée. Lorsque cette option est désactivée, le taux de réalisation est calculé comme le pourcentage de tronçons réalisés non ignorés, par rapport à l’ensemble des tronçons du circuit non ignorés (réalisés ou non).", "operationalStatistics.gridHeaders.round.tooltips.realizationRateIgnored": "Taux de réalisation avec l'option \"Afficher les tronçons ignorés\" activée. Lorsque cette option est activée, le taux de réalisation est calculé comme le pourcentage de tronçons réalisés non ignorés, par rapport à l’ensemble des tronçons du circuit non ignorés (réalisés ou non).", "operationalStatistics.gridHeaders.round.tooltips.serviceTime": "Durée d'exécution de circuit.", "operationalStatistics.gridHeaders.round.tooltips.serviceKm": "Distance parcourue sur le circuit.", "operationalStatistics.gridHeaders.round.tooltips.collectionTime": "Temps de collecte => Différence entre la date/heure de la dernière position sur le dernier tronçon du circuit et la date/heure de la première sur le premier tronçon du circuit.", "operationalStatistics.gridHeaders.round.tooltips.collectionKm": "Km collecte => Distance effectuée entre la dernière position du dernier tronçon du circuit et la première position du 1er tronçon du circuit.", "operationalStatistics.gridHeaders.round.tooltips.hlpTime": "Temps passé en haut le pied => durée pendant laquelle le véhicule est en déplacement (conformément à la segmentation des trajets : vitesse inférieure au seuil pendant une durée supérieure au seuil) et aucun capteur en mode travail n’est activé, pendant une durée supérieure au seuil défini. Ce seuil est paramétrable dans la configuration client (niveau administrateur Sabatier) et fixé par défaut à 4 minutes.", "operationalStatistics.gridHeaders.round.tooltips.hlpKm": "Distance en haut le pied => distance pendant laquelle le véhicule est en déplacement (conformément à la segmentation des trajets : vitesse inférieure au seuil pendant une durée supérieure au seuil) et aucun capteur en mode travail n’est activé, pendant une durée supérieure au seuil défini. Ce seuil est paramétrable dans la configuration client (niveau administrateur Sabatier) et fixé par défaut à 4 minutes.", "operationalStatistics.gridHeaders.round.tooltips.reverseTime": "Durée totale en marche arrière sur le circuit.", "operationalStatistics.gridHeaders.round.tooltips.reverseKm": "Distance parcourue en marche arrière sur le circuit.", "operationalStatistics.gridHeaders.round.tooltips.breakTime": "Temps de pause sur le circuit. Lorsqu'un arrêt dure plus de X minutes (dépend du paramétrage), il est considéré comme une pause.", "operationalStatistics.gridHeaders.round.tooltips.stopTime": "Temps d'arrêt sur le circuit. Temps pendant lequel la vitesse est inférieure à un certain seuil (dépend du paramétrage), peu importe le contact (ON ou OFF).", "operationalStatistics.gridHeaders.round.tooltips.stopNumber": "Nombre d'arrêts.", "operationalStatistics.gridHeadersGroups.sensors": "<PERSON><PERSON><PERSON>", "operationalStatistics.gridHeaders.sensors.totalLiftNumber": "Nb total levées", "operationalStatistics.gridHeaders.sensors.leftLiftNumber": "Nb levées gauche", "operationalStatistics.gridHeaders.sensors.rightLiftNumber": "Nb levées droit", "operationalStatistics.gridHeaders.sensors.biCompLiftNumber": "Nb levées bi-comp", "operationalStatistics.gridHeaders.sensors.combinedLiftNumber": "Nb levées combiné", "operationalStatistics.gridHeaders.sensors.tooltips.totalLiftNumber": "Nombre total de bac levés", "operationalStatistics.gridHeaders.sensors.tooltips.leftLiftNumber": "Nombre de bacs levés à gauche", "operationalStatistics.gridHeaders.sensors.tooltips.rightLiftNumber": "Nombre de bacs levés à droite", "operationalStatistics.gridHeaders.sensors.tooltips.biCompLiftNumber": "Nombre de bacs levés en bicomp", "operationalStatistics.gridHeaders.sensors.tooltips.combinedLiftNumber": "Nombre de bacs levés en combiné", "operationalStatistics.gridHeadersGroups.emptyingWeighing": "Vidage / Tonnage", "operationalStatistics.gridHeaders.emptyingWeighing.emptyingNumber": "Nb vidages", "operationalStatistics.gridHeaders.emptyingWeighing.totalWeightDeclared": "Tonnage total - <PERSON><PERSON><PERSON><PERSON><PERSON>", "operationalStatistics.gridHeaders.emptyingWeighing.totalWeightWeighed": "Tonnage total - Pesée", "operationalStatistics.gridHeaders.emptyingWeighing.tooltips.emptyingNumber": "Nb vidages", "operationalStatistics.gridHeaders.emptyingWeighing.tooltips.totalWeightDeclared": "Tonnage total - <PERSON><PERSON><PERSON><PERSON><PERSON>", "operationalStatistics.gridHeaders.emptyingWeighing.tooltips.totalWeightWeighed": "Tonnage total - Pesée", "operationalStatistics.gridHeadersGroups.anomalies": "Anomalies", "operationalStatistics.gridHeaders.anomalies.anomalyNumber": "Nb anomalies", "operationalStatistics.gridHeaders.anomalies.tooltips.anomalyNumber": "Nombre d’anomalies déclarées pendant l’exécution du circuit", "operationalStatistics.gridHeadersGroups.route": "<PERSON><PERSON><PERSON> trajet", "operationalStatistics.gridHeaders.route.routeTime": "Tps trajet", "operationalStatistics.gridHeaders.route.routeKm": "Km trajet", "operationalStatistics.gridHeaders.route.activityTime": "Tps activité", "operationalStatistics.gridHeaders.route.activityKm": "Km activité", "operationalStatistics.gridHeaders.route.breakTime": "Tps pause", "operationalStatistics.gridHeaders.route.stopTime": "Tps arret", "operationalStatistics.gridHeaders.route.reverseTime": "Tps marche arrière", "operationalStatistics.gridHeaders.route.reverseDistance": "Km marche arrière", "operationalStatistics.gridHeaders.route.consumption": "Consommation", "operationalStatistics.gridHeaders.route.tooltips.routeTime": "Durée totale du trajet.", "operationalStatistics.gridHeaders.route.tooltips.routeKm": "Distance totale du trajet.", "operationalStatistics.gridHeaders.route.tooltips.activityTime": "Temps d'activité (i.e. au moins 1 capteur activité actif).", "operationalStatistics.gridHeaders.route.tooltips.activityKm": "Distance parcourue en activité (i.e. au moins 1 capteur activité actif).", "operationalStatistics.gridHeaders.route.tooltips.breakTime": "Tps de pause. Lorsqu'un arret dure plus de X minutes (dépend du paramétrage), il est considéré comme une pause.", "operationalStatistics.gridHeaders.route.tooltips.stopTime": "Tps d'arret. Temps pendant lequel la vitesse est inférieure à un certain seuil (dépend du paramétrage), peu importe le contact (ON ou OFF).", "operationalStatistics.gridHeaders.route.tooltips.reverseTime": "Durée totale en marche arrière.", "operationalStatistics.gridHeaders.route.tooltips.reverseDistance": "Distance parcourue en marche arrière.", "operationalStatistics.gridHeaders.route.tooltips.consumption": "Consommation du véhicule sur le trajet."}