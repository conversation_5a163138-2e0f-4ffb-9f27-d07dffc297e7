import i18n from '@/i18n'
import {
  formatDate,
  formatTime,
  formatSecondsToHHMMSS,
  formatSecondsToHHMM,
  driverNameGetter,
  tippingCountGetter,
} from '@/utils/operationalStatisticsFormatters'

export const operationalStatisticsColumns = [
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.vehicleName'),
    field: 'vehicleName',
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.driverName'),
    valueGetter: driverNameGetter,
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.roundName'),
    field: 'roundName',
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.executionDate'),
    field: 'executionStart',
    valueFormatter: (params) => formatDate(params.value, i18n.locale),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.executionStartTime'),
    field: 'executionStart',
    valueFormatter: (params) => formatTime(params.value, i18n.locale),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.executionEndTime'),
    field: 'executionEnd',
    valueFormatter: (params) => formatTime(params.value, i18n.locale),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.executionDuration'),
    field: 'duration',
    valueFormatter: (params) => formatSecondsToHHMMSS(params.value),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.serviceTime'),
    field: 'fdr.serviceTime',
    valueFormatter: (params) => formatSecondsToHHMM(params.value),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.theoreticalDistance'),
    field: 'theoricalDistance',
    valueFormatter: (params) => (params.value != null ? `${params.value} km` : ''),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.actualDistance'),
    field: 'actualDistance',
    valueFormatter: (params) => (params.value != null ? `${params.value} km` : ''),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.totalDistance'),
    field: 'distance',
    valueFormatter: (params) => (params.value != null ? `${params.value} km` : ''),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.averageSpeed'),
    field: 'averageSpeed',
    valueFormatter: (params) => (params.value != null ? `${params.value} km/h` : ''),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.fuelConsumed'),
    field: 'fuelConsumed',
    valueFormatter: (params) => (params.value != null ? `${params.value} L` : ''),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.binsCollectedLeft'),
    field: 'binCollection.leftSideNumber',
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.binsCollectedRight'),
    field: 'binCollection.rightSideNumber',
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.weightCollected'),
    field: 'binCollection.weight',
    valueFormatter: (params) => (params.value != null ? `${params.value} kg` : ''),
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.tippingCount'),
    valueGetter: tippingCountGetter,
  },
  {
    headerName: i18n.t('operationalStatistics.gridHeaders.status'),
    field: 'status',
  },
]
