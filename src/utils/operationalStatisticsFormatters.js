const formatDate = (dateString, currentLocale) => {
  if (!dateString) return '';
  try {
    return new Date(dateString).toLocaleDateString(currentLocale || 'default', {
      year: 'numeric', month: '2-digit', day: '2-digit'
    });
  } catch (e) {
    console.warn('Error formatting date:', e, 'locale:', currentLocale, 'dateString:', dateString);
    return dateString;
  }
};

const formatTime = (dateString, currentLocale) => {
  if (!dateString) return '';
  try {
    return new Date(dateString).toLocaleTimeString(currentLocale || 'default', {
      hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false
    });
  } catch (e) {
    console.warn('Error formatting time:', e, 'locale:', currentLocale, 'dateString:', dateString);
    return dateString;
  }
};

const formatSecondsToHHMMSS = (totalSeconds) => {
  if (totalSeconds == null || isNaN(totalSeconds)) return '';
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
};

const formatSecondsToHHMM = (totalSeconds) => {
  if (totalSeconds == null || isNaN(totalSeconds)) return '';
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
};

const driverNameGetter = (item) => {
  if (item && item.fdr && item.fdr.crew && Array.isArray(item.fdr.crew.members)) {
    return item.fdr.crew.members
      .map(member => (typeof member === 'object' && member.name) ? member.name : (typeof member === 'string' ? member : 'N/A'))
      .join(', ');
  }
  return '';
};

const tippingCountGetter = (item) => {
  if (item && item.fdr && Array.isArray(item.fdr.emptyings)) {
    return item.fdr.emptyings.length;
  }
  return 0;
};

const getNestedValue = (obj, path) => {
  if (!path) return obj;
  return path.split('.').reduce((acc, part) => acc && acc[part], obj);
};

const escapeCSVField = (field) => {
  if (field == null) return ''; // handle null or undefined
  let stringField = String(field);
  if (stringField.includes(',') || stringField.includes('\n') || stringField.includes('"')) {
    stringField = stringField.replace(/"/g, '""');
    return `"${stringField}"`;
  }
  return stringField;
};

export {
  formatDate,
  formatTime,
  formatSecondsToHHMMSS,
  formatSecondsToHHMM,
  driverNameGetter,
  tippingCountGetter,
  getNestedValue,
  escapeCSVField
};
