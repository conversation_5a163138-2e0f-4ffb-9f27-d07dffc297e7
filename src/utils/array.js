/**
 * Create dynamic chunks so that ids*datesLen <= 10 (customizable) (equal to 10 parallel requests)
 *
 * @param {Array} ids
 * @param {number} multiplier
 * @param {number} maxCeilValue chunks will be generated in a way that ids*multiplier is never greater than maxCeilValue
 * @returns
 */
export function chunkIds(ids, multiplier = 1, maxCeilValue = 10) {
  ids = ids.slice() //Shallow copy to prevent side-effects when shifting items.
  let idealChunkSize = Math.ceil(maxCeilValue / (multiplier || 1)) //Defaults to 1 to avoid Infinity
  let chunks = []
  let currentChunk = []
  let idsRemaining = ids.length
  while (idsRemaining > 0) {
    let chunkSize = Math.min(idealChunkSize, idsRemaining)
    for (let i = 0; i < chunkSize; i++) {
      currentChunk.push(ids.shift())
    }
    chunks.push(currentChunk)
    currentChunk = []
    idsRemaining -= chunkSize
  }
  console.debugVerbose(8, 'chunksIds', {
    chunks,
  })
  return chunks
}

export function chunkArray(array, size) {
  let chunks = []
  let i = 0
  while (i < array.length) {
    chunks.push(array.slice(i, i + size))
    i += size
  }
  console.debugVerbose(8, 'chunkArray', {
    chunks,
  })
  return chunks
}

/**
 * Merges two arrays of objects into a single array based on a unique key field.
 *
 * @param {Object[]} array1 - The first array of objects to merge.
 * @param {Object[]} array2 - The second array of objects to merge.
 * @param {string} key - The unique key field on which to merge the objects.
 * @returns {Object[]} The merged array of objects.
 */
export function mergeArraysByKey(array1, array2, key) {
  function arrayToObject(array, keyField) {
    return Object.assign(
      {},
      ...array.map((item) => ({
        [item[keyField]]: item,
      }))
    )
  }

  return Object.values({
    ...arrayToObject(array1, key),
    ...arrayToObject(array2, key),
  })
}

/**
 * Sorts an array of objects by a specified date property.
 *
 * @param {array} arr - The array of objects to sort.
 * @param {string} dateProp - The property name of the date to sort by.
 * @param {boolean} [clone=false] - If true, a clone of the original array will be sorted.
 * @returns {array} The sorted array.
 */
export function sortByDate(arr, dateProp = '', order = 'desc', clone = false) {
  if (clone) arr = [...arr] // clone the array if requested
  arr.sort((a, b) => {
    let dateA, dateB

    if (dateProp) {
      if (typeof a[dateProp] === 'number') {
        // assume timestamp
        dateA = a[dateProp]
        dateB = b[dateProp]
      } else {
        // assume string date
        dateA = new Date(a[dateProp])
        dateB = new Date(b[dateProp])
      }
    } else {
      // Handle case where the array contains Date objects directly
      dateA = a instanceof Date ? a : new Date(a)
      dateB = b instanceof Date ? b : new Date(b)
    }

    if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
      throw new Error(`Invalid date: ${dateA} or ${dateB}`)
    }
    return order === 'desc' ? dateB - dateA : dateA - dateB
  })
  return arr
}

/**
 * Flattens an array of pairs (e.g., [[a, b], [c, d]]) into a flat array [a, b, c, d]
 * @param arrayOfPairs
 * @returns {*}
 */
export function flattenArrayOfPairs(arrayOfPairs) {
  if (!Array.isArray(arrayOfPairs)) {
    console.warn(
      'flattenArrayOfPairs: Expected an array of pairs, received:',
      arrayOfPairs
    )
    return []
  }

  return arrayOfPairs
    .filter((pair) => Array.isArray(pair) && pair.length === 2)
    .flat()
}
