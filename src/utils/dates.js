/**
 * @namespace Utils
 * @category Utils
 * @module Dates*/

//@todo Remove: Use global i18n if available instead
import i18n from '@/i18n'

import moment from 'moment'
import momentTz from 'moment-timezone'
import { chunkArray } from '@/utils/array'
import searchService from '@/services/search-service'
import { APIV3RequestDatetimeFormat } from '@/config/simpliciti-apis'
//import moment from 'moment-timezone'
//moment.tz.setDefault('Europe/Paris')
window._m = moment
window._d = {
  formatDateInternal,
}

export const frenchDatePattern = 'DD/MM/YYYY'
export const internationalDatePattern = 'DD-MM-YYYY'
export const englishDatePattern = 'MM/DD/YYYY'
export const englishTimePattern = 'hh:mm A'

/**
 * Use instead: formatDatetimeWithSeconds, formatDatetime, formatDate, formatTime and formatTimeWithSeconds
 *
 * @internal
 * @TODO Add milliseconds support
 * @param {String|Moment|Date} date Any valid date as string or Moment instance
 * @param {Boolean} options.onlyTime Default to false
 * @param {Boolean} options.onlyDate Default to false
 * @param {String} options.fallbackValue Value to display if invalid date (Default to an empty string)
 * @param {String} options.locale Locale code (i.g fr) (Defaults to i18n library current locale)
 * @param {String} options.inputFormat Custom input format (i.g HH:mm:ss MM-YYYY-DD)
 * @param {Boolean} options.seconds Add seconds to time format
 * @returns
 */
export function formatDateInternal(date = null, options = {}) {
  if (typeof options !== 'object') {
    console.trace('WARN: formatDateInternal options expected to be an object')
  }

  let isMomentObject = (date || '')._isAMomentObject === true
  let lang = options.locale || i18n.locale || 'fr'
  let pattern = ''
  let timeSeparator = options.timeSeparator || ':'

  if (lang === 'fr' && options.useFrenchTimeSeparator) {
    timeSeparator = 'h'
  }

  /*
  if (options.useFrenchTimeSeparator !== undefined) {
    console.log('formatDateInternal', {
      lang,
      timeSeparator,
      options,
    })
  }*/

  switch (lang) {
    case 'en':
      pattern = `${englishDatePattern} ${englishTimePattern}`
      pattern = options.onlyDate ? `${englishDatePattern}` : pattern
      pattern = options.onlyTime ? englishTimePattern : pattern
      break
    case 'nl':
      pattern = `${internationalDatePattern} HH[${timeSeparator}]mm`
      pattern = options.onlyDate ? internationalDatePattern : pattern
      pattern = options.onlyTime ? `HH[${timeSeparator}]mm` : pattern
      break
    case 'fr':
    case 'es':
    default:
      pattern = `${frenchDatePattern} HH[${timeSeparator}]mm`
      pattern = options.onlyDate ? frenchDatePattern : pattern
      pattern = options.onlyTime ? `HH[${timeSeparator}]mm` : pattern
      if (options.seconds || options.milliseconds) {
        pattern = pattern
          .split(`HH[${timeSeparator}]mm`)
          .join(`HH[${timeSeparator}]mm[${timeSeparator}]ss`)
      }
      break
  }

  if (options.returnPattern) {
    return pattern
  }

  let isValid = isMomentObject
    ? date.isValid()
    : moment(date, options.inputFormat).isValid()
  if (isValid) {
    return isMomentObject
      ? date.format(pattern)
      : moment(date, options.inputFormat).format(pattern)
  } else {
    return options.fallbackValue || ''
  }
}

/**
 * Used by calendars?
 * @returns
 */
export function getDatePattern(options) {
  return formatDate(null, {
    ...options,
    returnPattern: true,
  })
}

/**
 * Used by datatable sorting plugin ($.fn.dataTable.moment(pattern))
 * @returns
 */
export function getDatetimePattern(options = {}) {
  return formatDateInternal(null, {
    ...options,
    returnPattern: true,
    seconds: options?.seconds === undefined ? true : options.seconds,
  })
}

/**
 * FR Example: DD/MM/YYYY HH:mm:ss
 * @param {*} date
 * @param {*} options
 * @returns
 */
export function formatDatetimeWithSeconds(date, options = {}) {
  return formatDateInternal(date, {
    ...options,
    seconds: true,
  })
}
/**
 * @param {string} date date with format YYYY-MM-DDTHH:MM:SS+TZ
 */
export function adjustDateWithTimezone(date) {
  return momentTz.parseZone(date)
}
/**
 * FR Example: DD/MM/YYYY HH:mm
 * @param {*} date
 * @param {*} options
 * @returns
 */
export function formatDatetime(date, options = {}) {
  return formatDateInternal(date, options)
}
/**
 * FR Example: DD/MM/YYYY
 * @param {*} date
 * @param {*} options
 * @returns
 */
export function formatDate(date, options) {
  return formatDateInternal(date, {
    ...options,
    onlyDate: true,
  })
}
/**
 * FR example: HH:mm
 * @param {*} date
 * @param {*} options
 * @returns
 */
export function formatTime(date, options = {}) {
  if (typeof date === 'number') {
    return formatSeconds(date).split(':').slice(0, 2).join(':')
  }
  return formatDateInternal(date, {
    ...options,
    onlyTime: true,
  })
}
/**
 * FR example: HH:mm:ss
 * @param {*} date
 * @param {*} options
 * @returns
 */
export function formatTimeWithSeconds(date, options = {}) {
  if (typeof date === 'number') {
    return formatSeconds(date)
  }
  return formatDateInternal(date, {
    ...options,
    onlyTime: true,
    seconds: true,
  })
}

/**
 * @description Format seconds to 00:00:00
 * @example
 * formatSeconds(70)
 * 00:01:10
 */
export function formatSeconds(seconds) {
  let d = moment.duration(seconds * 1000)
  return (
    (d.hours() < 10 ? '0' : '') +
    d.hours() +
    ':' +
    (d.minutes() < 10 ? '0' : '') +
    d.minutes() +
    ':' +
    (d.seconds() < 10 ? '0' : '') +
    d.seconds()
  )
}

/**
 * Convert seconds to duration format.
 *
 * @param {number} seconds - The total number of seconds.
 * @param {string} [format='[:]mm:ss'] - The format of the duration. Uses moment.js format syntax.
 * @return {string} - The formatted duration string.
 */
export function secondsToDuration(seconds, format = '[:]mm:ss') {
  return (
    Math.floor(moment.duration(seconds * 1000).asHours()) +
    moment.utc(seconds * 1000).format(format)
  )
}

/**
 * Valid date parseable with moment
 * @param {*} date
 */
export function datetimeToTimestamp(datetime, inputFormat) {
  return moment(datetime, inputFormat)._d.getTime()
}

export function isMomentToday(momentValue) {
  const adjustedDateWithTimezone = adjustDateWithTimezone(momentValue)

  if (!adjustedDateWithTimezone.isValid()) {
    return false // Return false if the date is invalid
  }

  return adjustedDateWithTimezone.isSame(momentTz(), 'day')
}

/**
 * Function to check if dates are consecutive or not
 * @param {Array} dates - array of dates in string format
 * @returns {boolean} returns true if all the dates are consecutive false otherwise.
 */
export function areDatesConsecutive(dates) {
  for (let i = 0; i < dates.length - 1; i++) {
    const currentDate = moment(dates[i])
    const nextDate = moment(dates[i + 1])

    if (!nextDate.isSame(currentDate.add(1, 'days'), 'day')) {
      return false
    }
  }

  return true
}

/**
 *
 * Splits a date range into smaller chunks based on a max size
 * @param {Moment} startDate - The start of the date range
 * @param {Moment} endDate - The end of the date range
 * @param {number} [maxSize=10] - The max number of days per chunk
 * @returns {Object[]} - An array of date range objects in chunk format
 * @example
 * import moment from 'moment';
 * dateRangeToChunks(
 * moment('2022-01-01'),
 * moment('2022-01-05'),
 * 2
 * )
 * // [{startDate: 2022-01-01, endDate: 2022-01-02}, {startDate: 2022-01-03, endDate: 2022-01-04}]
 */
export function dateRangeToChunks(startDate, endDate, maxSize = 10) {
  {
    let track = console.trackTime('dateRangeToChunks')
    track.count('params', {
      startDate,
      endDate,
      maxSize,
    })
    //if start date is 1 and end date is 4, eachDayChunks will contain 1,2,3,4
    let eachDayChunks = getConsecutiveDays(
      startDate.clone()._d,
      endDate.clone()._d
    ).map((date) => {
      return {
        startDate: moment(date).clone().hour(0).minute(0).second(0),
        endDate: moment(date).clone().hour(23).minute(59).second(59),
      }
    })

    //if maxSize is 2 and we have 1,2,3,4, will return [1,2],[3,4]
    //convert into chunks of maxSize
    let smallerChunks = chunkArray(eachDayChunks, maxSize)

    //Map back to ranges
    let r = smallerChunks.map((chunk) => {
      return {
        startDate: chunk[0].startDate,
        endDate: chunk[chunk.length - 1].endDate,
      }
    })
    track.count('r', r)
    track.stop()
    return r
  }
}

/**
 * Returns an array of consecutive days between two dates.
 *
 * @param {Date} startDate - The starting date (inclusive) of the range.
 * @param {Date} endDate - The ending date (inclusive) of the range.
 * @returns {Array<Date>} An array of consecutive date objects within the specified range.
 */
export function getConsecutiveDays(startDate, endDate) {
  const consecutiveDays = []
  let currentDate = new Date(startDate)

  while (currentDate <= endDate) {
    consecutiveDays.push(new Date(currentDate))
    currentDate.setDate(currentDate.getDate() + 1)
  }

  //Replace last item with endDate
  consecutiveDays[consecutiveDays.length - 1] = endDate

  return consecutiveDays
}

export function getFormattedDurationFromSeconds(seconds) {
  let d = moment.duration(seconds * 1000)
  return (
    Math.floor(d.asHours()) + moment.utc(seconds * 1000).format('[h]mm[m]ss[s]')
  )
}

/**
 *  Calculate the difference in days between the minimum and maximum date in an array of dates
 *  @param {Array} arrayOfJsDates of Javascript Date objects
 *  @returns {Number} Difference in days between maximum and minimum date
 */
export function getDaysDifferenceBetweenMinMaxDates(arrayOfJsDates = []) {
  if (arrayOfJsDates.length === 0) return 0

  const minDate = moment(Math.min(...arrayOfJsDates))
  const maxDate = moment(Math.max(...arrayOfJsDates))

  return Math.ceil(moment.duration(maxDate.diff(minDate)).asDays()) // Millisecond conversion to days
}

/**
 * Checks for collisions between date ranges.
 *
 * @param {Array<Array<string>>} ranges - An array of ranges, where each range is represented as [startAt, endAt].
 * @returns {boolean} - True if collisions are found, false otherwise.
 */
export function checkDateRangeCollisions(ranges) {
  for (let i = 0; i < ranges.length; i++) {
    const currentRange = ranges[i]

    for (let j = i + 1; j < ranges.length; j++) {
      const otherRange = ranges[j]

      const currentStart = moment(currentRange[0])
      const currentEnd = moment(currentRange[1])
      const otherStart = moment(otherRange[0])
      const otherEnd = moment(otherRange[1])

      console.log(
        'Checking collision between:',
        currentRange,
        'and',
        otherRange
      )
      console.log(
        'Current range:',
        currentStart.format(),
        'to',
        currentEnd.format()
      )
      console.log('Other range:', otherStart.format(), 'to', otherEnd.format())

      if (
        (currentStart.isSameOrBefore(otherEnd) &&
          currentEnd.isSameOrAfter(otherStart)) ||
        (otherStart.isSameOrBefore(currentEnd) &&
          otherEnd.isSameOrAfter(currentStart))
      ) {
        // Collision detected between currentRange and otherRange
        console.log(
          'Collision detected between:',
          currentRange,
          'and',
          otherRange
        )
        return true
      }
    }
  }

  // No collisions found
  return false
}

/**
 * Generates an array of moment objects for each day of the specified week number.
 *
 * @param {number} weekNumber - The ISO week number for which to generate dates.
 * @param {number} [year] - Year of moment()
 * @returns {Array<moment.Moment>} Array containing a moment object for each day of the given week.
 */
export function getWeekDates(weekNumber, year = null) {
  var dates = []
  var start = moment()
  if (year && !isNaN(year)) {
    start = start.year(year)
  }
  start = start.isoWeek(weekNumber).startOf('isoWeek') // <— lundi → dimanche

  for (var i = 0; i < 7; i++) {
    dates.push(start.clone())
    start.add(1, 'days')
  }

  return dates
}

/**

Checks if a date range (inner range) falls within or partially collides
with another date range (outer range)
@param {Date} outerStart - Start of outer range
@param {Date} outerEnd - End of outer range
@param {Date} innerStart - Start of inner range
@param {Date} innerEnd - End of inner range
@param {boolean} checkSides - Check for collisions on sides
@returns {boolean} True if inner range is fully/partially within outer range
*/
export function isDateRangeWithinRange(
  outerStart,
  outerEnd,
  innerStart,
  innerEnd,
  checkSides
) {
  console.log('isDateRangeWithinRange', {
    outerStart,
    outerEnd,
    innerStart,
    innerEnd,
  })
  const outerStartTimestamp = outerStart.getTime()
  const outerEndTimestamp = outerEnd.getTime()

  const innerStartTimestamp = innerStart.getTime()
  const innerEndTimestamp = innerEnd.getTime()

  // Check if start of inner range is after or equal
  // to start of outer range
  if (innerStartTimestamp < outerStartTimestamp) return false

  // Check if end of inner range is before or equal
  // to end of outer range
  if (innerEndTimestamp > outerEndTimestamp) return false

  if (
    innerStartTimestamp < outerStartTimestamp ||
    innerEndTimestamp > outerEndTimestamp
  ) {
    // Collision on start or end
    return checkSides
  }

  // Fully inside
  return true
}

export function getRandomPastDateTime(
  days = 30,
  format = 'YYYY-MM-DDTHH:mm:ss'
) {
  const currentDate = moment() // Get current date
  const pastDate = moment().subtract(days, 'days') // Get date 30 days ago

  const randomTimestamp =
    pastDate.valueOf() +
    Math.random() * (currentDate.valueOf() - pastDate.valueOf()) // Generate random timestamp within the last 30 days
  const randomDate = moment(randomTimestamp)

  // Format the date
  return randomDate.format(format)
}

/**
 * Returns an array of formatted consecutive dates
 * It takes a number of days and returns consecutive dates including today
 * For example, if days === 1, then it will return yesterday and today dates
 * @param days
 * @param currentDate
 * @returns {*}
 */
export function getPastDatesUsingConsecutiveDays(days, currentDate = moment()) {
  // Calculate the start date (subtract days from current date)
  const startDate = currentDate.clone().subtract(days, 'days').toDate()

  const endDate = currentDate.toDate()

  // Use getConsecutiveDays to get the range of dates
  const dates = getConsecutiveDays(startDate, endDate)

  // Format the dates to 'YYYY-MM-DD'
  return dates.map((date) => moment(date).format('YYYY-MM-DD'))
}

/**
 * Returns a formatted date
 * Previously located within src/services/entities/location-main-search-item.js
 * @param date
 * @param inputFormat
 * @returns {*}
 */
export function stringDatetimeToDate(
  date,
  inputFormat = 'YYYY[-]MM[-]DD HH:mm:ss'
) {
  return moment(date, inputFormat)._d
}

/**
 * Returns a formatted datetime string from a unit (days, months..) and a number
 * @param unit
 * @param number
 * @returns {string}
 */
export function getRelativeFormattedPastDate(unit = 'days', number) {
  const acceptedUnit = ['days', 'weeks', 'months', 'years']

  // Check if unit is valid
  if (!acceptedUnit.includes(unit)) {
    throw new Error(
      `Invalid unit provided. Accepted units are: ${acceptedUnit.join(', ')}`
    )
  }

  // Return the formatted past date
  return moment()
    .subtract(number, unit)
    .startOf('day')
    .format('YYYY-MM-DDTHH:mm:ss')
}

export function filterValidDates(arrayOfDates) {
  return arrayOfDates.filter((date) => moment(date).isValid())
}

/**
 * Checks if date is valid ISO 8601 format
 * @param date
 * @returns boolean
 */
export function isIsoDate(date) {
  if (typeof date !== 'string') {
    return false
  }

  return moment(date, moment.ISO_8601, true).isValid()
}

/**
 * Checks if today is included in any of the provided date ranges.
 * @param dateRanges
 * @returns {*|boolean}
 */
export function hasTodayInRanges(dateRanges) {
  if (!Array.isArray(dateRanges)) return false

  return dateRanges.some(([startDate, endDate]) => {
    if (!startDate || !endDate) {
      console.warn('missing startDate or endDate in range')

      return false
    }
    return isMomentToday(startDate) || isMomentToday(endDate)
  })
}

/**
 * Sets the hour and minute of a moment date
 * @param {moment.Moment} m - Moment date object
 * @param {string} time - Time string in 'HH:mm' or 'HH:mm:ss' format
 * @returns {moment.Moment} - New moment object with time set
 */
export function setTime(m, time) {
  const parts = time.split(':')

  if (parts.length !== 2) {
    console.warn(`[setTime] Invalid time format: "${time}" (expected "HH:mm")`)
    return m.clone()
  }

  let [hour, minute] = parts.map(Number)

  if (
    isNaN(hour) ||
    isNaN(minute) ||
    hour < 0 ||
    hour > 23 ||
    minute < 0 ||
    minute > 59
  ) {
    console.warn(`[setTime] Invalid time values: "${time}"`)
    return m.clone()
  }

  return m.clone().set('hour', hour).set('minute', minute).set('second', 0) // Reset seconds to 0 for cleanliness
}

/**
 * Sorts an array of date values in ascending order, validating each date before sorting.
 * @param {Array} dates - Array of dates (strings, moment objects, or Date objects).
 * @returns {Array} - Sorted array of valid dates.
 * @throws {Error} - Throws an error if any date in the array is invalid.
 */
export function sortDatesAscending(dates) {
  if (!Array.isArray(dates)) {
    throw new Error('Input must be an array of dates.')
  }

  // Validate each date and filter out invalid dates
  const validDates = dates.filter((date) =>
    moment(date, moment.ISO_8601, true).isValid()
  )

  if (validDates.length !== dates.length) {
    console.warn('Some invalid dates were found and ignored.')
  }

  if (validDates.length === 0) {
    throw new Error('No valid dates found.')
  }

  // Sort the valid dates in ascending order
  return validDates.sort((a, b) => (moment(a).isBefore(b) ? -1 : 1))
}

export function updateTimeIfBeforeOrAfter(
  datetimeStr,
  timeStr,
  direction = 'before'
) {
  // Validate direction
  if (direction !== 'before' && direction !== 'after') {
    console.warn(
      `Invalid direction "${direction}". Must be "before" or "after".`
    )
    return datetimeStr
  }

  // Parse and validate datetimeStr
  const original = moment(datetimeStr, 'YYYY-MM-DD HH:mm:ss', true)
  if (!original.isValid()) {
    console.warn(
      `Invalid datetime format: "${datetimeStr}". Expected "YYYY-MM-DD HH:mm:ss".`
    )
    return datetimeStr
  }

  // Parse and validate timeStr
  const timeMoment = moment(timeStr, 'HH:mm', true)
  if (!timeMoment.isValid()) {
    console.warn(`Invalid time format: "${timeStr}". Expected "HH:mm".`)
    return datetimeStr
  }

  const originalTime = original.format('HH:mm')

  const shouldUpdate =
    (direction === 'before' && timeStr < originalTime) ||
    (direction === 'after' && timeStr > originalTime)

  if (shouldUpdate) {
    const [hour, minute] = timeStr.split(':').map(Number)
    original.set({ hour, minute, second: 0 })
    return original.format('YYYY-MM-DD HH:mm:ss')
  }

  return datetimeStr
}

export function getFormattedSelectedTimeRangeStringByDate(
  date,
  searchServiceInstance = searchService
) {
  //Check that date is a valid date
  if (!isIsoDate(date)) {
    console.warn('Invalid date provided :', date)
    return ''
  }

  const timeRange = searchServiceInstance.getRangeTimeSelected(date)

  if (!timeRange || !timeRange.start || !timeRange.end) {
    console.warn('Invalid time range for date:', date)
    return ''
  }

  const startTime = formatTime(timeRange.start)
  const endTime = formatTime(timeRange.end)

  return `${startTime} - ${endTime}`
}

/**
 * Returns a moment date without milliseconds
 * @param date
 * @returns {*|Date}
 */
export function stripMillisecondsFromDate(date) {
  if (!date) throw new Error('Date input is required')

  const momentDate = moment(date)

  //Throw error if date is invalid
  if (!momentDate.isValid()) {
    throw new Error('Invalid date input')
  }

  return moment(date).milliseconds(0).toDate()
}

/**
 * Normalizes a date to midnight (00:00:00) of the same day.
 * @param date
 * @returns {Date}
 */
export function normalizeDateToMidnight(date) {
  if (!date) throw new Error('Date input is required')

  const momentDate = moment(date)

  //Throw error if date is invalid
  if (!momentDate.isValid()) {
    throw new Error('Invalid date input')
  }

  return momentDate.startOf('day').toDate()
}

/**
 * Checks if dateA is after dateB using a specific format.
 * @param dateA
 * @param dateB
 * @param format
 * @returns {boolean}
 */
export function isDateAfterDate(dateA, dateB, format = 'HH:mm') {
  if (!dateA || !dateB) {
    console.warn('Both dateA and dateB must be provided')
    return false
  }
  return moment(dateA, format).isAfter(moment(dateB, format))
}

/**
 * Splits a date range into smaller ranges by day.
 * @param startDatetimeStr
 * @param endDatetimeStr
 * @param apiRequestDatetimeFormat
 * @returns {*[]}
 */
export function splitDateRangeByDay(
  startDatetimeStr,
  endDatetimeStr,
  apiRequestDatetimeFormat = APIV3RequestDatetimeFormat
) {
  const startDatetime = moment(startDatetimeStr, apiRequestDatetimeFormat)
  const endDatetime = moment(endDatetimeStr, apiRequestDatetimeFormat)

  if (!startDatetime.isValid() || !endDatetime.isValid()) {
    console.warn('Invalid datetime string(s)')
    return []
  }

  if (!startDatetime.isBefore(endDatetime)) {
    console.warn('Start datetime must be before end datetime')
    return []
  }

  const result = []
  let currentStart = startDatetime.clone()

  while (currentStart.isBefore(endDatetime)) {
    const endOfDay = currentStart.clone().endOf('day')
    const currentEnd = moment.min(endOfDay, endDatetime)

    result.push([
      currentStart.clone().format(apiRequestDatetimeFormat),
      currentEnd.clone().format(apiRequestDatetimeFormat),
    ])

    currentStart = currentEnd.clone().add(1, 'second')
  }

  return result
}

/**
 * Check if two date ranges overlap
 * @param startRange1
 * @param endRange1
 * @param startRange2
 * @param endRange2
 * @returns {boolean}
 */
export function doRangesOverlap(
  startRange1,
  endRange1,
  startRange2,
  endRange2
) {
  const start1 = moment(startRange1)
  const end1 = moment(endRange1)
  const start2 = moment(startRange2)
  const end2 = moment(endRange2)

  if (
    !start1.isValid() ||
    !end1.isValid() ||
    !start2.isValid() ||
    !end2.isValid()
  ) {
    console.warn('Invalid date range provided')
    return false
  }

  return start1.isSameOrBefore(end2) && end1.isSameOrAfter(start2)
}
