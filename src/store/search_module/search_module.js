/**
 * @namespace Stores
 * @category Stores
 * @module search-module-store
 * */
import { isUnitTest } from '@/utils/unit'
import { throttle } from '@/utils/async'
import moment from 'moment'
import Vue from 'vue'
import flushPromises from 'flush-promises'
import { getQueryStringValue } from '@/utils/querystring'
import { normalizeCategory } from '@/services/entities/location-main-search-item'
import vehicleService from '@/services/vehicle-service'
import driverService from '@/services/driver-service'
import circuitService from '@/services/circuit-service'
import { getNestedValue } from '@/utils/object.js'
import { getEnvIntValue } from '@/services/env-service.js'
import { fetchVehicleDatesWithDataManager } from '@/services/search-service.js'
import useSearchService from '@/composables/useSearchService'
import mitt from '@/plugins/mitt.js'
import * as R from 'ramda'
import { hasTodayInRanges, isMomentToday } from '@/utils/dates'
import colors from '@/styles/colors.js'

let initializingPromise = null
const isInitializing = () => initializingPromise instanceof Promise

const stats = (window.sms = {
  initTimes: 0,
})

const { getVehicleDatesDataManagerSegregationKey } = useSearchService()

/**
 * Throttle check function that will return false if called multiple time within 1 second
 */
const canRunInitializeThrottleCheck = (() => {
  const throttleFn = throttle(() => {}, 1000)
  return function () {
    return throttleFn()
  }
})()

const shouldLog =
  (getQueryStringValue('verbose') || '').includes('1') ||
  (getQueryStringValue('verbose') || '').includes('search')

const storage = (Vue.$localStorage &&
  Vue.$localStorage.fromNamespace('search_module')) || {
  getItem() {},
  setItem() {},
}

const stateKeys = {
  vehicle: 'selectedVehiclesIds',
  driver: 'selectedDriversIds',
  circuit: 'selectedCircuitsIds',
}

/**
 * @TODO refactor/move into service/utils
 */
function pushIfMissing(state, listName, value) {
  if (!(value instanceof Array)) {
    value = [value]
  }
  for (let id of value) {
    if (state[listName].indexOf(id) === -1) {
      state[listName].push(id)
    }
  }
}

/**
 * @TODO refactor/move into service/utils
 */
function spliceIfFound(state, listName, value) {
  if (state[listName].indexOf(value) !== -1) {
    state[listName].splice(state[listName].indexOf(value), 1)
  }
}

/**
 * @TODO refactor/move into service/utils
 */
function spliceRangeIfFound(state, listName, range) {
  let index = -1
  state[listName].forEach((currRange, currIndex) => {
    if (currRange[0] == range[0] && currRange[1] == range[1]) {
      index = currIndex
    }
  })
  state[listName].splice(index, 1)
}

function newState(oldPropertiesToKeep = {}) {
  return {
    /**
     * We keep stats from last search selection to be able to show searchDaysCount after selection is cleared
     * see: searchDaysCount
     */
    lastSelectionMetadata: {
      searchDaysCount: 0,
      selectedVehiclesIdsCount: [],
      selectedDriversIdsCount: [],
      selectedCircuitsIdsCount: [],
    },
    initialized: false,
    currentInitIndex: 0,
    /**
     * timestamp updated when the user click the "Valider" button
     */
    validatedAt: 0,
    /**
     * Reference data and current selection
     */
    vehicleCategories: [],
    vehicles: [],
    selectedVehiclesIds: [],
    driverCategories: [],
    drivers: [],
    selectedDriversIds: [],
    circuitCategories: [],
    circuits: [],
    selectedCircuitsIds: [],
    selectedDateRanges: [],
    /**
     * Used for location main search (see: premier_dernier_jour request parameter)
     */
    isFirstAndLastDay: false,
    /**
     * Is data first fetch?
     * @TODO refactor/remove if unused
     */
    vehiclesFirstFetch: false,
    driversFirstFetch: false,
    circuitsFirstFetch: false,
    /**
     * A flag to know whenever to show a back arrow  to switch back to results view (Form mode)
     */
    hasResults: false,
    /**
     * The current selected tab in form mode (vehicle/driver/circuit)
     */
    activeSearchFormTabName: '',
    /**
     * Will populate with the current mode (free_search/advanced_search)
     * free_search: floating search on top of map
     * advanced_search: legacy search form (like georedv2)
     */
    mode: '',
    /**
     * Flag to show a loader icon inside the "Valider" button
     */
    isSearchInProgress: false,
    /**
     * Current component view (selection/results)
     */
    view: 'selection',
    /**
     * Inject optional old properties
     */
    ...oldPropertiesToKeep,

    /**
     * Used by LocationSidebar to disable Validate button if fetch is progressing
     */
    fetchVehicleDatesWithDataProgressing: false,

    //Used by SMFormTree to regenerate data
    searchString: '',

    // interactively disable/enable button
    // todo “false” by default, as some modules cannot check whether a form is valid or not
    validButtonDisabled: false,
    // Used by datepicker to apply timerange to each day of the daterange
    applyTimeRangeToEachDayOfDateRange: false,
    // Used by events and alerts module to override native betterSelect styles
    betterSelectStyleOverrides: {
      '--vs-border-width': '1px',
      '--vs-border-color': colors.color_border,
      minHeight: '40px',
      boxShadow: '0 1px 1px rgba(108, 117, 125, 0.5)',
    },
    selectedAlertTypes: [],
    selectedAnomaliesTypes: [],
    selectedOperationMessageTypes: [],
    selectedStatusTypes: [],
    selectedOperationalStatsCircuits: [],
    selectedOperationalStatsCircuitsCategories: [],
  }
}

export default {
  namespaced: true,
  state: newState(),
  getters: {
    searchString: (state) => state.searchString,
    //Used by SearchResults (Location module) to compute total text
    searchDaysCount: (state) => state.lastSelectionMetadata.searchDaysCount,
    activeSearchFormTabSelectedItemsCountFromLastSearch(state, getters) {
      return (
        state.lastSelectionMetadata[
          stateKeys[getters.activeSearchFormTabName || 'vehicle'] + 'Count'
        ] || 0
      )
    },
    /** */
    currentMode: (state) => state.mode,
    isCurrentTabVehicles(state, getters) {
      return getters['activeSearchFormTabName'] === 'vehicle'
    },
    isCurrentTabAnyOf(state, getters) {
      return (arr) => arr.includes(getters['activeSearchFormTabName'])
    },
    isCurrentTabCircuit(state, getters) {
      return getters['activeSearchFormTabName'] === 'circuit'
    },
    isValidButtonDisabled(state) {
      return state.validButtonDisabled
    },
    activeSearchFormTabName(state) {
      return state.activeSearchFormTabName || 'vehicle'
    },
    isInitialLoadingComplete(state) {
      return {
        vehicles: state.vehiclesFirstFetch,
        drivers: state.driversFirstFetch,
        circuits: state.circuitsFirstFetch,
      }
    },
    isValidSelection(state) {
      return (
        state.selectedVehiclesIds.length > 0 ||
        state.selectedDriversIds.length > 0 ||
        state.selectedCircuitsIds.length > 0
      )
    },
    hasResults(state) {
      return state.hasResults
    },
    getSelectedIdsByType(state) {
      return (type) => state[stateKeys[type]]
    },
    isItemSelected: (state, getters) => (type, id) =>
      getters['getSelectedIdsByType'](type).findIndex((_id) => _id == id) >= 0,
    getSelectionLength: (state) =>
      (state.selectedVehiclesIds.length +
        state.selectedDriversIds.length +
        state.selectedCircuitsIds.length) *
      (state.selectedDateRanges.length || 1),
    getSelection: (state) => ({
      selectedVehiclesIds: state.selectedVehiclesIds,
      selectedDriversIds: state.selectedDriversIds,
      selectedCircuitsIds: state.selectedCircuitsIds,
      selectedDateRanges: state.selectedDateRanges,
    }),
    /**
     * @todo Refactor/Remove if multiple selections, this is prone to errors
     * @param {*} state
     * @returns
     */
    getSelectionType(state) {
      if (state.selectedVehiclesIds.length > 0) {
        return 'vehicle'
      }
      if (state.selectedDriversIds.length > 0) {
        return 'driver'
      }
      if (state.selectedCircuitsIds.length > 0) {
        return 'circuit'
      }
    },
    getSelectionAsAString: (state) => {
      let vehicleList = state.vehicles
        .filter((v) => state.selectedVehiclesIds.indexOf(v.id) !== -1)
        .map((v) => v.name)
      if (vehicleList.length > 0) {
        vehicleList = JSON.stringify(vehicleList.map((v) => v).join(','))
      } else {
        vehicleList = ''
      }

      let driversList = state.drivers
        .filter((d) => state.selectedDriversIds.indexOf(d.id) !== -1)
        .map((d) => d.name)
      if (driversList.length > 0) {
        driversList = JSON.stringify(driversList.map((d) => d).join(','))
      } else {
        driversList = ''
      }

      let circuitsList = state.circuits
        .filter((c) => state.selectedCircuitsIds.indexOf(c.id) !== -1)
        .map((c) => c.name)

      if (circuitsList.length > 0) {
        circuitsList = JSON.stringify(circuitsList.map((d) => d).join(','))
      } else {
        circuitsList = ''
      }

      let dates = ''
      if (state.selectedDateRanges.length > 0) {
        state.selectedDateRanges.forEach((date) => {
          dates += JSON.stringify(date)
        })
      }

      let searchList = [vehicleList, driversList, circuitsList, dates]
        .map((val) => val)
        .join(',')

      return JSON.stringify(searchList)
    },
    hasSelectedDates: (state) => state.selectedDateRanges.length > 0,
    /**
     * Used to decide whenever to show a cancel button
     * @param {*} state
     * @param getters
     * @returns
     */
    hasSelection: (state, getters) =>
      state.selectedVehiclesIds.length > 0.0 ||
      state.selectedDriversIds.length > 0.0 ||
      state.selectedCircuitsIds.length > 0.0 ||
      state.selectedDateRanges.length > 0.0 ||
      state.selectedAlertTypes.length > 0.0 ||
      getters.hasEventsModuleFilters ||
      getters.hasStatsModuleFilters,
    getValidatedTimestamp: (state) => state.validatedAt,
    getVehicleCategories(state) {
      return state.vehicleCategories
    },
    getVehicles(state) {
      return state.vehicles
    },
    getVehicleById: (state) => (vehicleId) =>
      state.vehicles.find((v) => v.id == vehicleId),
    getSelectedVehicles(state) {
      return state.vehicles.filter(
        (v) => state.selectedVehiclesIds.indexOf(v.id) !== -1
      )
    },
    getDriverCategories(state) {
      return state.driverCategories
    },
    getDrivers(state) {
      return state.drivers
    },
    getSelectedDrivers(state) {
      return state.drivers.filter(
        (v) => state.selectedDriversIds.indexOf(v.id) !== -1
      )
    },
    getCircuitCategories(state) {
      return state.circuitCategories
    },
    getCircuits(state) {
      return state.circuits
    },
    getSelectedCircuits(state) {
      return state.circuits.filter(
        (v) => state.selectedCircuitsIds.indexOf(v.id) !== -1
      )
    },
    getSelectedDateRanges(state) {
      return state.selectedDateRanges
    },
    getApplyTimeRangeToEachDayOfDateRange(state) {
      return state.applyTimeRangeToEachDayOfDateRange
    },
    getBetterSelectStyleOverrides(state) {
      return state.betterSelectStyleOverrides
    },
    getSelectedAlertTypes(state) {
      return state.selectedAlertTypes
    },
    getSelectedAnomaliesTypes(state) {
      return state.selectedAnomaliesTypes
    },
    getSelectedOperationMessageTypes(state) {
      return state.selectedOperationMessageTypes
    },
    getSelectedStatusTypes(state) {
      return state.selectedStatusTypes
    },
    hasEventsModuleFilters(state) {
      return (
        state.selectedAnomaliesTypes.length > 0 ||
        state.selectedOperationMessageTypes.length > 0 ||
        state.selectedStatusTypes.length > 0
      )
    },
    hasTodayInSelectedDateRanges(state, getters) {
      return hasTodayInRanges(getters.getSelection?.selectedDateRanges)
    },
    getSelectedOperationalStatsCircuits(state) {
      return state.selectedOperationalStatsCircuits
    },
    getSelectedOperationalStatsCircuitsCategories(state) {
      return state.selectedOperationalStatsCircuitsCategories
    },
    hasStatsModuleFilters(state) {
      return (
        state.selectedOperationalStatsCircuits.length > 0 ||
        state.selectedOperationalStatsCircuitsCategories.length > 0
      )
    },
  },
  mutations: {
    setValidButtonDisabled(state, disabled) {
      state.validButtonDisabled = disabled
    },
    setIsFirstAndLastDay(state, value) {
      state.isFirstAndLastDay = value
    },
    setMode(state, mode) {
      state.mode = mode || 'advanced_search'
    },
    selectAll(state, type) {
      state[stateKeys[type]] = state[`${type}s`].map((item) => item.id)
    },
    activeSearchFormTabName(state, activeSearchFormTabName) {
      console.debugVerbose(
        5,
        'search activeSearchFormTabName',
        activeSearchFormTabName
      )
      state.activeSearchFormTabName = activeSearchFormTabName
    },
    setFirstFetch(state, { type, value }) {
      state[
        {
          vehicle: 'vehiclesFirstFetch',
          driver: 'driversFirstFetch',
          circuit: 'circuitsFirstFetch',
        }[type]
      ] = value
    },
    resetState(state) {
      Object.assign(
        state,
        newState({
          currentInitIndex: state.currentInitIndex + 1,
        })
      )
      console.debugVerbose(8, 'search resetState')
    },
    /**
     * Re-initialize state variables
     * But keep reference data and initialized flag
     * @param {*} state
     */
    resetStatePartial(state) {
      Object.assign(
        state,
        newState({
          //Force current initialize operations to ignore/cancel/abort
          currentInitIndex: state.currentInitIndex + 1,

          vehicleCategories: state.vehicleCategories,
          vehicles: state.vehicles,
          driverCategories: state.driverCategories,
          drivers: state.drivers,
          circuitCategories: state.circuitCategories,
          circuits: state.circuits,
          initialized: state.initialized,
          vehiclesFirstFetch: state.vehiclesFirstFetch,
          driversFirstFetch: state.driversFirstFetch,
          circuitsFirstFetch: state.circuitsFirstFetch,
        })
      )
      console.debugVerbose(8, 'search resetStatePartial')
    },
    setInitialized(state, value) {
      state.initialized = value
    },
    setVehicleCategories(state, value) {
      state.vehicleCategories = value
    },
    setVehicles(state, value = []) {
      //console.log('search_module store setVehicles count', value.length)
      state.vehicles = value
    },
    selectVehicle(state, id) {
      pushIfMissing(state, 'selectedVehiclesIds', id)
    },
    deselectVehicle(state, id) {
      spliceIfFound(state, 'selectedVehiclesIds', id)
    },
    setDrivers(state, value = []) {
      state.drivers = value
    },
    setDriverCategories(state, value) {
      state.driverCategories = value
    },
    selectDriver(state, id) {
      pushIfMissing(state, 'selectedDriversIds', id)
    },
    deselectDriver(state, id) {
      spliceIfFound(state, 'selectedDriversIds', id)
    },
    setCircuits(state, value = []) {
      state.circuits = value
    },
    setCircuitCategories(state, value) {
      state.circuitCategories = value
    },
    selectCircuit(state, id) {
      pushIfMissing(state, 'selectedCircuitsIds', id)
    },
    deselectCircuit(state, id) {
      spliceIfFound(state, 'selectedCircuitsIds', id)
    },
    setIsSearchInProgress(state, inProgress) {
      state.isSearchInProgress = inProgress
    },
    setDate(state, date) {
      state.selectedDateRanges = [date]
    },
    /**
     * @TODO: Consider the end time set in the date picker form.
     */
    selectDateRange(state, range) {
      if (!(range instanceof Array) || range.length !== 2) {
        throw Error('INVALID_DATE_RANGE')
      }

      range[1] = moment(range[1])._d

      //splice data ranges who collide with this range
      state.selectedDateRanges
        .filter((currRange) => {
          //case 1: currRange inside new range
          //case 2: new range inside currRange
          return (
            (moment(currRange[0]).isSameOrAfter(range[0]) &&
              moment(currRange[1]).isSameOrBefore(range[1])) ||
            (moment(range[0]).isSameOrAfter(currRange[0]) &&
              moment(range[1]).isSameOrBefore(currRange[1]))
          )
        })
        .forEach((r) => spliceRangeIfFound(state, 'selectedDateRanges', r))

      state.selectedDateRanges.push(range)

      //sort by
      state.selectedDateRanges = state.selectedDateRanges.sort((a, b) => {
        return moment(a[0]).isBefore(moment(b[0])) ? -1 : 1
      })

      console.debugVerbose(8, 'selectDateRange', state.selectedDateRanges)
    },
    deselectDateRange(state, range) {
      spliceRangeIfFound(state, 'selectedDateRanges', range)
    },
    updateLastSelectionMetadata(state) {
      state.lastSelectionMetadata.searchDaysCount =
        state.selectedDateRanges.length
      state.lastSelectionMetadata.selectedVehiclesIdsCount =
        state.selectedVehiclesIds.length
      state.lastSelectionMetadata.selectedDriversIdsCount =
        state.selectedDriversIds.length
      state.lastSelectionMetadata.selectedCircuitsIdsCount =
        state.selectedCircuitsIds.length
    },
    validateSearch(state) {
      console.debugVerbose(8, 'search store validateSearch')
      state.validatedAt = Date.now()
    },
    clearDateSelection(state) {
      state.selectedDateRanges = []
      console.debugVerbose(8, 'clearDateSelection')
    },
    clearSelection(state, type = null) {
      if (type === null) {
        state.selectedVehiclesIds = []
        state.selectedDriversIds = []
        state.selectedCircuitsIds = []
        state.selectedDateRanges = []
        //console.log('clearSelection::all')
      } else {
        state[stateKeys[type]] = []
        //console.log('clearSelection')
      }
    },
    setHasResults(state, value) {
      state.hasResults = value

      //@todo: Refactor to remove this: If setHasResult switch to results view, should we do the opposite?
      if (state.hasResults) {
        state.view = 'results'
      }

      //console.log('setHasResults', value)
    },
    toggleItems(state, { ids, type }) {
      if (type === 'date_range') {
        return
      }

      let copy = [].concat(state[stateKeys[type]])
      let id = 0
      for (let x in ids) {
        id = ids[x]
        if (copy.indexOf(id) === -1) {
          copy.push(id)
        } else {
          copy.splice(copy.indexOf(id), 1)
        }
      }
      state[stateKeys[type]] = copy
    },
    restoreStateFromCache(state, cachedState) {
      //Localforage treat Date object as string
      cachedState.selectedDateRanges = cachedState.selectedDateRanges.map(
        (range) => {
          return [new Date(range[0]), new Date(range[1])]
        }
      )
      Object.assign(state, cachedState)
      shouldLog && console.debugVerbose(8, 'restoreStateFromCache')
    },
    setSelectedAlertTypes(state, value) {
      state.selectedAlertTypes = value
    },
    setSelectedAnomaliesTypes(state, value) {
      state.selectedAnomaliesTypes = value
    },
    setSelectedOperationMessageTypes(state, value) {
      state.selectedOperationMessageTypes = value
    },
    setSelectedStatusTypes(state, value) {
      state.selectedStatusTypes = value
    },
  },
  actions: {
    resetStatePartial({ commit }) {
      commit('resetStatePartial')
    },
    /**
     * Will fetch vehicle dates data available yes/no into memory (non-vuex)
     * Can be used later on by location main search to determine which vehicles to skip (history)
     * @returns
     */
    async fetchVehicleDatesWithData(
      { state, rootGetters },
      { vehicleIds, dates }
    ) {
      console.debugVerboseScope(6, 'search', 'fetchVehicleDatesWithData', {
        vehicleIds,
        dates,
      })
      if (dates.length === 0 || vehicleIds.length === 0) {
        console.debugVerboseScope(
          6,
          'search',
          'fetchVehicleDatesWithData no dates or no vehicles -> skip'
        )
        return
      }

      const hasTodayInDates = dates.some((date) => isMomentToday(date))

      //Return if dates have today
      if (hasTodayInDates) {
        return
      }

      state.fetchVehicleDatesWithDataProgressing = true
      setTimeout(() => {
        if (state.fetchVehicleDatesWithDataProgressing) {
          state.fetchVehicleDatesWithDataProgressing = false
        }
      }, getEnvIntValue('locationFetchVehicleDataDisableValidateBtnMaxDuration', 5000))
      const res =
        await fetchVehicleDatesWithDataManager.checkVehicleDatesWithData(
          {
            vehicleIds,
            dates,
          },
          getVehicleDatesDataManagerSegregationKey(
            rootGetters['auth/toClientId'] || rootGetters['auth/clientId']
          )
        )
      state.fetchVehicleDatesWithDataProgressing = false

      console.info({
        res,
      })
    },
    /**
     * Update state flag
     */
    activeSearchFormTabName({ commit }, activeSearchFormTabName) {
      commit('activeSearchFormTabName', activeSearchFormTabName)
    },
    /**
     * Toggle selection (multiple)
     */
    toggleItems({ state, commit }, { type, ids }) {
      console.debugVerboseScope(8, 'search', 'search toggleItems', {
        type,
        ids,
      })

      if (
        state.activeSearchFormTabName != type &&
        ['vehicle', 'driver', 'circuit'].includes(type) &&
        ids.length > 0
      ) {
        commit('activeSearchFormTabName', type)
      }

      commit('toggleItems', {
        type,
        ids,
      })
    },
    resetStore({ commit, dispatch, state, rootGetters }, options = {}) {
      if (!state.initialized) {
        //throw new Error('Called when before initialization')
        console.warn('search store reset ignore (called before initialization)')
        return
      }

      if (options.hardReset) {
        commit('resetState')
      } else {
        commit('resetStatePartial')
      }
      fetchVehicleDatesWithDataManager.reset(
        getVehicleDatesDataManagerSegregationKey(rootGetters)
      )
      mitt.emit('search_module/refresh')
      console.debugVerbose(
        5,
        'search store resetStore',
        options.origin || '(no-origin)'
      )
    },
    /**
     * Mock the old initialize action called from multiple places.
     * @todo Remove references and remove this action
     * @param {*} param0
     */
    async initialize({ state }) {
      if (!state.initialized) {
        console.warn('search initialize - initialized flag is not true', {
          initializing: isInitializing(),
        })
      }
    },
    /**
     * Update: Too complex. Call this once at login success and rename to lazySync
     *
     * Checks:
     * Throttle check to debounce execution if called multiple times within 1s
     * Flush promises at start so that any pending promise is flush/finish
     * Also flush any initialize operation using initializingPromise
     * Skip if already initialized (module switch with reset the initialized flag) (Less requests)
     * Ignore/Abort previous initialize operation if new fn is queued (Gperche) (Might happen if user switch to other module while an initialization is in progress)
     *
     *
     * Called once from SearchModule component (self-responsability)
     * Called from location_module store (Location module requires search module data)
     * Called from LocationModule each time layout updates (to set mode)
     * @param {boolean|undefined} options.ignorePreviousRequests //Default to true
     * @param {'free_search'|'advanced_search'} options.mode //Default to advanced_search (Location module only)
     * @returns {boolean} Success/Fail
     */
    async lazySync({ dispatch, commit, state }, options = {}) {
      if (isUnitTest()) {
        console.debugVerbose(8, 'search_module::initialize-skip (unit-test)')
        return
      }

      if (!canRunInitializeThrottleCheck()) {
        return false
      }

      // Flush promises (existing initializing operations)
      await flushPromises()

      // Wait existing initializing operations
      if (initializingPromise) {
        console.debugVerbose(8, 'search_module::initialize::wait-existing')
        await initializingPromise
        initializingPromise = null
      }

      // Reset if search is in progress in a previous module
      commit('setIsSearchInProgress', false)

      // New initialize operation
      let currentInitIndex = undefined

      const ignorePrevRequests = options.ignorePreviousRequests ?? true //Ignore prev requests by default.

      if (ignorePrevRequests) {
        state.currentInitIndex++
        currentInitIndex = state.currentInitIndex
      }
      /*
      console.log('search_module::initialize', {
        currentInitIndex,
        ignorePrevRequests,
      })*/

      if (options.hardReset) {
        state.vehicles = []
        state.vehicleCategories = []
        state.drivers = []
        state.driverCategories = []
        state.circuits = []
        state.circuitCategories = []
        state.initialized = false
      }

      // Skip if already initialized
      if (state.initialized) {
        /*console.log('search_module::initialize:skip-already-initialized', {
          currentInitIndex,
          ignorePrevRequests,
        })*/
        return false
      }

      const promise = new Promise(async (resolve, reject) => {
        // Try to load state from cache
        //let cachedState = null

        // TODO: Temporally disabled (Load last search selection after full refresh)
        //
        // try{
        //    cachedState = (await storage.getItem(`state_${rootGetters["auth/loginNameClientIdEncoded"]}`))||null
        // } catch(err) {
        //   Vue.$log.warn('cachedState',err.stack)
        // }

        //if (!cachedState) {
        //commit('resetStatePartial')

        if (shouldLog) {
          console.debugVerbose(8, 'search_module', 'initializing')
        }
        ////} else {
        // if (shouldLog) {
        //  console.debugVerbose(8,'search_module', 'initializing from cache')
        //}

        //commit('restoreStateFromCache', cachedState)
        //commit('updateLastSelectionMetadata')
        //}

        await dispatch('fetchVehicles', {
          initIndex: currentInitIndex,
          ignorePrevRequests,
        })

        if (ignorePrevRequests && currentInitIndex !== state.currentInitIndex) {
          return resolve({ error: true, step: 'vehicle fetch' })
        }

        await dispatch('fetchDrivers', {
          initIndex: currentInitIndex,
          ignorePrevRequests,
        })

        if (ignorePrevRequests && currentInitIndex !== state.currentInitIndex) {
          return resolve({ error: true, step: 'drivers fetch' })
        }

        await dispatch('fetchCircuits', {
          initIndex: currentInitIndex,
          ignorePrevRequests,
        })

        if (ignorePrevRequests && currentInitIndex !== state.currentInitIndex) {
          return resolve({ error: true, step: 'circuits fetch' })
        }

        resolve()
      })

      const afterInitializeOperations = async function () {
        let response = await promise

        if (response?.error) {
          console.warn(
            `search_module::initialize::abort -> Promise result ignored (step: ${response.step})`,
            {
              currentInitIndex,
              responseError: response?.error,
            }
          )
          return false
        }

        console.debugVerbose(8, 'search_module::initialize::success', {
          currentInitIndex,
        })
        if (options.mode) {
          commit('setMode', options.mode)
        }

        commit('setInitialized', true)
        stats.initTimes++
        return true
      }

      initializingPromise = afterInitializeOperations()
      let resPromise = new Promise(async (resolve, reject) => {
        resolve(await initializingPromise)
        initializingPromise = null
      })
      return resPromise
    },
    /**
     * Set state flag
     */
    setHasResults({ commit }, value) {
      commit('setHasResults', value)
    },
    /**
     * Set state flag
     */
    setIsFirstAndLastDay({ commit }, value) {
      commit('setIsFirstAndLastDay', value)
    },
    /**
     * Same as reset store?
     * @TODO refactor/remove if unused
     */
    clearSelection({ commit }, type) {
      commit('clearSelection', type)
    },
    clearDateSelection({ commit }) {
      commit('clearDateSelection')
    },
    /**
     * Triggered when the user click "Valider" or Search action button
     * - Updates timestamp in state
     */
    validateSearch({ commit, state, rootGetters }) {
      commit('updateLastSelectionMetadata')
      commit('validateSearch')

      //Save current selection in local cache
      storage.setItem(
        `state_${rootGetters['auth/loginNameClientIdEncoded']}`,
        R.omit(
          [
            'drivers',
            'driverCategories',
            'vehicles',
            'vehicleCategories',
            'circuits',
            'circuitCategories',
          ],
          JSON.parse(JSON.stringify(state))
        )
      )
    },
    /**
     * Select single item (vehicle/driver/circuit)
     */
    selectItem({ commit, state }, { value, type, ...otherParams }) {
      console.debugVerboseScope(5, 'search', 'search store selectItem', {
        type,
        value,
        otherParams,
      })
      const mutations = {
        vehicle: 'selectVehicle',
        driver: 'selectDriver',
        circuit: 'selectCircuit',
        date_range: 'selectDateRange',
      }

      //SearchBar: Switch the form tab depending the selected item
      if (
        state.activeSearchFormTabName != type &&
        ['vehicle', 'driver', 'circuit'].includes(type)
      ) {
        commit('activeSearchFormTabName', type)
      }

      commit(mutations[type], value)
      console.debugVerbose(8, 'selectItem', {
        mutationName: mutations[type],
        value,
      })
    },
    deselectItem({ commit }, { value, type }) {
      const mutations = {
        vehicle: 'deselectVehicle',
        driver: 'deselectDriver',
        circuit: 'deselectCircuit',
        date_range: 'deselectDateRange',
      }
      commit(mutations[type], value)
    },
    /**
     * Internal
     * @private
     * @param {*} param0
     * @param {*} param1 {initINdex, ignorePrevRequests}
     * @returns
     */
    async fetchVehicles({ commit, state }, { initIndex, ignorePrevRequests }) {
      const cl = (str) =>
        console.debugVerboseScope(8, 'search_module::fetchVehicles::' + str, {
          initIndex,
          ignorePrevRequests,
        })
      cl('start')

      if (ignorePrevRequests && initIndex !== state.currentInitIndex) {
        cl('skip1')
        return
      }

      commit(
        'setVehicleCategories',
        await vehicleService.fetchVehicleCategories(normalizeCategory)
      )

      if (ignorePrevRequests && initIndex !== state.currentInitIndex) {
        cl('skip2')
        return
      }

      let items = []
      commit('setVehicles', items)
      items = await vehicleService.fetchVehicles(
        vehicleService.normalizeVehicle,
        {
          callback(partialData = []) {
            items = [...items, ...partialData]
            commit('setVehicles', items)
            commit('setFirstFetch', { type: 'vehicle', value: true })
          },
        }
      )

      if (ignorePrevRequests && initIndex !== state.currentInitIndex) {
        cl('skip3')
        return
      }

      let ids = items.map((i) => i.id)
      items = items.filter((i, ii) => ids.indexOf(i.id) == ii)

      commit('setVehicles', items)
      commit('setFirstFetch', { type: 'vehicle', value: true })
    },
    /**
     * Internal
     * @private
     * @param {*} param0
     * @param {*} param1
     * @returns
     */
    async fetchDrivers({ commit, state }, { initIndex, ignorePrevRequests }) {
      const cl = (str) =>
        console.debugVerboseScope(8, 'search_module::fetchDrivers::' + str, {
          initIndex,
          ignorePrevRequests,
        })

      cl('start')

      if (ignorePrevRequests && initIndex !== state.currentInitIndex) {
        cl('skip1')
        return
      }

      commit(
        'setDriverCategories',
        await driverService.fetchDriverCategories(normalizeCategory)
      )

      let response = await driverService.fetchDrivers()

      if (ignorePrevRequests && initIndex !== state.currentInitIndex) {
        cl('skip2')
        return
      }

      commit(
        'setDrivers',
        response &&
          response.map((i) => {
            let categoryId = null
            //No category = null (Bug?)
            if (!i._links.category.href) {
              //tries to set "sans category" category
              categoryId = (
                state.driverCategories.find(
                  (c) => c.name.toLowerCase().indexOf('sans ') !== -1
                ) || {}
              ).id
            } else {
              categoryId = i._links.category.href.match(/\d+/)[0]
            }

            let result = Object.freeze({
              id: i.id,
              name: i.name,
              categoryId: categoryId,
              //Bug: Sometimes, category name fail to compute
              categoryName:
                state.driverCategories.find((dc) => dc.id == categoryId)
                  ?.name || categoryId,
              //original: i
            })

            return result
          })
      )
      commit('setFirstFetch', {
        type: 'driver',
        value: true,
      })
    },
    /**
     * Internal
     * @private
     * @param {*} param0
     * @param {*} param1
     * @returns
     */
    async fetchCircuits({ commit, state }, { initIndex, ignorePrevRequests }) {
      const cl = (str) =>
        console.debugVerboseScope(8, 'search_module::fetchCircuits::' + str, {
          initIndex,
          ignorePrevRequests,
        })
      cl('start')

      if (ignorePrevRequests && initIndex !== state.currentInitIndex) {
        cl('skip1')
        return
      }

      commit(
        'setCircuitCategories',
        await circuitService.fetchCircuitCategories((c) => {
          return normalizeCategory(c)
        })
      )

      let response = await circuitService.fetchCircuits()

      if (ignorePrevRequests && initIndex !== state.currentInitIndex) {
        cl('skip2')
        return
      }

      let circuits = []
      if (response) {
        response.forEach((i) => {
          if (i.archive == false) {
            let categoryId = null
            if (!i._links?.category?.href) {
              categoryId = (
                state.circuitCategories.find(
                  (c) => c.name.toLowerCase().indexOf('sans ') !== -1
                ) || {}
              ).id
            } else {
              categoryId = i._links.category.href.match(/\d+/)[0]
            }

            circuits.push(
              Object.freeze({
                id: getNestedValue(i, ['id', '_links.self.href'], null, {
                  transform(id, originalValue, rawItem) {
                    if ((id || '').toString().includes('/')) {
                      console.warn(
                        `Attribute id (Circuit Id) wasn't found in the APIV3 response (Circuits). Computed from _links.self.href (Intermediate solution)`
                      )
                      id = id.split('/').pop()
                    }

                    if (!id) {
                      console.error(
                        'No id found (Circuits) in APIV3 response',
                        {
                          rawItem,
                        }
                      )
                    }

                    return id
                  },
                }),
                name: i.shortName,
                categoryId: categoryId,
                categoryName: i.categoryName,
              })
            )
          }
        })
      }
      commit('setCircuits', circuits)
      commit('setFirstFetch', {
        type: 'circuit',
        value: true,
      })
    },
  },
}
