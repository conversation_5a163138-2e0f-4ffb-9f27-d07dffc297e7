/**
 * @namespace Stores
 * @category Stores
 * @module identification-store
 */
import { sortByDate } from '@/utils/array'
import { getEnvIntValue } from '@/services/env-service'
import { fetchLeveesDetails } from '@/services/identification-service'
import { removeFirstResolved } from '@/utils/promise'
import { getIdentificationSearchSelectionLimit } from '@/services/search-service'
import moment from 'moment'
import Vue from 'vue'
import { fetchLeveesSynthesis } from '@/services/identification-service'

const createState = () => ({
  isFetchDetailsInProgress: false,
  identificationBacSelected: null,
  /**
   * Details dataset
   */
  detailsGroupedByCircuit: [],
  /**
   * Segregation mode
   */
  detailedItemsBy: 'total',
  /**
   * Segregation mode state (selection)
   */
  detailedItemsByDateValue: null,
  detailedItemsByVehicleIdValue: null,
  detailedItemsByCircuitIdValue: null,
  fetchAborted: false,
  showTableProgressiveLoader: false,
  finalResults: [],
  items: [
    /**{
     date,
     dateFormatted
     vehicleId,
     vehicleName: getNestedValue(item, "vehicule_nom"),
     vehicleImmatriculation: getNestedValue(
     item,
     "vehicule_immatriculation"
     ),
     vehicleCategory: getNestedValue(item, "categorie_nom"),
     //Synthesis by circuit
     circuits,
     } */
  ],
  puceFilterValue: '',
  identifiedFilterValue: '',
  stoppedFilterValue: '',
  highPointFilterValue: '',
  blacklistFilterValue: '',
  memoryChipNumber: '',
  searchModuleView: '',
  dateFromFormatted: '',
  dateToFormatted: '',
  showListSpinner: '',
  mode: 'list',
  menuCollapsed: false,
})

export default {
  namespaced: true,
  state: createState(),
  getters: {
    isFetchDetailsInProgress: (state) => state.isFetchDetailsInProgress,
    identificationBacSelected: (state) => state.identificationBacSelected,
    detailsGroupedByCircuit: (state) => state.detailsGroupedByCircuit,
    detailedItemsBy: (state) => state.detailedItemsBy,
    detailedItemsByDateValue: (state) => state.detailedItemsByDateValue,
    detailedItemsByVehicleIdValue: (state) =>
      state.detailedItemsByVehicleIdValue,
    detailedItemsByCircuitIdValue: (state) =>
      state.detailedItemsByCircuitIdValue,
    puceFilterValue: (state) => state.puceFilterValue,
    identifiedFilterValue: (state) => state.identifiedFilterValue,
    stoppedFilterValue: (state) => state.stoppedFilterValue,
    highPointFilterValue: (state) => state.highPointFilterValue,
    blacklistFilterValue: (state) => state.blacklistFilterValue,
    memoryChipNumber: (state) => state.memoryChipNumber,
    searchModuleView: (state) => state.searchModuleView,
    showTableProgressiveLoader: (state) => state.showTableProgressiveLoader,
    showListSpinner: (state) => state.showListSpinner,
    mode: (state) => state.mode,
    menuCollapsed: (state) => state.menuCollapsed,
    items: (state) => state.items,
    detailedItems: (state) => {
      let arr = []

      switch (state.detailedItemsBy) {
        case 'total':
          arr = state.detailsGroupedByCircuit
            .map((g) => g.items)
            .reduce((a, v) => a.concat(v), [])
          break

        case 'date':
          arr = state.detailsGroupedByCircuit
            .filter((g) => g.dateFormatted === state.detailedItemsByDateValue)
            .map((g) => g.items)
            .reduce((a, v) => a.concat(v), [])
          break

        case 'vehicle':
          arr = state.detailsGroupedByCircuit
            .filter(
              (g) =>
                g.dateFormatted === state.detailedItemsByDateValue &&
                g.vehicleId === state.detailedItemsByVehicleIdValue
            )
            .map((g) => g.items)
            .reduce((a, v) => a.concat(v), [])
          break

        case 'circuit':
          arr = state.detailsGroupedByCircuit
            .filter((g) => {
              const areCircuitsSame =
                (g.circuitId === 0 &&
                  state.detailedItemsByCircuitIdValue === '') ||
                g.circuitId == state.detailedItemsByCircuitIdValue
              return (
                g.dateFormatted === state.detailedItemsByDateValue &&
                g.vehicleId === state.detailedItemsByVehicleIdValue &&
                areCircuitsSame
              )
            })
            .map((g) => g.items)
            .reduce((a, v) => a.concat(v), [])
          break
      }

      sortByDate(arr, 'datetime')
      return arr
    },
    filteredSynthesisItems: (state) => {
      if (state.detailedItemsBy === 'total') {
        return state.items
      }
      if (state.detailedItemsBy === 'date') {
        return state.items.filter(
          (item) => item.dateFormatted === state.detailedItemsByDateValue
        )
      }
      if (state.detailedItemsBy === 'vehicle') {
        return state.items.filter(
          (item) =>
            item.dateFormatted === state.detailedItemsByDateValue &&
            item.vehicleId == state.detailedItemsByVehicleIdValue
        )
      }
      if (state.detailedItemsBy === 'circuit') {
        return state.items.filter(
          (item) =>
            item.dateFormatted === state.detailedItemsByDateValue &&
            item.vehicleId == state.detailedItemsByVehicleIdValue &&
            (!state.detailedItemsByCircuitIdValue ||
              !!item.circuits.find(
                (circ) => circ.circuitId == state.detailedItemsByCircuitIdValue
              ))
        )
      }
      return []
    },
    sortedItems: (state) => {
      return [...state.items].sort((a, b) =>
        a.timestamp < b.timestamp ? 1 : -1
      )
    },
  },
  mutations: {
    setIdentificationBacSelected(state, data) {
      state.identificationBacSelected = data
    },
    setDetailsGroupedByCircuit(state, data) {
      state.detailsGroupedByCircuit = data
    },
    updateState(state, { key, value }) {
      if (Object.prototype.hasOwnProperty.call(state, key)) {
        state[key] = value
      } else {
        console.warn(`Invalid state key: "${key}"`)
      }
    },
    resetState(state) {
      let s = createState()
      for (var x in s) {
        state[x] = s[x]
      }
    },
  },
  actions: {
    setIdentificationBacSelected({ commit }, idIdentificationBac) {
      commit('updateState', {
        key: 'identificationBacSelected',
        value: idIdentificationBac,
      })
    },
    /**
     * Called after synthesis, when clicking mapTable button.
     *
     * This will load details dataset for table/map
     *
     * Old Split strategy: 1 vehicle/circuit per req sequential (1 req max)
     * New strategy: 1 vehicle/circuit per req, parallel req up to maxParallelReq
     *
     * It will use computed 'filteredSynthesisItems' to fetch a subset, optionally (i.g: The user clicks to show markers from a circuit on the map)
     */
    async fetchDetailsData({ dispatch, commit, state, getters, rootState }) {
      commit('updateState', {
        key: 'detailsGroupedByCircuit',
        value: [],
      })
      commit('updateState', {
        key: 'showTableProgressiveLoader',
        value: true,
      })
      commit('updateState', {
        key: 'fetchAborted',
        value: false,
      })
      commit('updateState', {
        key: 'finalResults',
        value: [],
      })

      const track = console.trackTime('Identification search details')
      let parallelPromises = []
      const maxParallelReq = getEnvIntValue(
        'identificationSearchMaxParallelRequests',
        15
      )

      if (getters.isFetchDetailsInProgress) {
        console.warn('fetchDetailsData skip (isFetchDetailsInProgress)')
        return
      } else {
        commit('updateState', {
          key: 'isFetchDetailsInProgress',
          value: true,
        })
      }

      const generateInitialChunks = () => {
        let subsets = []

        getters.filteredSynthesisItems.forEach((item) => {
          item.circuits.forEach((synthesisItem) => {
            if (
              !state.detailsGroupedByCircuit.find((grouped) => {
                return (
                  grouped.dateFormatted == item.dateFormatted &&
                  grouped.vehicleId == item.vehicleId &&
                  grouped.circuitId == synthesisItem.circuitId
                )
              })
            ) {
              //Filter a single circuit (If user click a toolbar action at circuit level)
              if (
                //Is filter by circuit mode
                state.detailedItemsBy === 'circuit' &&
                //Has a circuit id as filter value ("" = 0)
                ![undefined, null].includes(
                  state.detailedItemsByCircuitIdValue
                ) &&
                //Not the same circuit
                synthesisItem.circuitId !==
                  state.detailedItemsByCircuitIdValue &&
                //Not the same circuit
                !(
                  state.detailedItemsByCircuitIdValue === '' &&
                  synthesisItem.circuitId === 0
                )
              ) {
                return
              }

              subsets.push({
                dateFormatted: item.dateFormatted,
                date: item.date,
                vehicleId: item.vehicleId,
                circuitId: !synthesisItem.circuitId
                  ? 0
                  : synthesisItem.circuitId,
              })
            }
          })
        })

        return subsets
      }

      async function handleSubset(subset) {
        if (state.fetchAborted) {
          return []
        }

        track.count('request')
        let results = await fetchLeveesDetails(
          subset.date,
          subset.vehicleId,
          subset.circuitId,
          {
            filters: {
              puceNumber: state.puceFilterValue,
              identified: state.identifiedFilterValue,
              stopped: state.stoppedFilterValue,
              highPoint: state.highPointFilterValue,
              blacklisted: state.blacklistFilterValue,
              memoryChipNumber: state.memoryChipNumber,
            },
          }
        )
        state.detailsGroupedByCircuit.push(
          Object.freeze({
            dateFormatted: subset.dateFormatted,
            date: subset.date,
            vehicleId: subset.vehicleId,
            circuitId: subset.circuitId,
            items: results,
          })
        )

        state.finalResults.push(...results)
        sortByDate(state.finalResults, 'datetime')
        return results
      }

      let itemChunks = generateInitialChunks()

      track.count('params', {
        itemChunks,
        maxParallelReq,
      })

      for (let singleChunk of itemChunks) {
        if (state.fetchAborted) {
          break
        }
        if (parallelPromises.length > maxParallelReq) {
          parallelPromises = await removeFirstResolved(parallelPromises)
        }
        parallelPromises.push(
          handleSubset(singleChunk).then((r) => {
            if (
              !state.fetchAborted &&
              r.length > 0 &&
              state.searchModuleView !== 'results'
            ) {
              dispatch('search_module/setHasResults', true, { root: true })
              commit('updateState', {
                key: 'showTableProgressiveLoader',
                value: true,
              })
            }
            return r
          })
        )
      }
      track.count(state.fetchAborted ? 'aborted' : 'resolved')
      if (state.fetchAborted) {
        track.stop()
        state.isFetchDetailsInProgress = false
        this.fetchAborted = false
        commit('updateState', {
          key: 'showTableProgressiveLoader',
          value: false,
        })
        return []
      }
      await Promise.all(parallelPromises)
      track.stop()
      state.isFetchDetailsInProgress = false
      commit('updateState', {
        key: 'showTableProgressiveLoader',
        value: false,
      })

      return state.finalResults
    },
    /**
     * Fetch synthesis dataset
     * - Splits requests by vehicle/date (1-1)
     */
    async performsSearch({ dispatch, state, rootGetters, rootState }) {
      state.detailsGroupedByCircuit = []
      state.items = []
      dispatch('clearMapMarkers')
      let dateRanges =
        rootGetters['search_module/getSelection'].selectedDateRanges
      //vehiclers or circuits
      let activeSearchFormTabName =
        rootGetters['search_module/activeSearchFormTabName']

      let selectedElementsIds = rootGetters[
        'search_module/getSelectedIdsByType'
      ](activeSearchFormTabName)

      //Validation: Limit vehicles selection
      if (
        selectedElementsIds.length * dateRanges.length >
        getIdentificationSearchSelectionLimit()
      ) {
        Vue.prototype.$alertPopup.showSelectionLimitWarning(
          selectedElementsIds.length * dateRanges.length,
          getIdentificationSearchSelectionLimit()
        )
        return
      }

      let len = dateRanges.length
      if (len === 0) {
        dateRanges = [
          [
            moment().hour(0).minute(0).second(0)._d,
            moment().hour(23).minute(59).second(59)._d,
          ],
        ]
        len = dateRanges.length
      }
      rootState.search_module.isSearchInProgress = true

      //this.$loader.show()

      let dateFrom = dateRanges[0][0]
      let dateTo = dateRanges[len - 1][1]
      state.dateFromFormatted =
        Vue.prototype.$date.formatDatetimeWithSeconds(dateFrom)
      state.dateToFormatted =
        Vue.prototype.$date.formatDatetimeWithSeconds(dateTo)

      let dates = dateRanges
        .map((range) => range[0])
        .sort((a, b) => (a.getTime() < b.getTime() ? 1 : -1))

      try {
        state.fetchAborted = false
        state.showListSpinner = true
        await fetchLeveesSynthesis({
          elementIds: [...selectedElementsIds],
          elementType: activeSearchFormTabName,
          dates,
          filters: {
            puceNumber: state.puceFilterValue,
            identified: state.identifiedFilterValue,
            stopped: state.stoppedFilterValue,
            highPoint: state.highPointFilterValue,
            blacklisted: state.blacklistFilterValue,
            memoryChipNumber: state.memoryChipNumber,
          },
          handleResults: (results) => {
            if (state.fetchAborted) {
              return false
            }

            if (results.length > 0 && state.searchModuleView !== 'results') {
              dispatch('search_module/setHasResults', true, { root: true })
            }

            state.items = [...state.items, ...results]
            if (
              state.mode !== 'list' &&
              state.items.length > 0 &&
              !state.menuCollapsed
            ) {
              state.menuCollapsed = true
            }
          },
        })
      } finally {
        //why: Switch to results even if empty results
        if (!state.fetchAborted && state.searchModuleView !== 'results') {
          Vue.nextTick(() =>
            dispatch('search_module/setHasResults', true, { root: true })
          )
        }

        //Hide loader in search module search button
        rootState.search_module.isSearchInProgress = false

        state.fetchAborted = false
        state.showListSpinner = false
      }

      //Hide big loader
      //this.$loader.hide()
    },
    async clearMapMarkers({ dispatch }) {
      dispatch(
        'simpliciti_map/setDataset',
        {
          type: 'identificationBacsMarkers',
          data: [],
        },
        { root: true }
      )
    },
  },
}
