/**
 * @namespace Stores
 * @category Stores
 * @module messages
 * */

import { apiCacheStorage } from '@/api/api-cache.js'
import useMessages from '@/composables/messages'
import api from '@/api'
import { groupPlannedMessagesByDay } from '@/services/messages-service'
import * as R from 'ramda'

const { predefinedMessage: predefinedMessageAPI, fetchPredefinedMessages } =
  useMessages()

const createState = () => ({
  predefinedMessages: [],
  plannedMessages: [],
  recurrenceDays: [],
  recurrenceFrequencies: [],
  predefinedMessagesUpdateTimestamp: null,

  showPlannedMessagesTableLoader: true,
})

export default {
  namespaced: true,
  state: createState(),
  getters: {
    showPlannedMessagesTableLoader: (state) =>
      state.showPlannedMessagesTableLoader,
  },
  mutations: {
    updatePredefinedMessages(state, data) {
      state.predefinedMessages = data
      state.predefinedMessagesUpdateTimestamp = Date.now()
    },
    updatePlannedMessages(state, data) {
      state.plannedMessages = data
    },
  },
  actions: {
    async initialize({ dispatch, state }) {
      let track = console.trackTime('messages-store initialize')
      await dispatch('updatePlannedMessages')
      state.showPlannedMessagesTableLoader = false
      track.stop()
    },
    async updateRecurrenceFrequencies({ state }) {
      state.recurrenceFrequencies = await api.recurrenceFrequency.getAll()
    },
    async updateRecurrenceDays({ state }) {
      state.recurrenceDays = await api.recurrenceDay.getAll()
    },
    async updatePlannedMessages({ commit, rootGetters, state, dispatch }) {
      let items = await api.plannedMessage.getAllPooling({
        populate: {
          items: [
            {
              key: 'predefinedMessage',
              //Skip network populate if local data available
              handler(uri, id, populate) {
                return (
                  state.predefinedMessages.find((item) => item.id === id) ||
                  populate()
                )
              },
            },
            {
              key: 'frequency',
              handler: (uri, id, populate) =>
                state.recurrenceFrequencies.find((i) => i.id == id) ||
                populate(),
            },
            {
              key: 'day',
              handler: (uri, id, populate) =>
                state.recurrenceDays.find((i) => i.id == id) || populate(),
            },
          ],
        },
      })

      //Temporal solution to get vehicle name
      //-use local vehicles data
      await dispatch(
        'search_module/initialize',
        {},
        {
          root: true,
        }
      )
      const vehiclesArr = rootGetters['search_module/getVehicles']
      //-retrieve vehicle message plannings
      let vehicleMessagePlannings =
        await api.vehiclePlannedMessage.getAllPooling({
          transform(item) {
            let scope = {
              vehicleId: parseInt(
                item._links.vehicle.href.substring(
                  item._links.vehicle.href.lastIndexOf('/') + 1
                )
              ),
              messagePlanningId: parseInt(
                item._links.messagePlanning.href.substring(
                  item._links.messagePlanning.href.lastIndexOf('/') + 1
                )
              ),
            }
            return {
              ...scope,
              //cross vehicle name from local vehicles
              vehicleName:
                vehiclesArr.find((item) => item.id === scope.vehicleId)?.name ||
                '',
            }
          },
        })
      console.log('vehicleMessagePlannings', {
        items,
        vehicleMessagePlannings,
      })
      //update message planning will all vehicles names for each item
      items = items.map((item) => {
        const relatedVehiclePlannings = vehicleMessagePlannings.filter(
          (i) => i.messagePlanningId == item.id
        )
        item.vehicleIds = relatedVehiclePlannings.map((i) => i.vehicleId)
        //R.uniq was used because a vehicle affected multiple times could end in multiple message_planning_vehicle being created for the same vehicle (API bug?)
        item.vehicleNames = R.uniq(
          relatedVehiclePlannings.map((i) => i.vehicleName)
        ).join(', ')
        return item
      })

      //Feat: Group planned messages by day
      let enableGroupByDay = true

      if (enableGroupByDay) {
        items = groupPlannedMessagesByDay(items)
        console.log('enableGroupByDay', {
          items,
        })
      }

      commit('updatePlannedMessages', items)
    },
    async invalidatePlannedMessagesCache() {
      await apiCacheStorage.invalidateCacheByKeyInclude(
        'message/vehicle_message_plannings'
      )
      await apiCacheStorage.invalidateCacheByKeyInclude(
        'message/message_plannings'
      )
    },
    /**
     * Updates predefined messages once
     */
    async updatePredefinedMessagesOnce({ dispatch, state }) {
      if (!state.predefinedMessagesUpdateTimestamp) {
        await dispatch('updatePredefinedMessages')
      }
    },
    async updatePredefinedMessages({ commit }) {
      let data = await fetchPredefinedMessages()
      commit('updatePredefinedMessages', data)
    },
    async invalidatePredefinedMessagesCache() {
      await apiCacheStorage.invalidateCacheByKeyInclude('message_predefineds')
    },
  },
}
