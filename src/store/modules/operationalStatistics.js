// src/store/modules/operationalStatistics.js

const initialState = () => ({
  filterCriteria: {
    vehicleIds: [],
    circuitIds: [],
    dateRange: {
      // Example: { start: null, end: null }
      start: null,
      end: null,
    },
  },
  isModuleLoading: false,
  moduleError: null,
  poolingProgressDetails: '', // e.g., "Fetching circuit X/Y, vehicle batch A/B..."
  pooledData: [], // This might live more in the composable, but can be mirrored here if needed for broader access
  activeTab: 0,
  operationalPooledData: [],
  lastSearchedFilterCriteria: {
    selectedVehicles: [],
    selectedCircuits: [],
    dateRange: {
      start: null,
      end: null,
    },
  },
})

const state = initialState()

const getters = {
  getOpStatsFilterCriteria: (state) => state.filterCriteria,
  isOpStatsModuleLoading: (state) => state.isModuleLoading,
  getOpStatsModuleError: (state) => state.moduleError,
  getOpStatsPoolingProgress: (state) => state.poolingProgressDetails,
  getOpStatsPooledData: (state) => state.pooledData, // If storing pooled data here
  getOpStatsOperationalPooledData: (state) => state.operationalPooledData, // Assuming this is the same structure as pooledData
  hasResults: (state) => {
    return state.pooledData.length > 0 || state.operationalPooledData.length > 0
  },
}

const mutations = {
  SET_OP_STATS_FILTER_CRITERIA(state, criteria) {
    state.filterCriteria = { ...state.filterCriteria, ...criteria }
  },
  SET_OP_STATS_MODULE_LOADING(state, isLoading) {
    state.isModuleLoading = isLoading
  },
  SET_OP_STATS_MODULE_ERROR(state, error) {
    state.moduleError = error
    if (error) {
      console.error('OperationalStatisticsModule Error:', error)
    }
  },
  SET_OP_STATS_POOLING_PROGRESS(state, progress) {
    state.poolingProgressDetails = progress
  },
  SET_OP_STATS_POOLED_DATA(state, data) {
    // If storing pooled data here
    state.pooledData = data
  },
  RESET_OP_STATS_STATE(state, full) {
    //Preserve active tab
    const preservedTab = state.activeTab
    Object.assign(state, initialState())

    //Preserve active tab if not resetting fully
    if (!full) {
      state.activeTab = preservedTab
    }
  },
  SET_OP_STATS_ACTIVE_TAB(state, tabIndex) {
    state.activeTab = tabIndex
  },
  SET_OP_STATS_OPERATIONAL_POOLED_DATA(state, data) {
    state.operationalPooledData = data // Assuming this is the same structure as pooledData
  },
  SET_LAST_SEARCHED_FILTER_CRITERIA(state, criteria) {
    state.lastSearchedFilterCriteria = {
      selectedVehicles: [...criteria.selectedVehicles],
      selectedCircuits: [...criteria.selectedCircuits],
      dateRange: { ...criteria.dateRange },
    }
  },
  RESET_FILTER_CRITERIA(state) {
    state.lastSearchedFilterCriteria = {
      selectedVehicles: [],
      selectedCircuits: [],
      dateRange: {
        start: null,
        end: null,
      },
    }
  },
}

const actions = {
  setOpStatsPooledData({ commit }, data) {
    commit('SET_OP_STATS_POOLED_DATA', data)
  },
  // Initializes module state, could be called on component mount
  initializeOperationalStatisticsModule({ commit }, full = true) {
    commit('RESET_OP_STATS_STATE', full) // Start fresh or load from persisted state if any
    // Potentially fetch initial filter options if not handled elsewhere
  },

  // Updates filters and can optionally trigger data fetching
  updateOpStatsFilters({ commit }, newCriteria) {
    commit('SET_OP_STATS_FILTER_CRITERIA', newCriteria)
    // Optionally, trigger data fetch here or let the component do it
  },

  // Example action to set loading state
  setOpStatsLoading({ commit }, isLoading) {
    commit('SET_OP_STATS_MODULE_LOADING', isLoading)
  },

  // Example action to set error state
  setOpStatsError({ commit }, error) {
    commit('SET_OP_STATS_MODULE_ERROR', error)
  },

  // Example action to set progress
  setOpStatsPoolingProgress({ commit }, progress) {
    commit('SET_OP_STATS_POOLING_PROGRESS', progress)
  },
  // Example action to update pooled data in store (if used)
  updateOpStatsPooledData({ commit }, data) {
    commit('SET_OP_STATS_POOLED_DATA', data)
  },

  setOpStatsOperationalPooledData({ commit }, data) {
    commit('SET_OP_STATS_OPERATIONAL_POOLED_DATA', data)
  },
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}
