const state = {
  templates: [],
  selectedTemplate: null,
}

const getters = {
  getTemplates: (state) => state.templates,
  getSelectedTemplate: (state) => state.selectedTemplate,
}

const mutations = {
  SET_TEMPLATES(state, templates) {
    state.templates = templates
  },
  SET_SELECTED_TEMPLATE(state, template) {
    state.selectedTemplate = template
  },
}

const actions = {
  loadTemplates({ commit }, templates) {
    console.debug(9,'[selection_templates] Loading templates:', templates)
    commit('SET_TEMPLATES', templates)
  },
  setSelectedTemplate({ commit }, template) {
    console.debug(9,'[selection_templates] Setting selected template:', template)
    commit('SET_SELECTED_TEMPLATE', template)
  },
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}
