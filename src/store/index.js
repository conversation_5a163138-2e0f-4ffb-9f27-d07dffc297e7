import Vue from 'vue'
import Vuex from 'vuex'
import auth from './auth'
import ecoConduite from './ecoConduite'
import sidebar from './sidebar'
import navbar from './navbar'
import searchbar from './searchbar'
import alert from './alert'
import app from './app'
import analysis from './analysis'
import datatable from './datatable'
import search_module from './search_module/search_module'
import favorites from './search_favorites'
import location_module from './location_module'
import simpliciti_map from './simpliciti_map'
import map_options from './map_options'
import zones from './zones'
import settings from './settings'
import diagnostics from './diagnostics-store'
import box from './box-store'
import mapToolbox from './map-toolbox'
import latestPassedVehicles from './latestPassedVehicles'
import messages from './messages'
import black_spot from './black_spot'
import containers from './containers'
import containersModuleStore from './containers-module-store'
import referenceCircuitMapPopup from './reference_circuit_map_popup'
import events from './events'
import release_notes from './release_notes'
import map_context_menu from '@/store/map_context_menu'
import nearby_items from '@/store/nearby_items'
import geocoding from './geocoding.js'
import selection_templates from './modules/selection_templates'
import identification from './identification'
import referenceCircuit from './reference_circuit'
import operationalStatistics from './modules/operationalStatistics' // Added operationalStatistics module

Vue.use(Vuex)

console.debug('[Store] Creating store')

export default new Vuex.Store({
  state: {},
  mutations: {},
  actions: {},
  modules: {
    diagnostics,
    auth,
    ecoConduite,
    analysis,
    sidebar,
    navbar,
    searchbar,
    alert,
    app,
    datatable,
    search_module,
    favorites,
    location_module,
    simpliciti_map,
    map_options,
    zones,
    settings,
    box,
    mapToolbox,
    latestPassedVehicles,
    messages,
    black_spot,
    containers,
    containersModuleStore,
    referenceCircuitMapPopup,
    events,
    release_notes,
    map_context_menu,
    nearby_items,
    geocoding,
    selection_templates,
    identification,
    referenceCircuit,
    operationalStatistics, // Added operationalStatistics module
  },
})
