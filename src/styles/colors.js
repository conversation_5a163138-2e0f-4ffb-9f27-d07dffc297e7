/**
 * @todo Figure a way to reuse the same color codes from colors.scss
 * Method 1: Use the variable directly instead of hardcoded color (i.g var(--color-green))
 * Method 2: (If the color will be used by a third party lib which doesn't have access to the variable) Compute the color code use the variable (if available) getComputedStyle(
 *             document.querySelector(':root')
 *           ).getPropertyValue(
 *             'var(--color-main)'.split('var(').join('').split(')').join('')
 *           )
 */
export default {
  color_main: '#0b72b5',
  color_main_trans_high: '#0b71b51f',
  color_hamburger: '#4392c6',
  color_main_alt: '#2c6488',
  color_black: '#646464',
  color_dark_blue: '#014470',
  color_search_module_button: '#ebebeb',

  color_silver_tree: '#70bd95',
  color_tundora: '#484848',
  color_sandy_brown: '#f6ac59',
  color_coral_red: '#ff4545',
  color_deep_cerulean: '#0a71ac',
  color_slate_gray: '#748591',
  color_loblolly: '#c2cdd3',
  color_thunderbird: '#d41616',
  color_denim: '#0b72b5',
  color_denim_trans: '#014470b7',
  color_disco: '#861160',
  color_dove_gray: '#707070',
  color_concrete: '#f2f2f2',
  color_regal_blue: '#014470',
  color_wild_sand: '#f5f5f5',
  color_silver_chalice: '#aaaaaa',
  color_alizarin_crimson: '#ed2c2c',
  color_metal_rock: '#727272',

  color_speed: '#28B51A',
  color_contact_off: '#FF5555',
  color_contact_on: '#70A580',
  color_border: '#6c757d',
}
