//@import "./transitions.scss";
//@import './bootstrap.scss';
@import './custom.scss';
@import './buttons.scss';
@import './colors.scss';
@import './inputs.scss';
@import './map.scss';
@import './datatable.scss';
@import './titles.scss';
@import './directives/resizeable';
@import './search-module.scss';
@import './results-lists.scss';
@import "./ag-grid.scss";

body {
  font-family: 'Open Sans', sans-serif;
  font-size: 16px;
  overflow-x: hidden; /* comment to debug offset issues (it shouldn't be an scroll-x by default) */
}

h1,
h2,
h3,
h4,
h5,
h6,
label,
span,
button {
  font-family: 'Open Sans', sans-serif;
}
span[role='img'] {
  font-family: initial;
}

body,
body * {
  scrollbar-color: white;
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }
}

/*Global scrollbar style rule*/
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background-color: white;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-tundora);
  height: 15px;
  border-radius: 10px;
}

.blue {
  color: var(--color-dark-blue);
}

.small-text {
  font-size: 0.75rem;
}

.collapse-icon {
  position: absolute;
  right: 15px;
  padding: 5px;
}
.collapse:focus {
  outline: 0;
}

.unselectable {
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  user-select: none; /* Standard */
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-center-left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.flex-center-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
