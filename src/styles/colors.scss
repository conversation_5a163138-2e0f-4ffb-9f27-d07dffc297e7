:root {
  /** MAIN COLOR SCHEME **/
  /*@TODO: Reduce the number of colors (i.g Pick a color used in a single place, remplace it and remove it)*/
  /** These are colors shared across the entire application (I.g buttons, backgrounds, borders, fonts) **/
  --color-main: #0b72b5;
  --color-hamburger: #4392c6; /*diag tor5*/
  --color-main-alt: #0b71b51f; /*TODO: Replace/Remove*/
  --color-black: #646464;
  --color-dark-blue: #014470; /*alternative to main color*/
  --color-search-module-button: #ebebeb; /*TODO: Replace/Remove*/
  --color-silver-tree: #70bd95; /*fa-check and diag ana5*/
  --color-tundora: #484848; /*main color for fonts across the app*/
  --color-sandy-brown: #f6ac59; /*warning icon in location/identification */
  --color-coral-red: #ff4545; /*fa-times icon color, contact off color*/
  --color-deep-cerulean: #0a71ac; /*diag ana3*/
  --color-slate-gray: #748591; /*form builder font color and border color, addressautocomplete border color*/
  --color-loblolly: #c2cdd3; /*diag ana2, toggle button shadow color*/
  --color-thunderbird: #d41616; /*diag ana1 */
  --color-denim: var(
    --color-main
  ); /*TODO: Remove this (replace references with main color)*/
  --color-denim-trans: #014470b7; /*Used by Login view */
  --color-dove-gray: #707070; /*diagnostics tor6 and 7*/
  --color-concrete: #f2f2f2; /*Location result item hover color*/
  --color-wild-sand: #f5f5f5; /*Location history/circuit tab, layout menu background color*/
  --color-silver-chalice: #aaaaaa; /*horizontal button toggles color*/
  --color-alizarin-crimson: #ed2c2c; /*settings menu icons state color */
  --color-metal-rock: #727272; /*font, button background*/
  --color-picton-blue: #42a5f5; /*gps chart diagnostics*/
  --color-fern: #66bb6a; /*speed */
  --color-french-rose: #ec407a; /*harsh breaking */
  --color-celery: #9ccc65; /*harsh acceleration, acceleration*/
  --color-regent-grey: #78909c; /*spe harsh cornering*/
  --color-sunshade: #ffa726; /*spe excessive speed, tor7 */
  --color-gorse: #ffee58; /*can rpm, sensor tor8 */
  --color-scooter: #26c6da; /*fuel level perc*/
  --color-burnt-sienna: #ef5350; /*fuel level*/
  --color-silver: #bdbdbd; /*check icon, play icon, map options contact button, etc */
  --color-sunglow: #ffca28; /* can brake pedal */
  --color-green: #70a580; //contact on /*#F6AC59*/
  --color-red: #ff5555; //contact off /*#FF4545/
  --color-polyline-highlight: #89e5b1; /*NEW: #89e5b1;*/
  --color-resizeable-accent: var(
    --vs-colors--lightest
  ); /* Couleur d'accentuation de la barre de resize */

  --color-sidebar-bg-hover: #0b72b5; /*Couleur de fond lors du survol de la souris*/
  --color-location: #ab47bc; /*Module Localisation*/
  --color-dashboard: #26c6da;
  --color-zones: #ffa726;
  --color-citipav: #70bd95;
  --color-admin: #546d79;
  --color-analysis: #26a69a;
  --color-alerts: #ffca28;
  --color-help: #39a9dc;
  --color-identification: #014470;
  --color-ecoconduite: #66bb6a;
  --color-citifret: #0b72b5;
  --color-events: #ec407a;
  --color-messages: #0b72b5;
  --color-containers: #014470;
  --color-containers-module: #4c7f63;
  --color-editour: #546d79;
  --color-citimission: #0b72b5;
  --color-black-points: #5c6bc0;
  --color-geored-desktop: #0b72b5;
  --color-login-as: #0b72b5; /*User icon above settings*/
  --color-settings: #0b72b5;
  --color-logout: #0b72b5;
  --color-active-item: #e0e0e0;
  --color-border: #6c757d; /*Used in search module and progress bar*/

  /**
  Donuts version: https://easyredmine.simpliciti.fr/issues/43286
  **/
  --color-container-state-collected: #66cc33; /*collected*/
  --color-container-state-planned: #ff9900; /*not collected but planned */
  --color-container-state-no-collected: #ff3333; /*not collected and circuit exec finished*/
  --color-container-state-no-planned: #546d79; /* not planned or unknown*/
  --color-container-state-blue: #1e3a8a; /*used for metrics only*/

  /**
  First version (POC/LOT1) 
--color-container-state-collected: #66cc33; 
--color-container-state-planned: #f97316;
--color-container-state-no-collected: #ef4444;
--color-container-state-no-planned: #6b7280; 
--color-container-state-blue: #1e3a8a; 
**/

  /**
#4d7c0f
#f97316
**/

  /** -- BELOW, WE MUST REUSE COLORS FROM THE MAIN COLOR SCHEME ABOVE **/

  --color-contact-on: var(--color-green);
  --color-contact-off: var(--color-red);

  /** Predefined CAN, CAN SPE, Normal GPS point, Speed **/
  --color-gps: var(--color-picton-blue);
  --color-speed: var(--color-fern);
  --color-harsh-braking: var(--color-french-rose);
  --color-harsh-acceleration: var(--color-celery);
  --color-acceleration: var(--color-celery);
  --color-harsh-speHarshCornering: var(--color-regent-grey);
  --color-harsh-speExcessiveSpeed: var(--color-sunshade);
  --color-horizontal-button-toggle: var(--color-silver-chalice);
  --color-horizontal-button-toggle-true: var(--color-main);
  --color-can-rpm: var(--color-gorse);
  --color-can-fuel-level-perc: var(--color-scooter);
  --color-can-fuel-level-liters: var(--color-burnt-sienna);
  --color-can-fuel-level: var(--color-burnt-sienna);
  --color-can-sensorCanConsumptionLiters: var(--color-green);
  --color-can-vehicle-distance: var(--color-silver);
  --color-can-brake-pedal: var(--color-sunglow);
  --color-can-sensorCanBatteryPerc: var(--color-sandy-brown);

  /**Predefined TOR/ANA colors**/
  --color-sensor-tor1: var(--color-metal-rock);
  --color-sensor-tor2: var(--color-alizarin-crimson);
  --color-sensor-tor3: var(--color-silver);
  --color-sensor-tor4: var(--color-dark-blue);
  --color-sensor-tor5: var(--color-hamburger);
  --color-sensor-tor6: var(--color-dove-gray);
  --color-sensor-tor7: var(--color-sunshade);
  --color-sensor-tor8: var(--color-gorse);

  --color-sensor-ana1: var(--color-thunderbird);
  --color-sensor-ana2: var(--color-loblolly);
  --color-sensor-ana3: var(--color-deep-cerulean);
  --color-sensor-ana4: var(--color-tundora);
  --color-sensor-ana5: var(--color-silver-tree);
  --color-sensor-ana6: var(--color-search-module-button);
  --color-sensor-ana7: var(--color-dove-gray);
  --color-sensor-ana8: var(--color-main-alt);

  /** Cleaning sensors **/

  /* Niveau d'eau dans la cuve */
  --color-sensor-sweep-level: var(--color-picton-blue);
  /* Porte arrière fermée */
  --color-sensor-sweep-rear-door: var(--color-black);
  /* Vitesse de la brosse */
  --color-sensor-sweep-brush-sp: var(--color-fern);
  /* Travail de la turbine */
  --color-sensor-sweep-fan: var(--color-coral-red);
  /* Vitesse de la turbine */
  --color-sensor-sweep-fan-sp: var(--color-coral-red);
  /* Pompe d'humectage */
  --color-sensor-sweep-pump: var(--color-picton-blue);
}
