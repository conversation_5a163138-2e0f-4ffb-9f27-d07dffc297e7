import { getEnvIntValue } from '@/services/env-service'
import { getNestedValue } from '@/utils/object'
import L from 'leaflet'
import mitt from '@/plugins/mitt.js'

export const defaultLabelPath = 'properties.item.vehicule.nom'

/**
 * Goal: Allow user to quickly switch between markers popups (nearby markers that collide each other)
 * @returns {Object} pluginScopeObject
 */
export function useSwitchablePopup() {
  
  /**
   *
   * Used globally by marker popup components such as VehicleMarkerPopup to inject
   * some dynamic arrows to programatically switch between nearby marker popups.
   *
   * spm plugin (Switchable popup mixin)
   * The plugin state will reset if:
   * - User click on the map (anywhere)
   * - User closes a map marker popup
   * The nearby layers will be set
   */
  // @ts-ignore
  const rootScope = (window._spm = {
    circle: null,
    radius: null,
    map: null, //Leaflet map instance
    layerGroupName: '',
    parentMapSelector: '.map-wrapper',
    mapSelector: '.map.leaflet-container',
    binded: false,
    nearbyLayers: [],
    labelPath: defaultLabelPath,
    index: 0,
    elements: null,
    unbindedAt: null,
    unbindCheckHandler: null,
    labelPrefix: '',
    async unbindCheck() {
      if (
        !rootScope.unbindCheckHandler ||
        (rootScope.unbindCheckHandler && (await rootScope.unbindCheckHandler()))
      ) {
        rootScope.unbind()
      }
    },
    unbind() {
      if (!rootScope.mapSelector) {
        return
      }

      let mapEl = document.querySelector(rootScope.mapSelector)
      if (mapEl) {
        mapEl.removeEventListener('click', rootScope.unbindCheck)
        // @ts-ignore
        mapEl.dataset.spm = '0'
      }
      if (rootScope.circle && rootScope.circle.remove) {
        rootScope.circle.remove()
        rootScope.circle = null
        if (rootScope.map) {
          rootScope.map.eachLayer((layer) => {
            if (layer.isSwitchablePopupCircle) {
              rootScope.map.removeLayer(layer)
            }
          })
        }
      }
      rootScope.radius = null
      rootScope.map = null
      rootScope.layerGroupName = ''
      rootScope.parentMapSelector = '.map-wrapper' //Reset parentMapSelector on unbind allows to rebind on correct map
      rootScope.mapSelector = '.map.leaflet-container'
      rootScope.binded = false
      rootScope.nearbyLayers = []
      rootScope.index = 0
      rootScope.labelPath = defaultLabelPath
      let elementsWrapper = document.querySelector(
        '.leaflet-switchable-popup-arrow-wrapper'
      )

      document.removeEventListener('keyup', rootScope.onKeyUpListener)

      if (elementsWrapper) {
        elementsWrapper.remove()
      }

      rootScope.elements = null

      rootScope.unbindedAt = Date.now()
      console.log(`spm unbind`, rootScope.unbindedAt)
    },
    onKeyUpListener(e) {
      let previousIndex = rootScope.index

      switch (e.key) {
        case 'ArrowLeft':
        case 'A':
          rootScope.updateIndex('left')
          rootScope.updateState(previousIndex, rootScope.index)
          e.preventDefault()
          e.stopImmediatePropagation()
          e.stopPropagation()
          break
        case 'ArrowRight':
        case 'D':
          rootScope.updateIndex('right')
          rootScope.updateState(previousIndex, rootScope.index)
          e.preventDefault()
          e.stopImmediatePropagation()
          e.stopPropagation()
          break
      }
    },
    updateIndex(direction: string) {
      if (direction === 'left') {
        rootScope.index =
          rootScope.index === 0
            ? rootScope.nearbyLayers.length - 1
            : rootScope.index - 1
      } else {
        rootScope.index =
          rootScope.index === rootScope.nearbyLayers.length - 1
            ? 0
            : rootScope.index + 1
      }
    },
    /**
     * Semi-transparent circle draw once (to represent nearby layer search radius)
     */
    updateLeafletCircle() {
      let layer = rootScope.nearbyLayers[rootScope.index]
      let map = rootScope.map
      let radius = rootScope.radius

      if (layer && map && radius) {
        let circle = rootScope.circle || null
        if (circle) {
          circle.setLatLng([layer.getLatLng().lat, layer.getLatLng().lng])
        } else {
          circle = L.circle(
            [layer.getLatLng().lat, layer.getLatLng().lng],
            radius,
            {
              color: 'white',
              opacity: 0.2,
              fillOpacity: 0.2,
              fillColor: 'white',
              radius: radius,
            }
          ).addTo(map)
          circle.isSwitchablePopupCircle = true
        }
        rootScope.circle = circle
      }
    },
    /**
     * Lazy state bind
     */
    bind() {
      let mapEl = document.querySelector(rootScope.mapSelector)

      if (mapEl) {
        // @ts-ignore
        if (mapEl.dataset.spm !== '1') {
          // @ts-ignore
          mapEl.dataset.spm = '1'

          mapEl.addEventListener('click', rootScope.unbindCheck)
          let popup = document.querySelector('.leaflet-popup-content-wrapper')
          if (popup) {
            popup.addEventListener('click', (e) => e.stopPropagation())
          }
          rootScope.binded = true

          rootScope.elements = configureDomElements(
            ({ direction }: { direction: string } = { direction: 'left' }) => {
              let previousIndex = rootScope.index
              rootScope.updateIndex(direction)
              rootScope.updateState(previousIndex, rootScope.index)
              console.log('spm click arrow', direction)
            }
          )

          document.addEventListener('keyup', rootScope.onKeyUpListener)

          rootScope.updateState(0, 0)

          rootScope.updateLeafletCircle()

          console.log('spm bind !')
        }
      } else {
        console.warn('spm bind fail given map is not available')
      }
    },
    updateState(previousIndex: number, currentIndex: number) {
      let prevLayer = rootScope.nearbyLayers[previousIndex]
      let layer = rootScope.nearbyLayers[currentIndex]

      if (!layer) {
        console.warn('spm invalid layer')
        rootScope.unbind()
        return
      }

      let text =
        getNestedValue(
          layer,
          rootScope.labelPath,
          `Marker ${rootScope.index + 1}`,
          {
            onDefaultCallback() {
              console.warn(`Failed to compute marker label`, {
                layer,
                path: rootScope.labelPath,
              })
            },
          }
        ) + `<br/>${rootScope.index + 1}/${rootScope.nearbyLayers.length}`

      rootScope.elements.text.innerHTML = rootScope.labelPrefix + text

      //Trick: Get real layer from map instance
      const getRealLayer = (layer) => {
        try {
          // @ts-ignore
          let map = window.lmw.map
          let id = JSON.stringify(layer.properties) //unique id
          // @ts-ignore
          let match = Object.values(window.lmw.map._layers).find(
            (l: any) => JSON.stringify(l.properties) == id
          )
          return match || layer
        } catch (err) {
          console.warn('spm error while retrieving real layer', err)
          return layer
        }
      }
      prevLayer = getRealLayer(prevLayer)
      layer = getRealLayer(layer)

      if (rootScope.layerGroupName) {
        try {
          mitt.emit('leaflet-reinsert-layer', {
            groupName: rootScope.layerGroupName,
            layer,
            id: layer.externalId,
          })

          console.log('spm layer sort', rootScope.layerGroupName, layer)
        } catch (err) {
          console.warn('spm bring to front fail', {
            err,
          })
        }
      } else {
        console.warn('spm sort fail no layer group')
      }

      layer.openPopup()

      console.log('spm updateState ', {
        text,
        prevLayer,
        layer,
      })
    },
    /**
     * Called from mixin when the popup is opened. Will only work if the plugin is not bind.
     * @param layers
     */
    setNearbyLayers(layers) {
      if (!rootScope.binded) {
        rootScope.nearbyLayers = layers
        rootScope.index = 0
        console.log('spm setNearbyLayers', layers.length)
      }
    },
  })
  return rootScope
}

//Attention: State is shared across implementation ! (Do not forgot to reset all on unbind)
const singleRootScope = useSwitchablePopup()

/**
 * Feat: Adds arrows to Leaflet popups to switch marker popups quickly (nearby markers)
 * Used as workaround when markers collides each other and selection becomes hard.
 */
export default {
  data() {
    return {
      /**
       * Path to compute text/label (e.g properties.item.vehicule.nom applies to location realtime)
       */
      spmLabelPath: defaultLabelPath,
      spmLayerGroupName: '',
      spmScope: singleRootScope,
      spmLabelPrefix: '',
      spmComputeNearbyLayersUsingLeafletLayerGroupLayersAccessor: false,
    }
  },
  computed: {
    layoutId() {
      return JSON.stringify(this.$store?.state?.app?.layout || {})
    },
  },
  watch: {
    /**
     * React to layout changes (Location module)
     */
    layoutId() {
      if (this.spmScope.binded) {
        this.spmScope.unbind()
        this.initSwitchablePopupMixin()
      }
    },
  },
  created() {
    /**
     * Hack to cancel unbind on map click if map drag detected
     */
    const bindPreventSpmUnbinIfMapDrag = () => {
      let lastMoveEndAt = null
      //bind again if unbinded within 2s
      const handler = () => {
        lastMoveEndAt = Date.now()
      }
      this.$mitt.on('map_moveend', handler)
      this.spmScope.unbindCheckHandler = () => {
        return new Promise((resolve) => {
          setTimeout(() => {
            if (lastMoveEndAt && Date.now() - lastMoveEndAt < 1000) {
              console.log('Abort unbind (map moveend detected)')
              return false
            } else {
              return true
            }
          }, 1000)
        })
      }
      this.unbindMapMoveEnd = () => {
        this.$mitt.off('map_moveend', handler)
        this.spmScope.unbindCheckHandler = null
      }
    }

    bindPreventSpmUnbinIfMapDrag()
  },
  mounted() {
    //Allow some time so that the plugin state resets
    this.$nextTick(() => {
      setTimeout(() => {
        if (this.spmScope.binded) {
          //If user click another marker (not a nearby one), unbind existing plugin
          if (
            !this.spmScope.nearbyLayers.some(
              (l) => l.externalId === this.geometry.externalId
            )
          ) {
            console.log('unbind because marker is not nearby')
            this.spmScope.unbind()
          }
        }
        this.initSwitchablePopupMixin()
      }, 500)
    })
  },
  destroyed() {
    this.unbindMapMoveEnd()
  },
  methods: {
    /**
     * Call this function from a popup component
     * - Initializes the spm plugin
     * - Optionally, customize the spmLabelPath property in the popup component
     * @param isUnitTest is a hack to make unit tests pass. If true, the method will run with fake data.
     * @returns
     */
    initSwitchablePopupMixin(isUnitTest = false) {
      const geometry = isUnitTest ? { lat: 10, lng: 20 } : this.geometry

      if (!geometry) {
        return console.warn(
          'spm Switcheable popup mixin init fail, expected geometry property to exists'
        )
      } else {
        //Hack for use-case (Event module): normal leaflet group sub-layers do not have a _parent accessor
        let layerGroups = []
        if (
          this.spmComputeNearbyLayersUsingLeafletLayerGroupLayersAccessor &&
          this.spmLayerGroupName
        ) {
          layerGroups =
            this.$map.getLeafletWrapperVM().layerGroups[
              this.spmLayerGroupName
            ] || null
          // @ts-ignore
          layerGroups = layerGroups ? layerGroups.getLayers() : []
        }

        let rootScope = this.spmScope

        let radius = parseInt(
          getEnvIntValue(
            'globalFeatureSwitchableLeafletMarkerPopupMatchRadius',
            '30'
          )
        )
        rootScope.radius = radius
        let nearbyLayers = isUnitTest
          ? [{ id: 1 }, { id: 2 }]
          : getNearbyLeafletLayersInRadius(geometry, radius, layerGroups)

        if (nearbyLayers.length === 0) {
          return
        }

        if (!rootScope.binded) {
          rootScope.labelPath = this.spmLabelPath
          rootScope.layerGroupName =
            this.spmLayerGroupName || rootScope.layerGroupName
          rootScope.labelPrefix = this.spmLabelPrefix || ''
        }
        rootScope.setNearbyLayers([geometry, ...nearbyLayers])
        rootScope.map = this.$map.getLeafletInstance()
        rootScope.bind()
      }
    },
    /**
     * Hack to force unbind in case of destroyed component
     * Used in nearby items components
     */
    unbindFromDestroyedComponent() {
      if (singleRootScope) {
        singleRootScope.unbind()
      }
    },
    /**
     * Add custom css class to leaflet container
     * Used by nearby items
     * Allow to bind the markers to another map that the main
     * @param parentSelector
     * @param childSelector
     */
    addCustomClassToLeafletContainer(
      parentSelector: string,
      childSelector: string
    ) {
      const defaultMapContainer = '.map.leaflet-container'

      const parentMap = document.querySelector('.' + parentSelector)

      if (parentMap) {
        // Find appropriate child to add class to
        const mapContainer = Array.from(parentMap.children).find(
          (child) =>
            child.classList.contains('map') &&
            child.classList.contains('leaflet-container')
        )

        // Return if no mapContainer found
        if (!mapContainer) {
          console.warn('Map container not found')
          return
        }

        //Otherwise add the specific class
        mapContainer.classList.add(childSelector)

        const selector = defaultMapContainer + '.' + childSelector

        this.setParentMapSelector(parentSelector)
        this.setSpmMapSelector(selector)
      }
    },

    /**
     * Set spmMapSelector
     * Used to bind the markers to the correct map
     * @param value
     */
    setSpmMapSelector(value: string) {
      if (singleRootScope) {
        singleRootScope.mapSelector = value
      }
    },

    /**
     * Set parentMapSelector
     * Used to position the arrow wrapper within the correct map
     * @param value
     */
    setParentMapSelector(value: string) {
      if (singleRootScope) {
        singleRootScope.parentMapSelector =
          '.map-wrapper' + (value ? '.' + value : '')
      }
    },
  },
}

/**
 * @param {Function} [onClickHandler] Receives direction(left/rigth)
 * @returns
 */
function configureDomElements(onClickHandler: Function = () => {}) {
  console.log('spm configureDomElements')

  let arrowHtmlTemplate = `<span class="fas fa-chevron-left" style="font-size: 20px"/>`

  const scope = { left: null, right: null, text: null, wrapper: null }

  let arrowsWrapperEl = document.querySelector(
    '.leaflet-switchable-popup-arrow-wrapper'
  )

  // @ts-ignore
  function positionInRefElCenterBottom(
    targetEl: any = '.leaflet-switchable-popup-arrow-wrapper',
    refEl: any = '.map-wrapper'
  ) {
    refEl = typeof refEl === 'string' ? document.querySelector(refEl) : refEl
    targetEl =
      typeof targetEl === 'string' ? document.querySelector(targetEl) : targetEl
    let bottomY = refEl.offsetHeight + refEl.offsetTop
    let centerX =
      refEl.offsetLeft + refEl.offsetWidth / 2 - targetEl.offsetWidth / 2
    targetEl.style.position = 'absolute'
    targetEl.style.left = `${centerX}px`
    targetEl.style.top = `${bottomY - targetEl.offsetHeight - 10}px`
  }

  //Lazy add wrapper, arrows and text dom elements
  if (!arrowsWrapperEl) {
    arrowsWrapperEl = document.createElement('div')
    arrowsWrapperEl.className = 'leaflet-switchable-popup-arrow-wrapper'

    // @ts-ignore
    arrowsWrapperEl.style.cssText = `
    position: absolute;
bottom: 0px;
width: 200px;
height: 70px;
background-color: rgba(255, 255, 255, 0.87);
display: flex;
justify-content: center;
align-items: center;
z-index: 9999999;
border-radius: 20px;
box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.3607843137);
display: flex;
justify-content: space-evenly
    
  `
    //Dynamically set mapWrapper
    //Nearby items maps use specific selectors to not use the main map
    let mapWrapper = document.querySelector(singleRootScope.parentMapSelector)

    mapWrapper.appendChild(arrowsWrapperEl)

    positionInRefElCenterBottom(arrowsWrapperEl, mapWrapper)

    // Add arrowEl to arrowsWrapperEl
    const addArrow = (direction: string = 'left') => {
      let arrowEl = document.createElement('span')
      arrowEl.innerHTML =
        direction === 'left'
          ? arrowHtmlTemplate
          : arrowHtmlTemplate.replace('chevron-left', 'chevron-right')
      arrowEl.className = 'leaflet-switchable-popup-arrow ' + direction
      arrowEl.style.cssText = `
      cursor:pointer;
      color: var(--color-main);
      `
      arrowEl.addEventListener('click', (e) => {
        e.stopPropagation()
        onClickHandler({ direction })
      })
      arrowsWrapperEl.appendChild(arrowEl)
    }

    addArrow()

    let p = document.createElement('p')
    p.className = 'leaflet-switchable-popup-text'
    p.style.cssText = `
    font-weight: normal;
    font-size: 12px;
    text-align: center;
    margin-bottom: 0px;
    `
    arrowsWrapperEl.appendChild(p)

    addArrow('right')
  }

  scope.wrapper = document.querySelector(
    '.leaflet-switchable-popup-arrow-wrapper'
  )
  scope.left = document.querySelector('.leaflet-switchable-popup-arrow.left')
  scope.text = document.querySelector('.leaflet-switchable-popup-text')
  scope.right = document.querySelector('.leaflet-switchable-popup-arrow.right')

  return scope
}

/**
 * Given a leaflet layer, it will return a list of markers from the same layer group/parent within a radius in meters
 * @param {Layer} refLeafletLayer The reference Leaflet layer
 * @param {number} [radiusMeters] The radius in meters
 * @returns An array of nearby Leaflet layers
 */
function getNearbyLeafletLayersInRadius(
  refLeafletLayer: L.Layer,
  radiusMeters: number,
  parentLayers: any[] = []
): L.Layer[] {
  // @ts-ignore
  const parent = refLeafletLayer.__parent ? refLeafletLayer.__parent : null

  if (parentLayers.length === 0 && !parent) {
    console.warn(
      'spm getNearbyLeafletLayersInRadius unable to access parent',
      refLeafletLayer
    )
    return []
  }

  //Use-case: Leaflet Cluster group
  if (parentLayers.length === 0 && parent.getAllChildMarkers) {
    parentLayers = parent.getAllChildMarkers()
  }

  //Use-case: Normal leaflet group
  // @ts-ignore
  if (parentLayers.length === 0 && parent.getLayers()) {
    // @ts-ignore
    parentLayers = parent.getLayers()
  }

  const nearbyLayers: L.Layer[] = []
  // @ts-ignore
  const refLatLng: L.LatLng = refLeafletLayer.getLatLng()
  parentLayers.forEach((layer: L.Layer) => {
    if (layer !== refLeafletLayer) {
      // @ts-ignore
      const layerLatLng: L.LatLng = (layer as L.Marker).getLatLng()
      const distance: number = refLatLng.distanceTo(layerLatLng)
      if (distance <= radiusMeters) {
        nearbyLayers.push(layer)
      }
    }
  })

  return nearbyLayers
}
