import Vue from 'vue'
import { computed } from 'vue'

/**
 * In SFC components, we can't use this, therefore we can't use plugins like in Options API
 * Allows to provide plugins to SFC components without using this.$pluginName
 */
export function getPluginProviders() {
  const translations = Vue.prototype.$translations
  const date = Vue.prototype.$date
  const map = Vue.prototype.$map

  return {
    $translations: computed(() => translations),
    $date: date,
    $map: map,
  }
}
