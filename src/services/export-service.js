import i18n from '@/i18n'
import moment from 'moment'
import { isUnitTest } from '@/utils/unit.js'
import writeXlsxFile from 'write-excel-file'
import { computeColumnWidths } from '@/utils/excel'
const tablesPrefixes = {
  alerts: 'alertes',
  identification: 'identification',
  location: 'localisation',
  locationChrono: 'localisation-chrono',
  locationCrew: 'localisation-equipage',
  locationSensors: 'localisation-capteurs',
  locationMissions: 'localisation-missions',
  locationCircuits: 'localisation-circuits',
  locationHistory: 'localisation-details-historique',
  locationDetailsCircuit: 'localisation-details-circuit',
  locationAlerts: 'localisation-details-alertes',
  locationEvents: 'localisation-details-evenements',
  locationDetailsChrono: 'localisation-details-chrono',
  locationIdentification: 'localisation-details-identification',
  statsCircuit: 'statistiques-operationnelles-circuit',
  statsOperation: 'statistiques-operationnelles-operation',
}

/**
 * @param {*} code i.g locationDetailsCircuit
 * @returns
 */
function computeFilenameFromTableCode(code = 'table-export') {
  let i18nCode = `export_feature.table_prefixes.${code}`
  if (!i18n.te(i18nCode)) {
    console.warn('i18n expected:', i18nCode)
  }
  let prefix = i18n.te(i18nCode)
    ? i18n.t(i18nCode)
    : tablesPrefixes[code] || code
  return `${prefix}-${moment().format('DDMMYYYY[-]HHmm')}`
}

/**
 *
 * @param {Array} options.data arrayOfObjects
 * @param {String} options.tableCode i.g locationIdentification
 * @param {String} options.format csv/xlsx (default to xlsx)
 * @param {Array} options.columns
 * @returns
 */
export function exportDatatable(options = {}) {
  options.format = options.format || 'xlsx'
  if (options.format === 'xlsx' || options.format === 'xls') {
    return exportToXLSX(
      options.data || [],
      computeFilenameFromTableCode(options.tableCode),
      {
        columns: options.columns,
      }
    )
  }
  if (options.format === 'csv') {
    return exportToCSV(
      options.data || [],
      computeFilenameFromTableCode(options.tableCode),
      {
        columns: options.columns,
      }
    )
  }
  throw new Error('INVALID_EXPORT_FORMAT')
}

/**
 * @example
 * exportToXLSX([{name:"FOO",size:"200"},{name:"BAR",size:"300"}],"test")
 * exportToXLSX([{ name: "FOO", size: "200" },{ name: "BAR", size: "300" }],"test",{columns: {name:"NOMBRE",size:"TAMAÑO"},});
 * exportToXLSX([{ name: "FOO", size: "200" },{ name: "BAR", size: "300" }],"test",{columns: ["NAME", "SIZE"]});
 * @param {Array} columns
 * @param {Array} data
 * @param {*} name i.g test
 */
export async function exportToXLSX(
  arrayOfObjects = [],
  name = 'download',
  options = {}
) {
  let columns = []
  let data = []
  let columnKeys = []
  arrayOfObjects.forEach((item, index) => {
    if (index === 0) {
      columnKeys = Object.keys(item)
      columns =
        options.columns === undefined
          ? Object.keys(item)
          : options.columns instanceof Array
          ? options.columns
          : Object.keys(options.columns).map((key) => options.columns[key])
    }
    data.push(columnKeys.map((key) => item[key]))
  })
  // Build rows for write-excel-file
  const titleRow = [
    {
      value: name,
      span: columnKeys.length,
      fontWeight: 'bold',
      align: 'center',
      fontSize: 11,
    },
  ]

  const headerRow = columns.map((col) => ({
    value: col,
    fontWeight: 'bold',
    align: 'center',
    fontSize: 11,
  }))

  const dataRows = data.map((row) =>
    row.map((value) => ({ value, fontSize: 11 }))
  )

  const fullData = [titleRow, headerRow, ...dataRows]

  // Step 3: Calculate dynamic widths for each column
  const allRows = [columns, ...data] // Use headers + data

  const widths = computeColumnWidths(columnKeys, allRows)

  // Create full column config
  const columnConfig = widths.map((width) => ({
    width,
  }))

  // Step 4: Export the XLSX
  await writeXlsxFile(fullData, {
    fileName: `${name.replace(/\.xlsx$/i, '')}.xlsx`,
    sheet: 'Sheet1',
    columns: columnConfig,
  })
}

/**
 * @example
 * exportToCSV([{name:"FOO",size:"200"},{name:"BAR",size:"300"}],"test")
 * exportToCSV([{name:"FOO",size:"200"},{name:"BAR",size:"300"}],"test",{columns:['NAME','SIZE']})
 * @param {*} arrayOfObjects
 * @param {*} filename
 * @returns
 */
export function exportToCSV(
  arrayOfObjects = [],
  filename = 'download',
  options = {}
) {
  let delimiterCharacter = options.delimiterCharacter || ','
  var objectToCSVRow = function (dataObject) {
    var dataArray = new Array()
    for (var o in dataObject) {
      var value = dataObject[o]
      var innerValue =
        value === null || value === undefined ? '' : value.toString()
      var result = innerValue.replace(/"/g, '""')
      result = '"' + result + '"'
      result = result.split('&nbsp;').join(' ')
      dataArray.push(result)
    }
    return dataArray.join(delimiterCharacter) + '\r\n'
  }
  if (!arrayOfObjects.length) {
    return
  }

  let columns =
    options.columns === undefined
      ? Object.keys(arrayOfObjects[0])
      : options.columns instanceof Array
      ? options.columns
      : Object.keys(options.columns).map((key) => options.columns[key])

  var csvContent = 'data:text/csv;charset=utf-8,'
  csvContent += objectToCSVRow(columns)
  arrayOfObjects.forEach(function (item) {
    csvContent += objectToCSVRow(item)
  })

  //Add extra lines if option addExtraLinesAfterRows is true
  if (options.addExtraLinesAfterRows === true && options.extraLines) {
    if (Array.isArray(options.extraLines) && options.extraLines.length > 0) {
      //Add blank line before extra lines
      csvContent += '\r\n'

      options.extraLines.forEach(function (extraLine) {
        csvContent += objectToCSVRow(extraLine)
      })
    }
  }

  if (!isUnitTest()) {
    var encodedUri = encodeURI(csvContent)
    var link = document.createElement('a')
    link.setAttribute('href', encodedUri)
    link.setAttribute('download', filename.split('.csv').join('.csv') + '.csv')
    document.body.appendChild(link) // Required for FF
    link.click()
    document.body.removeChild(link)
  }
  return csvContent
}

export default {
  exportToCSV,
  exportDatatable,
}
