import { leafletBoundsToPolygonPoints } from '@/utils/map.js'
import api from '@/api/index.js'
import { getEnvIntValue } from '@/services/env-service'
import { getEnvValue } from '@/services/env-service.js'
import { computeStyleGlobalVariable } from '@/utils/styles.js'

export const itemsPerPage = getEnvIntValue('containersModuleItemsPerPage', 1000)

/**
 * On clients like Rabat Ville verte, the cuveNumber value is invalid in the consolidated version. If we disable the bin ignore consolidated, when the user filters by cuveNumber, the API will return the matched record from the reference data, instead.
 *
 * Consider turning this flag to 0 by default once the consolidation values are OK for most clients.
 */
export const disableBinIgnoreConsolidated =
  getEnvValue('containersModuledisableBinIgnoreConsolidated', '0') === '1'

export const maxAllowedItems = getEnvIntValue(
  'containersModuleMaxAllowedItems',
  10000
)

export const addressSearchRadius = getEnvIntValue(
  'containersModuleAddressSearchRadius',
  100
)
export interface CMItem {
  id: number
  name: String
  lat: number
  lng: number
  zoneLabel: string
}

export async function getContainerFluxItems() {
  return await api.containerFlow.getAll()
}

export async function getContainerTypeItems() {
  return await api.binTypes.getAll()
}

/**
 * Color based on type
 * Fallbacks to default color
 *
 * @param stateId
 * @returns
 */
export function getColorFromState(stateId = -1) {
  switch (stateId) {
    case 1:
      return 'var(--color-container-state-collected)'
    case 2:
      return 'var(--color-container-state-no-planned)'
    case 3:
      return 'var(--color-container-state-planned)'
    case 4:
      return 'var(--color-container-state-no-collected)'
    default:
      return 'var(--color-container-state-no-planned)'
  }
}

export function getColorFromCollectedAtKey(key: string) {
  const colors = {
    lessThan24h: '#66cc33',
    between24hAnd48h: '#ff9900',
    between48hAnd72h: '#ff3333',
    moreThan72h: '#6b7280',
    neverCollected: '#2a303d',
  }

  return colors[key] ?? colors.neverCollected
}

/**
 * Key based on type
 *
 * @param {number} stateId
 * @returns string
 */
export function getKeyFromState(stateId = -1) {
  switch (stateId) {
    case 1:
      return 'collected'
    case 2:
      return 'notCollectedNotPlaned'
    case 3:
      return 'toBeCollected'
    case 4:
      return 'notCollected'
    default:
      return 'notCollectedNotPlaned'
  }
}

/**
 *
 * @param {Array} bounds Leaflet bounds as array: [[lat,lng], [lat,lng]]
 * @returns {String} normalized polygon as string (APIV3 filter contains[point])
 */
export function getCurrentStatesNormalizedPolygonFromLeafletBounds(bounds) {
  let polygonData = leafletBoundsToPolygonPoints(bounds)
  return getCurrentStatesNormalizedPolygonFromLeafletPolygonData(polygonData)
}

/**
 *
 * @param {Array} polygonData Leaflet polygon data as array: [[lat,lng], [lat,lng]]
 * @returns {String} normalized polygon as string (APIV3 filter contains[point])
 */
export function getCurrentStatesNormalizedPolygonFromLeafletPolygonData(
  polygonData
) {
  if (polygonData.length === 0) {
    return ''
  }

  if (polygonData[0].lat) {
    polygonData = polygonData.map((latLng) => [latLng.lat, latLng.lng])
  }

  const closedPolygon =
    polygonData[0][0] === polygonData[polygonData.length - 1][0] &&
    polygonData[0][1] === polygonData[polygonData.length - 1][1]
      ? [...polygonData]
      : [...polygonData, polygonData[0].slice()]

  return `(${closedPolygon
    .map((value) => {
      value = value.lat ? [value.lat, value.lng] : value
      return `${value[1].toFixed(4)} ${value[0].toFixed(4)}` //lng lat
    }, [])
    .join(',')})` // => (lng lat, lng lat)
}

/**
 * Used by settings to render select options
 */
export function getAutoRefreshIntervalTimes(): number[] {
  const minRefreshIntervalTime = getEnvIntValue(
    'containersModuleMinAutoRefreshIntervalTimeMinutes',
    '15'
  )

  const intervalTimesArrayFromEnv: any = getEnvValue(
    'containersModuleAutoRefreshIntervalTimeMinutes',
    ''
  )
    .split(',')
    .filter((str) => !!str.trim())
    .map((str) => parseInt(str.trim())) //5,10,15,20,25,30
  const intervalTimeEachMinutes = getEnvIntValue(
    'containersModuleMinAutointervalTimeEachMinutes',
    5
  )
  const intervalTimeEachMinutesTimes: number = parseInt(
    getEnvIntValue('containersModuleMinAutointervalTimeEachMinutesCount', 6)
  )

  const hasFixedIntervalTimes = intervalTimesArrayFromEnv.length > 0

  let intervalTimesComputedArray: any = hasFixedIntervalTimes
    ? intervalTimesArrayFromEnv
    : []
  if (intervalTimesComputedArray.length === 0) {
    let curr = minRefreshIntervalTime
    for (let x = 0; x < intervalTimeEachMinutesTimes; x++) {
      intervalTimesComputedArray.push(curr)
      curr += intervalTimeEachMinutes
    }
  }

  return intervalTimesComputedArray
}

export interface getDataPoolingOptions {
  /**
   * Indicate a polygon in APIV3 format: ?contains[point]=(lng lat, lng lat, lng lat)
   * Example: (lng lat, lng lat, lng lat)
   */
  containsPolygon?: string
  containerFluxIds?: number[]
  addressLatLng?: number[]
  puceNumber?: string[]
  cuveNumber?: string
  containerTypeIds: number[]
  containerStateIds?: number[]
  binId: number[]
  /**
   * Used when searching by Zone - Circle
   * @property {number} nearPointRadius - Radius in meters
   */
  nearPointRadius?: number
  lang: string
  /**
   * Logging only
   */
  containsPolygonType?: String
  itemsPerPage: number
  maxAllowedItems?: number
  /**
   * See "searchCancelManager"
   */
  searchItem?: any

  binIgnoreConsolidated?: boolean
}

interface filterContainersResult {
  newItems: CMItem[]
  totalCount: number
}

/**
 * Asynchronously fetches a paginated list of containers from the server based on provided filter options.
 *
 * @param {Object} getDataPoolingOptions - An object containing various filtering criteria.
 * @param {Array} getDataPoolingOptions.containerFluxIds - An array of container flux ids.
 * @param {Array} [getDataPoolingOptions.addressLatLng=[]] - An array containing the latitude and longitude of the address.
 * @param {string} [getDataPoolingOptions.containsPolygonType='mapviewport'] - The type of polygon to check if a container is within.
 * @param {string} [getDataPoolingOptions.containsPolygon=''] - The polygon to check if a container is within.
 * @param {number} [getDataPoolingOptions.nearPointRadius=0] - The radius around a point to check if a container is near.
 * @returns {Promise} A promise resolving to an object indicating if all items are loaded, and containing the new items array.
 */
export async function filterContainersStates(
  {
    containerFluxIds,
    addressLatLng,
    containsPolygonType,
    containsPolygon,
    nearPointRadius,
    puceNumber,
    cuveNumber,
    containerTypeIds,
    lang,
    itemsPerPage,
    maxAllowedItems,
    searchItem,
    containerStateIds,
  }: getDataPoolingOptions = {
    addressLatLng: [],
    containsPolygon: '',
    nearPointRadius: 0,
    containerFluxIds: [],
    puceNumber: [],
    cuveNumber: '',
    containerTypeIds: [],
    binId: [],
    lang: 'fr',
    containsPolygonType: 'mapviewport',
    itemsPerPage: 1000,
    containerStateIds: [],
  }
): Promise<filterContainersResult> {
  console.log('search item from states: ', searchItem)

  // @ts-expect-error
  const track = console.trackTime('filterData (current states)')
  let itemsPerPageLocal = Math.min(itemsPerPage || 0, maxAllowedItems)

  const skipPuceSansBacData =
    getEnvValue('containersModuleCurrentStatesAPIParamExistsBinIdTrue', '1') ===
    '1'

  let payload: {
    page?: number
    itemsPerPage: number
    properties: Array<string>
    fluxId?: number[]
    chipNumber?: string[]
    tankNumber?: string
    lang: string
    [key: string]: string | number | Array<string> | number[] | Boolean
  } = {
    itemsPerPage: itemsPerPage,
    properties: [
      'id',
      'latitude',
      'longitude',
      'stateId',
      'typeId',
      'binId',
      'collectedAt',
      'streetNumber',
      'street',
      'zipCode',
      'city',
      'country',
      'chipNumber',
      'vehicleName',
      'referenceRoundName',
    ],
    lang,
  }

  if (skipPuceSansBacData) {
    payload['exists[binId]'] = true
  }

  if (containsPolygon && addressLatLng.length === 0) {
    payload['contains%5Bpoint%5D'] = containsPolygon
    track.count(`containsPolygonType: ${containsPolygonType}`)
  }

  if (!payload['contains%5Bpoint%5D']) {
    console.warn(
      'Filtering without coords is not recommended (containsPolygon)'
    )
  }

  if (containerFluxIds.length > 0) {
    payload.fluxId = containerFluxIds
  }

  if (containerTypeIds.length > 0) {
    payload.typeId = containerTypeIds
  }

  if (containerStateIds.length > 0) {
    payload.binState = containerStateIds
  }

  if (addressLatLng.length > 0) {
    payload['nearPoint[point][longitude]'] = addressLatLng[1]
    payload['nearPoint[point][latitude]'] = addressLatLng[0]
    payload['nearPoint[point][radius]'] =
      nearPointRadius > 0 ? nearPointRadius : addressSearchRadius
  }

  const filteredPuceNumber = puceNumber.filter(
    (pn) => typeof pn === 'string' && pn.trim().length > 0
  ) // Remove empty strings (case of form with no puceNumber specified)
  if (filteredPuceNumber.length > 0) {
    payload['chipNumber'] = filteredPuceNumber // Send only non-empty strings
  }

  if (cuveNumber) {
    payload.tankNumber = cuveNumber
  }

  let totalCount = 0
  let maxAllowedItemsError = false
  const newItems: CMItem[] = await api.binCollectionCurrentStates.getAllPooling(
    {
      payload,
      /**
       *
       * @param partial partial results
       * @param res APIV3 response (data property)
       * @returns
       */
      callback(partial, res) {
        if (!searchItem.shouldContinue()) {
          return false
        }
        if (res.totalItems > maxAllowedItems) {
          searchItem.cancel()
          maxAllowedItemsError = true
          return false
        }
        totalCount = res.totalItems
        console.log('containers state items pooling partial', partial.length)
      },
    }
  )
  track.count('info', {
    newItems,
    totalCount,
  })
  track.stop()
  if (maxAllowedItemsError) {
    throw new Error('TOO_MANY_ELEMENTS')
  }
  return { newItems, totalCount }
}

/**
 * Update containers bins
 *
 * @param {Object} vuexContext
 * @param {Object} payload
 * @param {string} payload.containsPolygon
 * @param {Array} [payload.addressLatLng]
 * @param {Number} [payload.nearPointRadius]
 * @param {Array} [payload.containerFluxIds] number[] Flux Ids to filter by
 * @param {Array} [payload.containerTypeIds] number[] Type ids to filter by
 * @param {Array} [payload.puceNumber]
 * @param {String|Number} [payload.cuveNumber]
 * @param{Array} [payload.binId]
 * @returns {void}
 */
export async function filterContainersBins(
  {
    containsPolygon,
    addressLatLng,
    nearPointRadius,
    containerFluxIds,
    containerTypeIds,
    puceNumber,
    cuveNumber,
    binId,
    itemsPerPage,
    searchItem,
    binIgnoreConsolidated,
    containerStateIds,
  }: getDataPoolingOptions = {
    addressLatLng: [],
    containsPolygon: '',
    nearPointRadius: 0,
    containerFluxIds: [],
    puceNumber: [],
    cuveNumber: '',
    containerTypeIds: [],
    binId: [],
    lang: 'fr',
    containsPolygonType: 'mapviewport',
    itemsPerPage: 1000,
    binIgnoreConsolidated: true,
    containerStateIds: [],
  }
) {
  // @ts-expect-error
  const track = console.trackTime('filterContainersBins')
  let newItems = []
  let totalCount = 0
  let maxAllowedItemsError = false
  try {
    let payload: any = {
      itemsPerPage: itemsPerPage,
    }

    if (!!cuveNumber && disableBinIgnoreConsolidated) {
      binIgnoreConsolidated = false
    }

    if (binIgnoreConsolidated !== false) {
      payload['exists[consolidatedAt]'] = false
    }

    if (containsPolygon && (addressLatLng || []).length === 0) {
      payload['contains[location]'] = containsPolygon
    }
    if (addressLatLng.length > 0) {
      payload['nearPoint[location][longitude]'] = addressLatLng[1]
      payload['nearPoint[location][latitude]'] = addressLatLng[0]
      payload['nearPoint[location][radius]'] =
        nearPointRadius > 0 ? nearPointRadius : addressSearchRadius
    }

    if ((containerFluxIds || []).length > 0) {
      payload['fluxMaterial.id'] = containerFluxIds
    }

    if (containerTypeIds.length > 0) {
      payload['binMake.binType.id'] = containerTypeIds
    }

    if (containerStateIds.length > 0) {
      payload['state'] = containerStateIds
    }

    const filteredPuceNumber = puceNumber.filter(
      (pn) => typeof pn === 'string' && pn.trim().length > 0
    ) // Remove empty strings (case of form with no puceNumber specified)
    if (filteredPuceNumber.length > 0) {
      payload['chipNumber'] = filteredPuceNumber // Send only non-empty strings
    }

    if (binId.length > 0) {
      payload['id'] = binId
    }

    if (cuveNumber) {
      payload.tankNumber = cuveNumber
    }

    let newItemsRes = await api.bins.getAllPooling({
      payload,
      callback(partial, res) {
        if (!searchItem.shouldContinue()) {
          return false
        }
        if (res.totalItems > maxAllowedItems) {
          searchItem.remove()
          maxAllowedItemsError = true
          return false
        }
        totalCount = res.totalItems
        console.log('containers bins items pooling partial', partial.length)
      },
    })
    newItems = newItemsRes
    track.count('final', {
      newItems,
      totalCount,
    })
  } catch (err) {
    console.error('Error while updating containers bins', {
      err,
    })
  } finally {
    track.stop()

    if (maxAllowedItemsError) {
      console.error('TOO_MANY_ELEMENTS')
    } else {
      // eslint-disable-next-line no-unsafe-finally
      return { newItems, totalCount }
    }
  }
}

/**
 * Fetch data from state/bins APIs
 *
 * Features:
 * - Pooling
 * - Cancelable logic
 * - Parallel
 * @param {Object} options getDataPoolingOptions
 * @param binsOnly query currentStates only if necessary
 * @param testFilterContainersStates Optional parameter for unit test purposes (to avoid API calls in this context)
 * @param testFilterContainersBins Optional parameter for unit test purposes (to avoid API calls in this context)
 * @returns
 */
export async function getDataPooling(
  options: getDataPoolingOptions,
  binsOnly,
  testFilterContainersStates?: (payload: any) => Promise<any>,
  testFilterContainersBins?: (payload: any) => Promise<any>
) {
  let stateItems = []
  let stateItemsTotalCount = 0
  let binItems = []
  let binItemsTotalCount = 0
  let binsWithConsolidated = []
  const { searchItem } = options
  let statePoolingPromise = (async () => {
    if (!binsOnly) {
      const statesDataPoolingPromise =
        typeof testFilterContainersStates === 'function'
          ? await testFilterContainersStates(options)
          : await filterContainersStates(options)

      const { newItems, totalCount } = statesDataPoolingPromise

      stateItems = newItems
      stateItemsTotalCount = totalCount
    }
  })()

  let binsPoolingPromise = (async () => {
    const binsDataPoolingPromise =
      typeof testFilterContainersBins === 'function'
        ? await testFilterContainersBins(options)
        : await filterContainersBins(options)

    const { newItems, totalCount } = binsDataPoolingPromise

    binItems = newItems
    binItemsTotalCount = totalCount
    binsWithConsolidated = newItems
  })()

  await Promise.all([statePoolingPromise, binsPoolingPromise])

  const canceled = searchItem.isCanceled
  searchItem.remove()
  return {
    stateItems,
    stateItemsTotalCount,
    binItems,
    binItemsTotalCount,
    canceled,
    binsWithConsolidated,
  }
}
