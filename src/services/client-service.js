import api from '@/api'
import { osmWmsUrl, wmsOptionsDefaults } from '@/services/map-service.js'
import { getClientParameter } from '@/services/auth-service.js'
import APIUrls from '@/config/simpliciti-apis'
import envService from './env-service'
import basemapOsmImg from '@c/shared/SimplicitiMap/assets/basemap_osm.jpg'
import basemapDefaultImg from '@c/shared/SimplicitiMap/assets/basemap_default.jpg'
import basemapSimpliciti1Img from '@c/shared/SimplicitiMap/assets/basemap_simpliciti1.jpg'
import basemapSimpliciti2Img from '@c/shared/SimplicitiMap/assets/basemap_simpliciti2.jpg'
import basemapGoogleMapsTransitImg from '@c/shared/SimplicitiMap/assets/basemap_googlemaps_transit.jpg'
import basemapGoogleMapsTrafficImg from '@c/shared/SimplicitiMap/assets/basemap_googlemaps_traffic.jpg'
import basemapGoogleMapsSatelliteImg from '@c/shared/SimplicitiMap/assets/basemap_googlemaps_satellite.jpg'
import basemapGoogleMapsHybridImg from '@c/shared/SimplicitiMap/assets/basemap_googlemaps_hybrid.jpg'
import basemapGoogleMapsRoadmapImg from '@c/shared/SimplicitiMap/assets/basemap_googlemaps_roadmap.jpg'
import basemapGoogleMapsTerrainImg from '@c/shared/SimplicitiMap/assets/basemap_googlemaps_terrain.jpg'

export const availableBaseMapImages = {
  osm: basemapOsmImg,
  default: basemapDefaultImg,
  simpliciti1: basemapSimpliciti1Img,
  simpliciti2: basemapSimpliciti2Img,
  googlemaps_transit: basemapGoogleMapsTransitImg,
  googlemaps_traffic: basemapGoogleMapsTrafficImg,
  googlemaps_satellite: basemapGoogleMapsSatelliteImg,
  googlemaps_hybrid: basemapGoogleMapsHybridImg,
  googlemaps_roadmap: basemapGoogleMapsRoadmapImg,
  googlemaps_terrain: basemapGoogleMapsTerrainImg,
}

const filterBasemapsUsingClientParameters = false //Toogle off once API respond with configured client basemaps

/**
 * @todo Normalize response
 * @returns
 */
export async function getClientParameters() {
  return await api.v3
    .get(`${APIUrls.APIV3_CLIENT_PARAMETERS}`)
    .then((response) => {
      return response?.data || []
    })
}

if (envService.isStagging()) {
  window.getClientParameters = getClientParameters
}

export async function getClientDefaultLocationTab(clientId) {
  if (!clientId) {
    console.warn('getClientDefaultLocationTab::clientId is required')
    return 'automatic'
  }

  return (await getClientParameter('defaultLocationTab')) || 'automatic'
}

export async function getClientBasemapsItems(clientId, isSabadmin = false) {
  if (!clientId) {
    console.warn('getClientBasemapsItems::clientId is required')
    return []
  }

  let track = console.trackTime('getClientBasemapsItems', false)

  /*const configuredEngineId = !filterBasemapsUsingClientParameters
    ? null
    : (await getClientParameter('MoteurCarto')) || ''*/
  const configuredBasemapsIds = !filterBasemapsUsingClientParameters
    ? null
    : ((await getClientParameter('FondDeCarte')) || '')
        .trim()
        .split(',')
        .map((v) => parseInt(v.trim()))

  track.count('Configured engine and basemaps', {
    //configuredEngineId,
    configuredBasemapsIds,
  })

  let normalizedItems

  const useServiceAPI = true

  if (useServiceAPI) {
    let rawServiceCartoResponse = await api.serviceCartography.getAll({
      clientId,
    })

    track.count('res-from-server', {
      rawServiceCartoResponse,
    })

    const computeWmsUrl = (cartographyUrl) => {
      if (cartographyUrl === 'http://OSM') {
        console.warn('Invalid OSM cartographyUrl', {
          cartographyUrl,
        })
        return ''
      }
      return cartographyUrl.split('?')[0]
    }

    /**
     * Stores crs as string to support local cache
     * @param {*} cartoEngineItem
     * @param {*} baseMapItem
     * @returns
     */
    const computeWmsCrs = (cartoEngineItem, baseMapItem) => {
      if ((baseMapItem?.projectionMap || '').includes('900913')) {
        return 'L.CRS.EPSG900913'
      }
      if ((baseMapItem?.projectionMap || '').includes('4326')) {
        return 'L.CRS.EPSG4326'
      }
      //EPSG:2154 --> Not suported by Leaflet (https://github.com/IGNF/geoportal-extensions/blob/develop/doc/README-leaflet.md#affichage-en-lambert-93-epsg2154)
      return null //Leaflet defaults to 3857
      /*
      Isoprod fonddecarte projections:
      	1
new OpenLayers.Projection('EPSG:2154')	1
new OpenLayers.Projection('EPSG:3857')	16
new OpenLayers.Projection('EPSG:4326')	18
new OpenLayers.Projection('EPSG:900913')	48
*/
    }

    function parseQueryString(url) {
      let queryString = url.split('?')[1]
      if (!queryString) {
        return {}
      }
      let queryParams = queryString.split('&')
      let queryParamsObject = {}
      for (let i = 0; i < queryParams.length; i++) {
        let param = queryParams[i].split('=')
        queryParamsObject[param[0]] = param[1]
      }
      return queryParamsObject
    }

    normalizedItems = (rawServiceCartoResponse || [])
      /*.filter(
        (cartoEngineItem) =>
          isSabadmin ||
          !filterBasemapsUsingClientParameters ||
          cartoEngineItem.idCartographyEngine.toString() ===
            configuredEngineId.toString()
      )*/
      .map((cartoEngineItem) => {
        if (!cartoEngineItem.baseMaps.filter) {
          console.error(
            'Invalid API response (baseMaps expected to be an array)',
            {
              cartoEngineItem,
            }
          )
          return []
        }

        let arr = cartoEngineItem.baseMaps
          .filter(
            (baseMapItem) =>
              isSabadmin ||
              !filterBasemapsUsingClientParameters ||
              configuredBasemapsIds.includes(baseMapItem.idBaseMap)
          )
          .map((baseMapItem) => {
            let wmsUrl = computeWmsUrl(cartoEngineItem.cartographyUrl)
            let queryParameters = parseQueryString(
              cartoEngineItem.cartographyUrl
            )

            let normalizeOptions = {
              wmsId: cartoEngineItem.idCartographyEngine,
              wmsName: cartoEngineItem.name,
              wmsUrl,
              wmsUrlParams: {
                ...queryParameters,
                crs: computeWmsCrs(cartoEngineItem, baseMapItem),
              },
            }

            track.count(`normalizing basemap ${cartoEngineItem.name}`, {
              baseMapItem,
              normalizeOptions,
            })

            return normalizeBaseMapItem(baseMapItem, normalizeOptions)
          })
        track.count(`map basemaps from ${cartoEngineItem.name}`, {
          arr,
        })
        return arr
      })
      .reduce((arr, baseMapItems) => {
        return [...arr, ...baseMapItems]
      }, [])
    track()
  } else {
    //This is deprecated
    let res = await api.v3.get(
      api.APIUrls.APIV3_CLIENT_CARTOGRAPHY.split('${clientId}').join(clientId)
    )
    normalizedItems = (res?.data?.baseMaps || []).map((item) => {
      return normalizeBaseMapItem(item, {
        wmsUrl: res.data.cartographyUrl,
      })
    })
  }

  /*console.log('getClientBasemapsItems::return', {
    normalizedItems,
  })*/
  return normalizedItems

  function normalizeLogoName(logoName) {
    logoName = logoName === 'TEST' ? 'default' : logoName
    return logoName || 'default'
  }
  function normalizeBaseMapName(name) {
    if (name.includes('cBaseLayerSabatier')) {
      name =
        name === 'cBaseLayerSabatier'
          ? 'Simpliciti 1'
          : name.split('cBaseLayerSabatier').join('Simpliciti ')
    }
    return name
  }

  function normalizeBaseMapItem(item, options = {}) {
    const code = item.name
    let name = normalizeBaseMapName(item.name)
    let logo = normalizeLogoName(item.logo)
    //Before using default logo, try to match by name: "Simpliciti 1" => "simpliciti1"
    if (
      logo === 'default' &&
      !!availableBaseMapImages[name.trim().split(' ').join('').toLowerCase()]
    ) {
      logo = name.trim().split(' ').join('').toLowerCase()
    }

    let scope = {
      id: item.idBaseMap,
      code,
      name,
      logo,
      ...options,
      wmsOptions: {
        //...wmsOptionsDefaults,//attribution, layers, format, transparent, styles, language
        ...{
          format: item.format || wmsOptionsDefaults.format,
          transparent:
            item.transparent !== undefined
              ? item.transparent
              : wmsOptionsDefaults.transparent,
          styles: item.styles || undefined,
          attribution: item.attribution || '&#169; Simpliciti2',
          maxZoom: 18,
          layers: item.layers || wmsOptionsDefaults.layers,
          //crs: options.crs || null,
        },
        ...(options.wmsUrlParams || {}),
        /*Example: working here config
        layers: 'here-satellite.day',
      format: 'image/png',
      transparent: true,
      version: '1.1.1',
      crs: L.CRS.EPSG900913, // Use the EPSG:900913 CRS
      */
      },
    }
    scope.wmsUrl = options.wmsUrl || osmWmsUrl
    return scope
  }
}
