import { generateShortId } from '@/utils/crypto.js'
import { removeFirstResolved, splitOperation } from '@/utils/promise.js'
import moment from 'moment'
import { getNestedValue } from '~/utils/object'
import { getQueryStringValue } from '@/utils/querystring'
import { getFormattedAddress } from '@/utils/address.js'
import api from '@/api'
import i18n from '@/i18n'
import { getAPIV3Pooling } from '@/api'
import envService, {
  getEnvIntValue,
  getEnvValue,
} from '@/services/env-service.js'
import APIUrls, {
  APIV3RequestDatetimeFormat,
} from '@/config/simpliciti-apis.js'
import searchService, { getRangeTimeSelected } from '@/services/search-service'
import { chunkArray, chunkIds } from '@/utils/array'
import Ajv from 'ajv'
import store from '@/store'
import { adjustDateWithTimezone } from '@/utils/dates'
import { isNumericString } from '@/utils/string'

const ajv = new Ajv()
const fetchEventsSchema = {
  type: 'object',
  properties: {
    elementIds: { type: 'array', minItems: 1 },
    dates: { type: 'array', minItems: 1 },
    elementType: { type: 'string' },
  },
  required: ['elementIds', 'dates', 'elementType'],
}

export async function getEventTypesCategories(onData = () => {}) {
  let types = {
    exploitation: 'operation_message',
    anomalie: 'anomaly',
    statut: 'status',
  }
  return await getAPIV3Pooling({
    uri: `${APIUrls.APIV3_EVENTS_ANOMALIES}?page=1&itemsPerPage=40`,
    callback: onData,
    transform(item) {
      return {
        id: item.id,
        code: item.code,
        label: item.description,
        type: types[item.type] || item.type,
      }
    },
  })
}

export async function fetchEventDetailsById(aggregateId) {
  let url = APIUrls.APIV3_AGGREGATE_EVENTS + '/' + aggregateId
  if (store.getters['location_module/clientCanHaveOrderGeneratedFromEvents']) {
    url += '?group=order'
  }
  return normalizeAPIV3Item((await api.v3.get(url)).data)
}

/**
 * Used by Location module - Events tab
 *
 * @param {*} vehicleId
 * @param {*} dates
 * @param {*} filters
 * @returns
 */
export async function fetchVehicleEvents(
  vehicleId,
  dates = [],
  filters = {},
  clientCanHaveOrderGeneratedFromEvents = false
) {
  console.log('fetchVehicleEvents', filters)

  let filtersValue = []
  if (filters.anomaly) {
    filtersValue.push(0)
  }
  if (filters.eventExploitation) {
    filtersValue.push(1)
  }
  if (filters.status) {
    filtersValue.push(3)
  }

  const payload = {
    vehicleId: vehicleId,
    'multipleDate[between][datetime][start]': dates.map(
      (date) => searchService.getRangeTimeSelected(date).start
    ),
    'multipleDate[between][datetime][end]': dates.map(
      (date) => searchService.getRangeTimeSelected(date).end
    ),
    type: filtersValue,
    group: 'all',
    page: 1,
    itemsPerPage: 9999,
  }

  const result =
    (await api.v3.get(APIUrls.APIV3_AGGREGATE_EVENTS, payload)).data || []

  console.log('fetchVehicleEvents', result)
  return result.map((item) => {
    return normalizeAPIV3Item(item)
  })
}

export async function fetchCitimissionIsAble() {
  console.log('fetchCitimissionIsAble')
  return (await api.v3.get(APIUrls.APIV3_EVENTS_TO_ORDER_CHECK)).data || []
}

/**
 * Used by events module.
 * - Data used for: Map markers and table
 * - Split strategy: Dynamic based on envs (max items per req, max dates per req, max parallel req)
 * @param {Array} options.elementIds Vehicle or circuit ids [1,2,3]
 * @param {Array} options.dates Dates ['2022-04-29'] or [Moment]
 * @param {Object} options.filters Custom querystring parameters
 * @param {String} options.elementType Helps build the uri (vehicle/circuit)
 * @param {Function} [options.handleResults] Optional (results)=>({}) Each time results are available
 * @param {Object} [options.fetchOptions] Optional Give options to axios
 */
export async function fetchEvents({
  elementIds,
  dates,
  filters,
  elementType,
  handleResults = null,
  fetchOptions = {},
} = {}) {
  const validate = ajv.compile(fetchEventsSchema)

  if (
    !validate({
      elementIds,
      dates,
      elementType,
    })
  ) {
    const errors = validate.errors
    const err = new Error('Invalid function parameters (ajv)')
    err.ajv = errors
    throw err
  }

  const elementParamName = {
    vehicle: 'vehicleId',
    circuit: 'roundId',
  }
  const maxParallelReq = getEnvIntValue('eventsMaxParallelReq', 20)
  const maxItemsPerReq = getEnvIntValue('eventsDetailsMaxItemsPerReq', 10)
  const maxDatesPerReq = getEnvIntValue('eventsDetailsMaxDatesPerReq', 1)
  const itemsChunks = chunkArray(elementIds, maxItemsPerReq)
  const datesChunks = chunkArray(dates, maxDatesPerReq)
  const result = []
  let parallelPromises = []
  let aborted = false
  const track = console.trackTime('events fetch details')
  let counter = 0
  const performSingleRequest = async (vehicleIds, dates) => {
    let data = {
      [elementParamName[elementType]]: vehicleIds,
      'multipleDate[between][datetime][start]': dates.map(
        (date) => searchService.getRangeTimeSelected(date).start
      ),
      'multipleDate[between][datetime][end]': dates.map(
        (date) => searchService.getRangeTimeSelected(date).end
      ),
      //other filters goes here
      ...(filters || {}),
      //Custom service doesn't support hal+json but does have pagination for perf reasons (We bring everything at once given that we already split requests)
      page: 1,
      itemsPerPage: 9999,
    }
    if (
      store.getters['location_module/clientCanHaveOrderGeneratedFromEvents']
    ) {
      if (typeof data.group == 'undefined' || data.group != 'all') {
        data.group = 'order'
      }
    }
    let res = await api.v3.get(
      APIUrls.APIV3_AGGREGATE_EVENTS,
      data,
      fetchOptions
    )
    res = (res.data || []).map(normalizeAPIV3Item)

    counter++
    track.count('performSingleRequest_' + counter, {
      vehicleIds,
      dates,
      res,
    })

    res.forEach((r) => track.count('resultsCount'))

    result.push(...res)
    if (handleResults) {
      aborted = handleResults(res || []) === false
    }
    return res
  }

  track.count('params', {
    maxDatesPerReq,
    maxItemsPerReq,
    maxParallelReq,
    dates,
    elementIds,
    itemsChunks,
    datesChunks,
  })
  for (let currItemsIds of itemsChunks) {
    for (let currDates of datesChunks) {
      if (aborted) {
        break
      }
      if (parallelPromises.length > maxParallelReq) {
        parallelPromises = await removeFirstResolved(parallelPromises)
        track.count('promise_resolved')
      }
      track.count('request')
      parallelPromises.push(performSingleRequest(currItemsIds, currDates))
    }
  }
  track.count(aborted ? 'aborted' : 'resolved')
  if (aborted) {
    track.stop()
    return []
  } else {
    await Promise.all(parallelPromises)
    parallelPromises.forEach((p) => track.count('promise_resolved'))
    track.stop()
    return result
  }
}

fetchEvents.schema = fetchEventsSchema

/**
 * @todo Immatriculation
 * @param {Object} item Object to transform
 * @returns
 */
export function normalizeAPIV3Item(item = {}) {
  let photos = (item.files || []).map((file) => file.url)

  let datetimeWithTimezone = adjustDateWithTimezone(item.datetime)

  return {
    id: item.id,
    lat: item.latitude,
    lng: item.longitude,
    ...(item.datetime
      ? {
          date: item.datetime,
          timestamp: moment(item.datetime)._d.getTime(),
          formattedDate: datetimeWithTimezone.format('DD-MM-YYYY'),
          formattedTime: datetimeWithTimezone.format('HH:mm:ss'),
        }
      : {}),
    immat: '',
    address: getFormattedAddress(item.mapMatch || {}, {
      city: 'nocity',
    }),
    city: getNestedValue(item, 'mapMatch.city'),
    fullAddress: getFormattedAddress(item.mapMatch || {}, {}),
    comment: item.comment,
    label:
      item.anomalyLabel !== ''
        ? item.anomalyLabel
        : store.getters['events/getAnomalyLabelByCode'](item),
    photos,
    hasPhotos:
      getNestedValue(item, ['totalFiles', 'filesNumber'], photos.length) > 0,
    type: 'event_marker',
    color: store.state.events.colors[item.type] || 'red',
    vehicleName: item.vehicleName,
    vehicleId: item.vehicleId,
    isAPIV3Item: true,
    circuitName: item.roundName || '',
    puceNumber: getNestedValue(item, 'binCollection.chipNumber'),
    binCollectionSide: getNestedValue(item, 'binCollection.side'),
    binCollectionSideLabel: getNestedValue(item, 'binCollection.side', '', {
      allowZero: true,
      transform(sideNumber, originalValue, rootValue) {
        return (
          {
            0: i18n.t('events.table_column_side_left'), //gauche
            1: i18n.t('events.table_column_side_right'), //droite
            2: i18n.t('events.table_column_side_combine'), //combiné
            3: i18n.t('events.table_column_side_bicomp'), //bicomp
            5: i18n.t('events.table_column_side_arm'), //bras
            9: i18n.t('events.table_column_side_pedestrian'), //pieton
          }[sideNumber.toString()] || ''
        )
      },
    }),
    orderStateName: item.orderStateName,
    orderNumber: item.orderNumber,
    // Boolean value that indicates if work has been done for the anomaly
    isWorkDone: item.hasOwnProperty('nud4')
      ? getWorkDoneLabel(item.nud4)
      : i18n.t('common.no'),
  }
}

export function normalizeAPIV2Array(data) {
  let normalizedData = []

  const colors = {
    0: 'red', //anomaly
    1: 'orange', //Message d’exploitation
    3: 'green', //status
  }

  data.forEach((item) => {
    let streetNum =
      item.evenement_num_rue !== '' && item.evenement_num_rue !== '0'
        ? item.evenement_num_rue + ' '
        : ''
    let streetName =
      item.evenement_adresse !== '' ? item.evenement_adresse + ' ' : ''
    let formatAddress = streetNum + streetName

    normalizedData.push({
      id: item.evenement_id,
      lat: item.evenement_latitude_wgs84,
      lng: item.evenement_longitude_wgs84,
      date: item.evenement_dateheure,
      timestamp: moment(item.evenement_dateheure)._d.getTime(),
      formattedDate: moment(item.evenement_dateheure).format('DD-MM-YYYY'),
      formattedTime: moment(item.evenement_dateheure).format('HH:mm:ss'),
      immat: item.nom_vehicule,
      address: formatAddress,
      city: item.evenement_commune,
      fullAddress: formatAddress + item.evenement_commune,
      comment: item.evenement_tud150,
      label: item.anomalie_libelle,
      photos: item.photos ? (item.photos.photo || []).map((i) => i.url) : [],
      type: item.evenement_type,
      color: colors[item.evenement_type] || 'red',
      vehicleName: item.nom_vehicule,
      vehicleId: item.vehicule_id,
    })
  })
  return normalizedData
}

function getLabelByType(type) {
  return (
    {
      0: i18n.te('events.types.anomaly')
        ? i18n.t('events.types.anomaly')
        : 'anomaly', //anomaly
      1: i18n.te('events.types.message_exploitation')
        ? i18n.t('events.types.message_exploitation')
        : 'Message d’exploitation', //Message d’exploitation
      3: i18n.te('events.types.status')
        ? i18n.t('events.types.status')
        : 'status', //status
    }[type] ||
    (i18n.te('events.types.unknown')
      ? i18n.t('events.types.unknown')
      : '(No name)')
  )
}

/**
 * Replace JSON mock with hardcoded/auto-generated mock inside the code source (Optimize)
 *
 * Split strategy:
 * - dynamic chunks so that we reach a max parallel req goal
 * - 1 date per req (fixed)
 * - X veh/circ per req (param)
 *
 * @param {*} elementIds
 * @param {*} dates
 * @param {*} options
 * @returns
 */
export async function fetchEventsCumulation(
  elementIds,
  dates,
  { onDataReceived, filters, elementType } = {}
) {
  console.debugVerbose(2, 'event-service::fetchEventsCumulation', {
    elementIds,
    dates,
  })

  const maxParallelRequestsCount = getEnvIntValue(
    'events_search_cumulation_max_parallel_requests',
    15
  )
  const maxItemsPerRequest = getEnvValue(
    'events_search_cumulation_items_size',
    5
  )
  let trackMappingCounter = 0
  const track = console.trackTime('events fetch synthesis')
  const finalRes = []

  const handleSingleOperation = async (ids, dates) => {
    track.count('reqCount')
    let partialResults = await handleSingleSubset(ids, dates)
    if (onDataReceived) {
      aborted = onDataReceived(partialResults, mergeCumulationResults) === false
    }
    return partialResults
  }
  let aborted = false
  let idsChunks = chunkArray(elementIds, maxItemsPerRequest)
  let datesChunks = chunkArray(dates, 1)
  let parallelPromises = []
  track.count('params', {
    idsChunks,
    datesChunks,
    elementIds,
    dates,
    maxItemsPerRequest,
    maxParallelRequestsCount,
  })
  for (let ids of idsChunks) {
    for (let dates of datesChunks) {
      if (aborted) {
        break
      }
      if (parallelPromises.length > maxParallelRequestsCount) {
        parallelPromises = await removeFirstResolved(parallelPromises)
        track.count('promise_resolved')
      }
      parallelPromises.push(handleSingleOperation(ids, dates))
    }
  }
  track.count(aborted ? 'aborted' : 'resolved')
  if (aborted) {
    track.stop()
    return []
  } else {
    await Promise.all(parallelPromises)
    parallelPromises.forEach((p) => track.count('promise_resolved'))
    track.stop()
    return finalRes
  }

  /**
   * Because of split, we need to merge results containing the same date
   * @param {*} array
   * @param {*} newArr
   */
  function mergeCumulationResults(array = [], newArr = []) {
    //Merge by label (e.g 07/11/2023)
    newArr.forEach((item) => {
      let match = array.find(
        (i) =>
          i.label === item.label &&
          i.subItems !== undefined &&
          item.subItems !== undefined
      )
      if (match) {
        match.count += item.count

        //Merge by code (e.g Black liste)
        //match.items = [...match.items, ...item.items]
        item.items.forEach((cumulationItemHeaderSubItem) => {
          let m = match.items.find(
            (i) => i.code === cumulationItemHeaderSubItem.code
          )
          if (m) {
            m.count += cumulationItemHeaderSubItem.count
          } else {
            match.items.push(cumulationItemHeaderSubItem)
          }
        })

        //No merge needed as the req split work by vehicle/circuit
        match.subItems = [...match.subItems, ...item.subItems]
      } else {
        array.push(item)
      }
    })
  }

  function mapResponseItems(root, subItems = true, extendObject = {}) {
    let items = []
    Object.keys(root).forEach((label) => {
      let singleItemRaw = root[label]
      let codesObject = singleItemRaw.types || singleItemRaw.codes || []
      let singleItem = {
        //id: singleItemRaw.id || label,
        id: singleItemRaw.id || generateShortId('id-'),
        label: label,
        count: singleItemRaw.total,
        //Recap by anomaly type (header)
        items: Object.keys(codesObject).map((eventGroupKey) => {
          let eventGroup = codesObject[eventGroupKey]
          return {
            id: eventGroup.code,
            code: eventGroup.code,
            //Hack to get label from CitiPAV statuses, where labels are numeric strings
            label: isNumericString(eventGroup.label)
              ? store.getters['events/getAnomalyLabelByCode'](eventGroup)
              : eventGroup.label,
            count: eventGroup.count,
            type: eventGroup.type,
            typeLabel: getLabelByType(eventGroup.type),
          }
        }),
      }
      if (subItems) {
        singleItem.date = singleItem.label //YYYY/MM/DD
        singleItem.label = moment(singleItem.label, 'YYYY/MM/DD').format(
          `DD/MM/YYYY`
        )
        //Vehicle / Circuits collapsibles
        singleItem.subItems = mapResponseItems(
          singleItemRaw.vehicles || singleItemRaw.rounds || [],
          false,
          {
            date: singleItem.date,
          }
        )
        singleItem.timestamp = moment(singleItem.label)._d.getTime()

        trackMappingCounter++
        track.count('mapResponseItemsItem_' + trackMappingCounter, {
          label,
          data: root[label],
          normalized: singleItem,
        })
      }
      Object.assign(singleItem, extendObject)
      items.push(singleItem)
    })
    return items
  }

  async function handleSingleSubset(elementIds, dates) {
    let res =
      getQueryStringValue('mockapi') === '1'
        ? []
        : (
            await api.v3.get(APIUrls.APIV3_EVENTS_CUMULATION, {
              format: {
                vehicle: 'vehicle',
                circuit: 'round',
              }[elementType || 'vehicle'],
              page: 1,
              itemsPerPage: 30,

              //vehicule_id/circuit_id
              [{
                vehicle: 'vehicleId',
                circuit: 'roundId',
              }[elementType || 'vehicle']]: elementIds,
              'multipleDate[between][datetime][start]': dates.map(
                (date) => searchService.getRangeTimeSelected(date).start
              ),
              'multipleDate[between][datetime][end]': dates.map(
                (date) => searchService.getRangeTimeSelected(date).end
              ),
              ...(filters || {}),
            })
          ).data
    return mapResponseItems(res)
  }
}

export function getEventsSearchSelectionLimit(defaultValue = 100) {
  return parseFloat(
    getEnvValue('eventsModuleSearchSelectionLimit') ||
      getEnvValue('searchSelectionLimit', defaultValue)
  )
}

/**
 * Returns a common yes/no label depending on the boolean value passed as param
 * Fallbacks to no
 * @param value
 * @returns {*|string|VueI18n.LocaleMessages}
 */
export function getWorkDoneLabel(value) {
  return (
    {
      0: i18n.t('common.no'),
      1: i18n.t('common.yes'),
    }[value] || i18n.t('common.no')
  )
}

export default {
  fetchEvents,
  fetchEventDetailsById,
  fetchEventsCumulation,
  fetchVehicleEvents,
  getEventsSearchSelectionLimit,
  getEventTypesCategories,
  normalizeAPIV2Array,
  normalizeAPIV3Item,
  fetchCitimissionIsAble,
  getWorkDoneLabel,
}
