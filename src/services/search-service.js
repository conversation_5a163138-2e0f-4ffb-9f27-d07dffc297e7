/**
 * @namespace Services
 * @category Services
 * @module search-service
 * */

import { getEventsSearchSelectionLimit as gessl } from '@/services/events-service.js'
import { getEnvValue } from '@/services/env-service.js'
import storage from '@/plugins/vue-local-storage.js'
import api from '@/api'
import * as R from 'ramda'
import store from '@/store'
import { APIV3RequestDatetimeFormat } from '@/config/simpliciti-apis'
import moment from 'moment'
import { splitOperation } from '@/utils/promise'
import { chunkArray, chunkIds } from '@/utils/array'
import { setTime } from '@/utils/dates'

export const getEventsSearchSelectionLimit = gessl

export const fetchVehicleDatesWithDataManager =
  createFetchVehicleDatesWithDataManager()

export default {
  waitUntilInitialized,
  getRangeTimeSelected,
}

/**
 * Wait until store.state.search_module.initialized is true
 * @param {boolean} type Optionally wait for reference data to be available (vehicles/drivers/circuits)
 */
export async function waitUntilInitialized(type = '') {
  return new Promise((resolve, reject) => {
    wait()
    function wait() {
      const isTypeInitialized =
        type === '' ||
        store.getters['search_module/isInitialLoadingComplete'][type] === true

      if (store.state.search_module.initialized && isTypeInitialized) {
        resolve()
      } else {
        setTimeout(wait, 50)
      }
    }
  })
}

export function getIdentificationSearchSelectionLimit(defaultValue = 50) {
  return parseFloat(
    getEnvValue('identificationModuleSearchSelectionLimit') ||
      getEnvValue('searchSelectionLimit', defaultValue)
  )
}

export function getLocationSearchSelectionLimit(defaultValue = 120) {
  return parseFloat(
    getEnvValue('locationSearchSelectionLimit') ||
      getEnvValue('searchSelectionLimit', defaultValue)
  )
}

export function getRangeTimeSelected(
  date,
  format = APIV3RequestDatetimeFormat
) {
  const selectedDateRanges =
    store.getters['search_module/getSelectedDateRanges'] || []
  let startRange = null
  let endRange = null

  const momentDate = moment(date)

  if (selectedDateRanges.length > 0) {
    const foundRange = selectedDateRanges.find(
      ([start, end]) =>
        moment(start).isSameOrBefore(momentDate) &&
        moment(end).isSameOrAfter(momentDate)
    )

    if (foundRange) {
      // If the date is in one of the ranges, we take that range
      startRange = moment(foundRange[0])
      endRange = moment(foundRange[1])
    } else {
      // If the date is not in any of the ranges, we take the first range
      startRange = moment(selectedDateRanges[0][0])
      endRange = moment(selectedDateRanges[0][1])
    }
  }

  let start = moment(date)
    .hour(startRange?.hour() || 0)
    .minute(startRange?.minute() || 0)
    .second(0)
    .format(format)
  let minuteEnd = endRange?.minute() !== undefined ? endRange?.minute() : 59
  let end = moment(date)
    .hour(endRange?.hour() !== undefined ? endRange?.hour() : 23)
    .minute(minuteEnd)
    .second(minuteEnd !== 0 ? 59 : 0)
    .format(format)
  return { start, end }
}

/**
 * Common way of request split to achieve a target a max number of parallel requests.
 *
 * @deprecated Use nested "for of" loops intead. See messages module as example.
 *
 * Used by events
 *
 * @param {Array} elementsIds Array with unique ids (e.g vehicleIds, circuitIds) Required
 * @param {Array} datesArr Array with dates Required
 * @param {Function} handleSubsetCallback Required
 * @param {Object} options
 * @param {number} options.maxParallelReq Default to 20 (Optional)
 * @param {number} options.maxItemsPerReq Default to 9999 (Optional)
 * @param {number} options.maxDatesPerReq Default to undefined (Optional)
 * @param {number} options.sequentialChunksMultiplier Defaults to dates length (optional)
 */
export async function nestedSplitElementsDates(
  elementsIds,
  datesArr,
  handleSubsetCallback,
  options = {}
) {
  const sequentialChunksMultiplier =
    options.sequentialChunksMultiplier || datesArr.length
  if (!handleSubsetCallback) {
    throw new Error('handleSubsetCallback required')
  }
  if (
    !elementsIds ||
    elementsIds.length === 0 ||
    !datesArr ||
    datesArr.length === 0
  ) {
    return handleSubsetCallback(null)
  }
  let reqTrack = null
  if (options.trackTime) {
    reqTrack = console.trackTime(options.trackTime)
  }

  let canceled = false

  function handleSubsetWithSplitDates(ids, dates) {
    if (options.maxDatesPerReq !== undefined) {
      return splitOperation({
        sequential: false,
        generateSubsets: () => chunkArray(dates, 1),
        handleSubset: async (datesSubsetArray) => {
          if (canceled) {
            return []
          }
          reqTrack &&
            reqTrack.count('handleSubset3 (req)', {
              ids,
              datesSubsetArray,
            })
          return await handleSubsetCallback(ids, datesSubsetArray, () => {
            console.debugVerbose(8, 'nestedSplitElementsDates canceled')
            canceled = true
          })
        },
      })
    } else {
      return handleSubsetCallback(ids, datesArr)
    }
  }

  let finalRes = await splitOperation({
    sequential: true,
    generateSubsets: () => {
      const count = chunkIds(
        elementsIds,
        sequentialChunksMultiplier,
        options.maxParallelReq || 20
      )
      reqTrack &&
        reqTrack.count('maxParallelReq chunks count', {
          count,
        })
      return count
    },
    handleSubset: async (ids) => {
      if (canceled) {
        return null
      }
      reqTrack &&
        reqTrack.count('handleSubset1', {
          ids,
        })
      if (ids.length > (options.maxItemsPerReq || 9999)) {
        return await splitOperation({
          sequential: false,
          generateSubsets: () => chunkArray(ids, options.maxItemsPerReq),
          handleSubset: async (_ids) => {
            if (canceled) {
              return []
            }
            reqTrack &&
              reqTrack.count('handleSubset2', {
                _ids,
              })
            return await handleSubsetWithSplitDates(_ids, datesArr)
          },
        })
      } else {
        return handleSubsetWithSplitDates(ids, datesArr)
      }
    },
  })
  reqTrack && reqTrack.stop()
  return finalRes
}

/**
 * Used by SearchModule/LocationModule to skip vehicle/dates from realtime/history API calls when there is no data available. (Performance)
 *
 * Provides a method (fetchVehicleDatesWithData) to call an API (api.dateVehicleExists.getAll) caching results in client-side using localforage/localStorage.
 * @returns
 */
export function createFetchVehicleDatesWithDataManager() {
  let memory = [
    /** {vehicleId:1,dates:['2020-11-21']} */
  ]
  let lastSyncPromise = null
  let searchStorage = null
  let progressing = false

  /**
   * Bring from cache into memory using cache interface "searchStorage" which supports async keys(), async setItem(key,value), async getItem(key)
   */
  async function syncFromCache() {
    memory = await searchStorage.getItem('vehicleDatesData', [])
  }

  /**
   * Removes dates older than 3 months (Reduces cache size)
   * Removes cache item if no dates (Reduces cache size)
   */
  async function removeOldCache(_searchStorage) {
    _searchStorage = _searchStorage || searchStorage
    try {
      let mem = await _searchStorage.getItem('vehicleDatesData', [])
      const oldDate = moment().subtract(3, 'month')
      let vehicleItemsToRemove = []
      mem.forEach((vehicleItem) => {
        let datesToRemove = []
        const pushToDatesToRemoveFn = (date) => {
          if (moment(date, 'YYYY-MM-DD').isBefore(oldDate)) {
            datesToRemove.push(date)
          }
        }
        vehicleItem.dates.forEach(pushToDatesToRemoveFn)
        vehicleItem.dates = vehicleItem.dates.filter(
          (d) => !datesToRemove.includes(d)
        )
        if (vehicleItem.noDates !== undefined) {
          vehicleItem.noDates.forEach(pushToDatesToRemoveFn)
          vehicleItem.noDates = vehicleItem.noDates.filter(
            (d) => !datesToRemove.includes(d)
          )
        }
        if (
          vehicleItem.dates.length === 0 &&
          (vehicleItem.noDates === undefined ||
            vehicleItem.noDates.length === 0)
        ) {
          vehicleItemsToRemove.push(vehicleItem.vehicleId)
        }
      })
      _searchStorage.setItem(
        'vehicleDatesData',
        mem.filter((i) => !vehicleItemsToRemove.includes(i.vehicleId))
      )
    } catch (err) {
      console.error(
        'search-service (Fetch vehicle dates with data manager): While removing old cache',
        {
          err,
        }
      )
    }
  }

  function configureSearchStorage(key, force = false) {
    if (!key && !searchStorage) {
      throw new Error(
        'search service vehileDatesDataManager configureSearchStorage key required'
      )
    }
    searchStorage =
      force === false && !!searchStorage
        ? searchStorage
        : storage.fromNamespace('search_vehicledatesdata_' + key)
  }

  const scope = {
    getMemory: () => memory,
    removeOldCache,

    /**
     * Unload data from memory
     */
    reset(searchStorageSegregateKey) {
      if (!searchStorageSegregateKey) {
        throw new Error('searchStorageSegregateKey required')
      }
      memory.length = 0
      configureSearchStorage(searchStorageSegregateKey)
      progressing = false
      window.fvdwdmss = searchStorage
      lastSyncPromise = syncFromCache()
      removeOldCache(searchStorage)
    },
    /**
     * - If some vehicleId/date has cached results, we use them instead of API call.
     * - Calls the API (api.dateVehicleExists.getAll) with remaining vehicleId/date which are not in cache.
     * - We cache results (vehicleId/date)
     * - We return cached+new results
     * @return {Array} Array [{vehicleId:1,dates:[],noDates:[]}] dates: Has data, noDates: No data
     */
    async checkVehicleDatesWithData({ vehicleIds, dates }, segregationKey) {
      //Resolve last sync
      if (lastSyncPromise) {
        await lastSyncPromise
      }

      if (progressing) {
        return new Promise((resolve, reject) => {
          setTimeout(check, 500)
          function check() {
            console.debugVerbose(8, 'checkVehicleDatesWithData delay')
            if (!progressing) {
              scope
                .checkVehicleDatesWithData(
                  { vehicleIds, dates },
                  segregationKey
                )
                .then(resolve)
                .catch(reject)
            } else {
              setTimeout(check, 500)
            }
          }
        })
      }

      const track = console.trackTime('fetch vehicles/dates data', [
        8,
        'search',
      ])
      try {
        progressing = true

        //Filter out cached items from vehicleIds and dates variables
        let getCachedMemory = () =>
          memory
            .filter((i) => vehicleIds.includes(i.vehicleId))
            .map((item) => {
              return {
                vehicleId: item.vehicleId,
                dates: item.dates.filter((d) => dates.includes(d)),
                noDates: item.noDates || [],
              }
            })

        let cachedMemory = getCachedMemory()

        if (getEnvValue('location_has_data_check_skip_if_cached') === '1') {
          vehicleIds = vehicleIds.filter((id) => {
            let match = cachedMemory.find((i) => i.vehicleId === id)
            return (
              //If no cache, include
              !match ||
              //If some of the dates are not available in cache dates and noDates, include
              (match &&
                dates.some(
                  (d) => !match.dates.includes(d) && !match.noDates.includes(d)
                ))
            )
          })
        }

        let res = []
        if (vehicleIds.length > 0 && dates.length > 0) {
          //Perform API call
          /*
          res = await api.dateVehicleExists.getAll({
            vehicleId: vehicleIds,
            date: dates,
          })*/
          //Feat: concat into sequential chunks of 500, parallel 50 ids (20 req at time)
          res = await splitOperation({
            sequential: true,
            generateSubsets() {
              return chunkArray(vehicleIds, 500)
            },
            async handleSubset(subset) {
              if (subset.length > 50) {
                return R.flatten(
                  await splitOperation({
                    generateSubsets() {
                      return chunkArray(subset, 50)
                    },
                    handleSubset(subSubset) {
                      return api.dateVehicleExists.getAll({
                        vehicleId: subSubset,
                        date: dates,
                      })
                    },
                  })
                )
              }
              return await api.dateVehicleExists.getAll({
                vehicleId: subset,
                date: dates,
              })
            },
          })
          res = R.flatten(res)
        } else {
          return Object.freeze(cachedMemory)
        }

        console.debugVerbose(8, 'checkVehicleDatesWithData raw res', {
          res,
          memory,
        })

        //Merge response from API into memory
        res.forEach((item) => {
          let cachedItem = memory.find((i) => i.vehicleId === item.vehicleId)
          if (cachedItem) {
            cachedItem.dates = R.uniq([...cachedItem.dates, ...item.dates])
          } else {
            memory.push({
              vehicleId: item.vehicleId,
              dates: item.dates,
            })
          }
        })

        //Update noDates (empty results) dates in memory
        vehicleIds.forEach((id) => {
          let resItem = res.find((i) => i.vehicleId === id)
          if (!resItem) {
            resItem = { vehicleId: id, dates: [] }
          }

          let mem = memory.find((m) => m.vehicleId === id)
          if (!mem) {
            memory.push({
              vehicleId: id,
              dates: [],
            })
            mem = memory.find((m) => m.vehicleId === id)
          }

          dates.forEach((date) => {
            if (!resItem.dates.includes(date)) {
              mem.noDates = mem.noDates || []
              mem.noDates = R.uniq([...mem.noDates, ...[date]]) //Persist empty results
            }
          })
        })

        //Update cache
        configureSearchStorage(segregationKey)
        await searchStorage.setItem('vehicleDatesData', memory)

        //Compute response (cached+new)
        /*let merged = cachedMemory
        vehicleIds.forEach((vehicleId) => {
          let match = merged.find((i) => i.vehicleId === vehicleId)
          if (!match) {
            merged.push({ vehicleId, dates: [] })
            match = merged.find((i) => i.vehicleId === vehicleId)
          }
          //merge response dates if any
          const resItem = res.find((i) => i.vehicleId === vehicleId)
          if (resItem) {
            match.dates = R.uniq([...match.dates, ...resItem.dates])
          }
        })*/

        //Return updated cached memory
        cachedMemory = getCachedMemory()
        let r = Object.freeze(cachedMemory)
        console.debugVerbose(8, 'checkVehicleDatesWithData result', {
          r,
        })
        return r
      } finally {
        progressing = false
        track.stop()
      }
    },
  }
  window._vdwdm = scope
  return scope
}

/**
 * Returns start and end time for a given date
 *
 * @param {moment.Moment} date - The base moment date
 * @param {number} index - The index of the date
 * @param {number} total - Total number of dates
 * @param {string} timeFrom - "HH:mm"
 * @param {string} timeTo - "HH:mm"
 * @param {boolean} applyTimeRangeToEachDayOfDateRange - If true, apply time range to each day of the date range
 * @returns {[moment.Moment, moment.Moment]} - [m, mEnd]
 */
export function computeStartAndEndTimeOfDate(
  date,
  index,
  total,
  timeFrom,
  timeTo,
  applyTimeRangeToEachDayOfDateRange
) {
  let m = moment(date)
  let mEnd = m.clone()

  // Handle the case when there's only one date
  if (total === 1) {
    m = setTime(m, timeFrom)
    mEnd = setTime(mEnd, timeTo)
  } else {
    const isFirst = index === 0
    const isLast = index === total - 1
    const isIntermediate = !isFirst && !isLast

    if (isFirst) {
      m = setTime(m, timeFrom)

      if (!applyTimeRangeToEachDayOfDateRange) {
        mEnd = mEnd.set({ hour: 23, minute: 59, second: 0 })
      } else {
        mEnd = setTime(mEnd, timeTo)
      }
    } else if (isIntermediate) {
      if (!applyTimeRangeToEachDayOfDateRange) {
        m = m.set({ hour: 0, minute: 0, second: 0 })
        mEnd = mEnd.set({ hour: 23, minute: 59, second: 0 })
      } else {
        m = setTime(m, timeFrom)
        mEnd = setTime(mEnd, timeTo)
      }
    } else if (isLast) {
      if (!applyTimeRangeToEachDayOfDateRange) {
        m = m.set({ hour: 0, minute: 0, second: 0 })
      } else {
        m = setTime(m, timeFrom)
      }

      mEnd = setTime(mEnd, timeTo)
    }
  }

  return [m, mEnd]
}
