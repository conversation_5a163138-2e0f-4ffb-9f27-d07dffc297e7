/**
 * @namespace Services
 * @category Services
 * @module alert-service
 * */
import moment from 'moment'
import i18n from '@/i18n'
import {
  fetchAlerts,
  fetchAlertTypes as fetchAlertTypesFromAPI,
  acknowledgeAlert as acknowledgeAlertFromAPI,
} from '@/api/alerts-api.js'
import { getEnvIntValue } from '@/services/env-service'
import { nestedSplitElementsDates } from './search-service'
import { dateRangeToChunks } from '@/utils/dates'

import Ajv from 'ajv'
import { chunkArray } from '@/utils/array'
import searchService from '@/services/search-service'
import { APIV2RequestDatetimeFormat } from '@/config/simpliciti-apis'

const ajv = new Ajv()

const momentSchema = {
  type: 'object',
  properties: {
    _isAMomentObject: {
      type: 'boolean',
    },
  },
  required: ['_isAMomentObject'],
}

const validateMoment = ajv.compile(momentSchema)

const MAX_PARALLEL_REQ = getEnvIntValue('alertsSearchMaxParallelReq', 20)
const MAX_DATES_PER_REQ = getEnvIntValue('alertsSearchMaxDatesPerReq', 10)
const MAX_VEHICLE_PER_REQ = getEnvIntValue('alertsSearchMaxVehiclesPerReq', 10)

export function getSearchSelectionLimit() {
  return getEnvIntValue(
    'alertsSearchMaxSelectionLimit',
    getEnvIntValue('searchSelectionLimit', 100)
  )
}

export async function fetchAlertTypes() {
  return fetchAlertTypesFromAPI()
}

export async function acknowledgeAlert(messageId) {
  return acknowledgeAlertFromAPI(messageId)
}

export function shareAlertByEmail(alertItem) {
  const mail = document.createElement('a')
  const title = `${alertItem.title} (${alertItem.type})`
  const formattedBody = `
    ${alertItem.message}
    `
  mail.href = `mailto:?subject=${title}&body=${encodeURIComponent(
    formattedBody
  )}`
  mail.click()
}

export function computeStatusLabel(isAck = false) {
  return isAck
    ? i18n.t('alerts.item.status_ack')
    : i18n.t('alerts.item.status_noack')
}

/**
 * Split a date range (start/end) into chunks of date ranges given a maxSize constraint.
 * @param {Moment} startDate
 * @param {Moment} endDate
 * @returns
 */
function getAlertStartEndDatesToChunks(startDate, endDate, maxSize) {
  if (!validateMoment(startDate)) {
    throw new Error('startDate must be a Moment object')
  }

  if (!validateMoment(endDate)) {
    throw new Error('endDate must be a Moment object')
  }

  const diffDays = Math.abs(endDate.diff(startDate, 'day'))
  const datesChunks = []

  let t = console.trackTime('getAlertStartEndDatesToChunks', false)
  t.count('state', { diffDays, startDate, endDate })

  //e.g more than 10 days
  if (diffDays <= maxSize) {
    datesChunks.push({
      startDate: startDate.clone(),
      endDate: endDate.clone(),
    })
  } else {
    //24hs or more without custom time
    if (startDate.hour() === 0 && endDate.hour() === 0) {
      datesChunks.push(...dateRangeToChunks(startDate, endDate))
    } else {
      //first chunk contains time to midnight
      datesChunks.push({
        startDate: startDate.clone(),
        endDate: startDate.clone().hour(23).minute(59).second(59),
      })
      //days in between (full days)
      datesChunks.push(
        ...dateRangeToChunks(
          startDate.clone().add(1, 'day').hour(0).minute(0).second(0),
          endDate.clone().subtract(1, 'day').hour(23).minute(59).second(59),
          maxSize
        )
      )
      //last chunk contains midnight to start time
      datesChunks.push({
        startDate: endDate.clone().hour(0).minute(0).second(0),
        endDate: endDate.clone(),
      })
    }
  }
  t.stop()
  return datesChunks
}

getAlertStartEndDatesToChunks.schema = {
  type: 'object',
  properties: {
    startDate: momentSchema,
    endDate: momentSchema,
  },
  required: ['startDate', 'endDate'],
}

/**
 * Used by alerts module.
 *
 * - Progressive
 * - Parallel split (max vehicles, max dates, max parallel req)
 *
 * @param {Array} options.vehicleIds []
 * @param {Moment} options.dateFrom Date
 * @param {Moment} options.dateTo Date
 * @param {Array} options.types [] i.g [48,32]
 * @param {Array} options.status [] i.g [0,1]
 * @param {function} options.handleResults (results)=>({}) Each time results are available (Returns false to cancel)
 * @return {Array} items
 */
export async function fetchAlertsProgressive(options = {}) {
  const getFetchOptions = () => ({
    types: options.types,
    status: options.status,
  })
  const { vehicleIds, startDate, endDate, dateRanges } = options

  let abort = false
  let items = []
  const customHandler = async (vehicleIds, _startDate, _endDate) => {
    const partialRes = await fetchAlerts(
      vehicleIds.join(','),
      _startDate,
      _endDate,
      getFetchOptions()
    )
    items.push(...partialRes)
    abort = (await options.handleResults(partialRes)) === false
  }

  const datesChunks = chunkArray(dateRanges, MAX_DATES_PER_REQ)

  const vehicleIdsChunks = chunkArray(vehicleIds, MAX_VEHICLE_PER_REQ)
  const track = console.trackTime('alerts fetch')
  track.count('state', {
    vehicleIdsChunks,
    datesChunks,
    MAX_DATES_PER_REQ,
    MAX_VEHICLE_PER_REQ,
    MAX_PARALLEL_REQ,
    startDate,
    endDate,
  })
  const parallelPromises = []
  for (let vehicleIdsSubset of vehicleIdsChunks) {
    for (let date of datesChunks[0]) {
      if (abort) {
        break
      }
      if (parallelPromises.length > MAX_PARALLEL_REQ) {
        await parallelPromises[0]
        parallelPromises.shift()
      }
      track.count('request')

      let fromDate = searchService.getRangeTimeSelected(
        date,
        APIV2RequestDatetimeFormat
      ).start
      let toDate = searchService.getRangeTimeSelected(
        date,
        APIV2RequestDatetimeFormat
      ).end

      parallelPromises.push(customHandler(vehicleIdsSubset, fromDate, toDate))
    }
  }
  await Promise.all(parallelPromises)
  track.count(abort ? 'aborted' : 'resolved')
  track.stop()
  return items
}
