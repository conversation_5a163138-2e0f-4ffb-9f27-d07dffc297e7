/**
 * @namespace Services
 * @category Services
 * @module identification-service
 * */

import {
  fetchLeveesSynthesis as fetchLeveesSynthesisFromAPI,
  fetchLeveesDetails as fetchLeveesDetailsFromAPI,
} from '@/api/identification-api'
import { removeFirstResolved, splitOperation } from '@/utils/promise.js'
import moment from 'moment'
import { getNestedValue } from '@/utils/object.js'
import { chunkArray, chunkIds } from '@/utils/array'
import { getEnvValue } from '@/services/env-service'
import { isUnitTest } from '@/utils/unit'

export { normalizeLeveesSynthesisItem } from '@/api/identification-api'

export async function fetchLeveesDetails(
  date,
  vehicleId,
  circuitId,
  options = {}
) {
  return isUnitTest()
    ? []
    : fetchLeveesDetailsFromAPI(date, vehicleId, circuitId, options)
}

/**
 *
 * Called first, after validating a search selection.
 *
 * Split strategy: max items per req, max parallel req (single date per req because api constraint)
 *
 * @param {Object} options
 * @param {number} options.elementIds []
 * @param {Array} options.dates
 * @param {*} options.types [] i.g [48,32]
 * @param {*} options.status [] i.g [0,1]
 * @param {Object} options.elementType fetch options: elementType
 * @param {Object} options.filters fetch options: filters
 * @param {function} options.handleResults (results)=>({}) Each time results are available
 */
export async function fetchLeveesSynthesis({
  elementIds,
  dates,
  elementType,
  filters = {},
  handleResults = null,
} = {}) {
  const finalResults = []
  const track = console.trackTime('Identification fetch synthesis')
  let parallelPromises = []
  const maxParallelReq = getEnvValue(
    'identificationSearchSynthesisMaxparallelPromises',
    15,
    {
      transform: (v) => parseInt(v) || 15,
    }
  )
  const maxItemsPerRequest = getEnvValue(
    'identificationSearchSynthesisItemsSize',
    1,
    {
      transform: (v) => parseInt(v) || 1,
    }
  )
  const idsChunks = chunkArray(elementIds, maxItemsPerRequest)
  let aborted = false
  const getFetchOptions = () => ({
    elementType,
    filters,
  })

  const handleSubset = async (ids, dates) => {
    track.count('reqCount')
    let results = await fetchLeveesSynthesisFromAPI(
      ids.join(','),
      dates,
      getFetchOptions()
    )
    if (handleResults) {
      aborted = handleResults(results) === false
    }
    finalResults.push(...results)
    return results
  }
  track.count('state', {
    idsChunks,
    elementIds,
    elementType,
    dates,
    finalResults,
    parallelPromises,
  })
  for (let ids of idsChunks) {
    for (let date of dates) {
      if (aborted) {
        break
      }
      if (parallelPromises.length > maxParallelReq) {
        parallelPromises = await removeFirstResolved(parallelPromises)
      }
      parallelPromises.push(handleSubset(ids, [date]))
    }
  }
  track.count(aborted ? 'aborted' : 'resolved')
  if (aborted) {
    track.stop()
    return []
  } else {
    await Promise.all(parallelPromises)
    track.stop()
  }
  return finalResults
}

export function createContainerHelpersMixin() {
  return {
    data() {
      return {
        //Necessary to compute table header text
        dateFromFormatted: '',
        dateToFormatted: '',
        //detailedItemsBy (external)
        //detailedItemsByDateValue (external)
        //detailedItemsByVehicleIdValue (external)
      }
    },
    computed: {
      tableHeaderText() {
        if (this.detailedItemsBy === 'total') {
          return this.$t('identification.table.header_text.total', {
            fromDate: this.dateFromFormatted,
            toDate: this.dateToFormatted,
          })
          //return `Résultats du ${this.dateFromFormatted} au ${this.dateToFormatted}`;
        }
        if (this.detailedItemsBy === 'date') {
          return this.$t('identification.table.header_text.date', {
            date: this.detailedItemsByDateValue,
          })
          //return `Résultats du ${this.detailedItemsByDateValue}`;
        }
        if (this.detailedItemsBy === 'vehicle') {
          return this.$t('identification.table.header_text.vehicle', {
            vehicleName: this.getVehicleName(
              this.detailedItemsByVehicleIdValue
            ),
            date: this.detailedItemsByDateValue,
          })
          //return `Résultats du véhicule ${this.getVehicleName(this.detailedItemsByVehicleIdValue)} du ${this.detailedItemsByDateValue}`;
        }
        if (this.detailedItemsBy === 'circuit') {
          return this.$t('identification.table.header_text.circuit', {
            vehicleName: this.getVehicleName(
              this.detailedItemsByVehicleIdValue
            ),
            circuitName: this.getCircuitName(
              this.detailedItemsByVehicleIdValue,
              this.detailedItemsByCircuitIdValue
            ),
            date: this.detailedItemsByDateValue,
          })
          //return `Résultats du véhicule ${this.getVehicleName(this.detailedItemsByVehicleIdValue)}, circuit ${this.getCircuitName(this.detailedItemsByVehicleIdValue,this.detailedItemsByCircuitIdValue)} du ${this.detailedItemsByDateValue}`;
        }
        return ''
      },
    },
    methods: {
      /**
       * Uses this.items (external)
       */
      getVehicleName(id) {
        return (
          ((this.items || []).find((i) => i.vehicleId == id) || {})
            .vehicleName || ''
        )
      },
      getCircuitName(vehicleId, circuitId) {
        let synthesisVehicleItem =
          (this.items || []).find((i) => i.vehicleId == vehicleId) || {}
        let circuitSynthesis =
          (
            synthesisVehicleItem.circuits ||
            synthesisVehicleItem.synthesis ||
            []
          ).find((s) => s.circuitId == circuitId) || {}
        return circuitSynthesis.circuitName || ''
      },
    },
  }
}

/**
 * Used by Location - Identification (Popup details, triggered from table)
 * Used by Identification (Popup details, triggered from table)
 * @param {Object} data API Response
 * @returns
 */
export function normalizeIdentificationBacDetailsResponse(data) {
  if (!data || (data instanceof Array && data.length === 0)) {
    return {}
  }
  return {
    id: data['id'],
    startDate: data['collectedAt'],
    prohibition: formatProhibition(data['blackList']),
    behavior: formatBehavior(data['containerLiftingBehavior']),
    export: formatExport(data['liftExports']),
    override: formatOverride(
      data['vehicle'],
      data['round'],
      data['complement']
    ),
    vehicle: formatVehicle(data['vehicle']),
    round: formatRound(data['round']),
    location: formatLocation(data),
    status: formatStatus(data),
  }

  function formatObjectValue(item, key, defaultValue, options = {}) {
    return getNestedValue(item, key, defaultValue, options)
  }

  function formatProhibition(item) {
    if (!item) return {}

    return {
      id: item['blackListId'],
      isBlacklisted: data['isBlacklisted'],
      filename: item['fileName'],
      versionDate: item['versionedAt'],
      issue: item['reason'],
    }
  }

  function formatBehavior(item) {
    if (!item) return {}

    return {
      name: item['name'],
      endValidity: item['endValidityAt'],
      blockedAccess: item['blockingAccessContainerLiftingBehavior'],
      driverChoice: item['driverChoiceBlockingContainerLiftingBehavior'],
    }
  }

  function formatExport(items) {
    if (!items || !Array.isArray(items) || items.length === 0) return {}

    let lastFile = items.reduce((latest, current) => {
      return moment(current.date).isAfter(moment(latest.date))
        ? current
        : latest
    })

    return {
      lastExportDate: lastFile.date,
      fileExport: lastFile.fileName,
    }
  }

  function formatOverride(vehicle, round, override) {
    if (!override) return {}
    console.log('override', override, vehicle, round)
    return {
      isOverride: override['overrideAt'] !== null,
      initial: {
        vehicle: vehicle,
        round: round,
      },
      current: {
        vehicle: override['vehicleOverride'],
        round: override['roundOverride'],
      },
      editedAt: override['overrideAt'],
      editedBy: formatUser(override['userOverride']),
    }
  }

  function formatVehicle(item) {
    if (!item) return {}

    return {
      id: item['id'],
      name: item['name'],
      categoryName: item['categoryName'],
      registrationPlate: item['registrationPlate'],
    }
  }

  function formatRound(item) {
    if (!item) return {}

    return {
      contract: item['contract'],
      shortName: item['shortName'],
      categoryName: item['categoryName'],
      id: item['id'],
    }
  }

  function formatLocation(item) {
    return {
      streetNumber: item['streetNumber'],
      streetName: item['streetName'],
      city: item['city'],
      zipCode: item['zipCode'],
      country: item['country'],
      latitude: item['latitude'],
      longitude: item['longitude'],
    }
  }

  function formatStatus(item) {
    return {
      isStopped: item['isStopped'],
      isAuthorized: item['isAuthorized'],
      isIdentified: item['isIdentified'],
      isCollected: item['isCollected'],
      isHighPoint: item['isHighPoint'],
      isWeightOnLoad: item['isWeightOnLoad'],
      isWeightUnderLoad: item['isWeightUnderLoad'],
    }
  }

  function formatUser(data) {
    if (!data) return {}

    return data['fullName']
  }
}
