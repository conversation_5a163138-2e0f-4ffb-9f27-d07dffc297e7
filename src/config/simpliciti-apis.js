import { getEnvValue } from '@/services/env-service.js'

/**
 * @todo Use these variables instead of hardcoded URLs
 * @todo Rename with prefix to indicate method. Example: APIV2_POST, APIV3_PATCH
 */
const APIUrls = {
  APIV3_GET_POSITION: '/positions/geored/get_position',
  APIV3_GET_POSITIONS: '/positions/geored/get_positions',
  APIV2_POSITIONS_GPS: '/v2/historique/positions_gps',
  APIV2_POSITIONS_CAPTEURS: '/v2/historique/positions_capteurs',
  APIV3_GET_SENSORS: '/positions/geored/get_sensors',
  APIV2_POSITIONS: '/v2/historique/positions',
  APIV2_HISTORIQUE_BY_VEHICLE_DATES: '/v2/historique/by_vehicule_dates',
  APIV2_HISTORIQUE_TRONCONS_DETAILS: '/v2/historique/troncons_details',
  APIV2_HISTORIQUE_CIRCUIT_TRONCONS: '/v2/historique/circuit_troncons',
  APIV2_HISTORIQUE_DERNIER_CIRCUIT: '/v2/historique/dernier_circuit',
  APIV2_HISTORIQUE_BY_CIRCUIT_DATES: '/v2/historique/by_circuit_dates',
  APIV2_HISTORIQUE_CONSOMMATION_PAR_PORTION:
    '/v2/historique/consommation_par_portion',
  APIV2_EVENEMENTS_CLIENT_INDICATEURS: '/v2/evenements_client/indicateurs.json',
  APIV3_AGGREGATE_EVENTS: '/geored/events/aggregate_events',
  APIV3_EVENTS_ANOMALIES: '/geored/events/anomalies',
  APIV2_ZONE_CLIENT: '/v2/zoneclient',
  APIV2_MESSAGE_PREDEFINI_GET: '/v2/message_predefini/get',
  APIV2_IDENTIFICATION_LEVEE_BAC_DETAIL:
    import.meta.env.VITE_API_IDENTIFICATION_BAC_DETAILS_URI ||
    '/v2/levee_bac_detail',
  APIV3_VEHICLE_DETAILS: '/geored/vehicle/vehicles',
  APIV3_VEHICLE: '/geored/vehicle/vehicles',
  APIV3_POSITIONS_CUMULATION: '/positions/geored/cumulation',
  APIV3_EVENTS_CUMULATION: '/geored/events/service/cumulation',
  APIV3_VEHICLE_BOX_ALLOCATIONS: '/geored/vehicle/box_allocations',
  APIV3_BOX_SENSOR_ASSIGNMENTS: '/geored/box/sensor_assignments',
  APIV3_BOX_CONFIGURATIONS: '/geored/box/box_configurations',
  APIV3_CLIENT_LOGOS: getEnvValue(
    'VITE_CLIENT_LOGO_URI',
    '/geored/client/logos'
  ),
  APIV3_CLIENT_PARAMETERS: getEnvValue(
    'VITE_CLIENT_PARAMETERS_URI',
    '/geored/client_parameters'
  ),
  APIV3_USER_ACTIVITY: '/geored/service/client/get_user_activity',
  APIV3_USER_LOGIN_AS: '/geored/service/user/login_as',
  APIV3_CLIENT_HIRERARCHIES: '/geored/client/hierarchies',
  APIV2_MESSAGE_ACK: '/v2/cdm/message_alerte/ack',
  APIV2_CDM_TYPE_ALERTE: '/v2/cdm/type_alerte',
  APIV2_CHAUFFEUR_CATEGORIES: '/v2/chauffeur/categorie',
  APIV2_CHAUFFEURS: `/v2/chauffeur`,
  APIV2_CIRCUIT_CATEGORIES: `/v2/circuit/categorie`,
  APIV2_CIRCUITS: `/v2/circuits`,
  APIV2_MESSAGES_ALERTES: '/v2/cdm/messages_alertes',
  APIV2_TEMP_REEL_INDICATEUR_BY_VEHICLE:
    '/v2/temps_reel/indicateurs/by_vehicule.json',
  APIV2_IDENTIFICATION_LEVEE_BAC: '/v2/levee_bac',
  //APIV2_MOB_CATEGORIES_VEHICLES: '/v2/mob_categoriesvehicules',
  //APIV2_MOB_VEHICLES: `/v2/mob_vehicules`,
  APIV3_JAVASCRIPT_ERRORS: '/javascript_errors',
  APIV3_CARTO_GEOCODING_PROXY_OSM_NOMINATIM:
    '/service/cartography/osm_nominatim',
  APIV3_CARTO_GEOCODING_PROXY_GOOGLE: getEnvValue(
    'VITE_GOOGLE_GEOCODING',
    '/service/cartography/google'
  ),
  APIV3_CARTO_GEOCODING_NATURAL: '/service/cartography/natural_geocoding',
  APIV3_CARTO_GEOCODING: '/service/cartography/geocoding',
  APIV3_CARTO_GEOCODING_REVERSE:
    import.meta.env.VITE_REVERSE_GEOCODING_URI ||
    '/service/cartography/reverse_geocoding',
  APIV3_CARTO_ROUTING:
    import.meta.env.VITE_ROUTING_GEOCODING_URI ||
    '/service/cartography/routing',
  APIV3_USER_SESSIONS: '/geored/client/user/sessions',
  APIV3_USER_AUDITS: '/geored/client/user/audits',
  APIV3_GET_BIN_COLLECTION_BLACK_LIST_HISTOS:
    '/bin_collection/black_list_histos',
  APIV3_GET_BIN_COLLECTION_BLACK_LISTS: '/bin_collection/black_lists',
  APIV3_CLIENT_CARTOGRAPHY: '/geored/client/clients/${clientId}/cartography',
  APIV3_USER_ACCESS_RIGHTS: '/geored/client/user_access_rights',
  APIV3_ECO_DRIVING_VEHICLES: '/eco_driving_vehicles/aggregate_client',
  APIV3_ECO_DRIVING_DRIVERS: '/eco_driving_drivers/aggregate_client',
  APIV3_ECO_DRIVING_CIRCUITS: '/eco_driving_rounds/aggregate_client',
  APIV2_ECOCONDUITE_ECO_DRIVING_VEHICLES:
    '/v2/eco_conduite/eco_driving_vehicles',
  APIV2_ECOCONDUITE_ECO_DRIVING_DRIVERS: '/v2/eco_conduite/eco_driving_drivers',
  APIV2_ECOCONDUITE_ECO_DRIVING_CIRCUITS: '/v2/eco_conduite/eco_driving_rounds',
  APIV3_SERVICE_VEHICLE_CONFIGURATION: '/geored/vehicle/service/configuration',
  APIV3_VEHICLE_CATEGORIES: '/geored/vehicle_categories',
  APIV3_DRIVER_CATEGORIES: '/geored/driver_categories',
  APIV3_DRIVERS: '/geored/drivers',
  APIV3_ROUND_CATEGORIES: `/geored/round_categories`,
  APIV3_ROUNDS: `/geored/rounds`,
  APIV2_TEMPS_REEL_BY_VEHICLE: '/v2/temps_reel/by_vehicule',
  APIV2_TEMPS_REEL_BY_DRIVER: '/v2/temps_reel/by_chauffeur',
  APIV2_HISTORIQUE_BY_DRIVER_DATES: '/v2/historique/by_chauffeur_dates',
  APIV2_TEMPS_REEL_BY_CIRCUIT: '/v2/temps_reel/by_circuit',
  APIV3_GET_GEORED_CLIENT_USER_MAP_VIEWS: '/geored/client/user/map_views',
  APIV3_POST_GEORED_CLIENT_USER_MAP_VIEWS: '/geored/client/user/map_views',
  APIV3_GEORED_ROUND_VERSIONS: '/geored/round/versions',
  APIV3_GEORED_ROUND_VERSIONS_SECTIONS: '/geored/round/versions_sections',
  APIV3_GEORED_ROUND_ACTIVITIES: '/geored/round/activities',
  APIV3_GEORED_ROUND_SERVICE_LAST_ROUND_EXECUTION_SECTIONS:
    '/geored/round/service/last_round_execution_sections',
  APIV3_GEORED_ROUND_MANEUVERS: '/geored/round/maneuvers',
  APIV3_GEORED_CLIENT_USER_PARAMETERS: '/geored/client/user_parameters',
  APIV3_SERVICE_NEARBY_VEHICLES: '/service/cartography/nearby_vehicles',
  APIV3_GEORED_REALTIME_SERVICE_TO_KML: '/geored/real_time/service/to_kml',
  APIV3_GEORED_SERVICE_HISTORY_TO_KML: '/geored/service/history/to_kml',
  APIV3_ZONE_TYPES: '/geored/area/area_types',
  APIV3_ZONES: '/geored/area/areas',
  APIV3_ZONE_CATEGORIES: '/geored/area_categories',
  APIV3_CLIENT_SERVICE_CARTOGRAPHY: '/geored/client/service/cartography',
  APIV3_GEORED_SERVICE_HISTORY_TO_SHAPE: '/geored/service/history/to_shape',
  APIV3_GEORED_SERVICE_ROUND_EXECUTION_PENDULUM:
    '/geored/round/service/round_execution/${roundExecutionId}/pendulum',
  APIV3_GEORED_SERVICE_HISTORY_TO_ROUND:
    '/geored/service/history/to_round/${vehicleId}',
  APIV3_GEORED_ROUND_CALCULATION_METHODS: '/geored/round/calculation_methods',
  APIV3_GEORED_SERVICE_CARTOGRAPHY_LAST_VEHICLES:
    '/service/cartography/last_vehicles',
  APIV3_GEORED_SERVICE_HISTORY_VEHICLES: '/geored/service/history/vehicles',
  APIV3_GEORED_SERVICE_HISTORY_SECTIONS: '/geored/service/history/sections',
  APIV3_GEORED_SERVICE_VEHICLE_MESSAGES:
    '/geored/message/service/vehicle-messages',
  APIV3_GEORED_MESSAGE_SERVICE_SEND_MESSAGE:
    '/geored/message/service/send-messages',
  APIV3_GEORED_MESSAGE_PREDEFINED: '/geored/message/predefineds',
  APIV3_GEORED_MESSAGE_PLANNING: '/geored/message/message_plannings',
  APIV3_GEORED_RECURRENCE_RECURRENCE_DAYS: '/geored/recurrence/recurrence_days',
  APIV3_GEORED_RECURRENCE_RECURRENCE_FREQUENCIES:
    '/geored/recurrence/recurrence_frequencies',
  APIV3_GEORED_MESSAGE_SERVICE_SEND_PLANNED_MESSAGE:
    '/geored/message/service/send-planning-messages',
  APIV3_GEORED_MESSAGE_VEHICLE_MESSAGE_PLANNING:
    '/geored/message/vehicle_message_plannings',
  APIV3_GEORED_BLACK_SPOT_BACK_SPOTS: '/geored/black_spot/black_spots',
  APIV3_GEORED_BLACK_SPOT_BACK_SPOTS_CONTRAINTS_TYPES:
    '/geored/black_spot/constraint_types',
  APIV3_GEORED_BLACK_SPOT_CATEGORIES:
    '/geored/black_spot/black_spot_categories',
  APIV3_GEORED_BLACK_SPOT_SEGMENTS: '/geored/black_spot/segments',
  APIV3_GEORED_BLACK_SPOT_SPEED_LIMITS: '/geored/black_spot/speed_limits',
  APIV3_GEORED_BLACK_SPOT_VALIDITY_PERIODS:
    '/geored/black_spot/validity_periods',
  APIV3_GEORED_BLACK_SPOT_IMPORT_SERVICE: '/geored/black_spot/service/import',
  APIV3_GEORED_BLACK_SPOT_BACK_SPOTS_CONSTRAINTS_TYPES:
    '/geored/black_spot/constraint_types',
  APIV3_GEORED_CONTAINER_CONTAINERS: '/geored/container/containers',
  APIV3_GEORED_CONTAINER_CONTAINERS_UNDELETE:
    '/geored/container/containers/${id}/un_delete',
  APIV3_GEORED_CONTAINER_STATES: '/geored/container_states',
  APIV3_GEORED_CONTAINER_USES: '/geored/container_uses',
  APIV3_GEORED_CONTAINER_USE_CONTAINERS: '/geored/container_use_containers',
  APIV3_GEORED_FLUX_MATERIALS: '/geored/flux_materials',
  APIV3_BIN_COLLECTION_CURRENT_STATES: '/bin_collection/current_states',
  APIV3_GEORED_SERVICE_LIFT_BIN_COLLECTION:
    '/bin_collection/service/lift_bin_collection',
  APIV3_GEORED_POSITIONS_DATE_VEHICLE_POSITION_EXIST:
    '/positions/geored/date_vehicle_position_exist',
  APIV3_GEORED_BIN_TYPES: '/geored/bin/bin_types',
  APIV3_GEORED_BIN_BINS: '/geored/bin/bins',
  APIV3_BIN_COLLECTION_CURRENT_STATES_INDICATORS:
    '/bin_collection/current_states/indicator',
  APIV3_BIN_COLLECTION_SERVICE_LAST_LIFT_BIN_COLLECTION:
    '/bin_collection/service/last_lift_bin_collection',
  APIV3_BIN_COLLECTION_SERVICE_BIN_COLLECTION:
    '/bin_collection/service/lift_bin_collection',
  APIV3_BIN_COLLECTIONS: '/bin_collection/bin_collections',
  APIV3_GEORED_EVENTS_AGGREGATE_EVENT_BIN_COLLECTION:
    '/geored/events/aggregate_event_bin_collections',
  APIV3_TACHOGRAPH_EVENTS: '/service/embedded/get_tachograph_events',
  APIV3_PUBLIC_AUTH_FORGOTTEN_PASSWORD: '/public/auth/forgotten_password',
  APIV3_EVENTS_TO_ORDER_CHECK: '/geored/client/check_event_to_order',
  API_V3_RELEASE_NOTES: '/geored/news',
  APIV3_SERVICE_NEARBY_EVENTS: '/geored/events/service/nearby_events',
  APIV3_GEORED_USERS: '/geored/users',
  APIV3_WEB_DOCUMENTATION: '/proxy/web-documentation',
  APIV3_GEORED_SERVICE_REALTIME: '/geored/real_time/service/real_time',
  APIV3_GEORED_SERVICE_HISTORY_TO_CSV: '/geored/service/history/to_csv',
  APIV3_SEARCH_USER_SEARCH_QUERY_USER:
    '/search-user/function_search_query_users',
  APIV3_GEORED_ROUND_EXECUTIONS: '/geored/round/aggregate/round_executions',
  APIV3_GEORED_SERVICE_VEHICLE_AGGREGATE_GPS:
    '/geored/vehicle/service/aggregate/gps',
}
export default APIUrls

/**
 * @todo Replace hardcoded moment format with these variables in the entire application
 */
export const APIV3RequestDatetimeFormat = 'YYYY-MM-DDTHH:mm:ss' //[Z]
export const APIV2RequestDatetimeFormat = 'YYYY-MM-DD HH:mm:ss'
export const APIV2RequestDateFormat = 'YYYY-MM-DD'
export const APIV2ResponseDatetimeFormat = 'YYYY[-]MM[-]DD HH:mm:ss'
export const APIV2RequestDatetimeFormatWithCustomTime = (time = '00:00:00') =>
  `${APIV2RequestDatetimeFormat.split('HH:mm:ss').join(time)}`

export const apiPlatformResourcesConfiguration = {
  zoneType: APIUrls.APIV3_ZONE_TYPES,
  zone: APIUrls.APIV3_ZONES,
  serviceCartography: APIUrls.APIV3_CLIENT_SERVICE_CARTOGRAPHY,
  calculationMethod: APIUrls.APIV3_GEORED_ROUND_CALCULATION_METHODS,
  activity: APIUrls.APIV3_GEORED_ROUND_ACTIVITIES,
  lastVehicleSrv: APIUrls.APIV3_GEORED_SERVICE_CARTOGRAPHY_LAST_VEHICLES,
  historyVehicleSrv: APIUrls.APIV3_GEORED_SERVICE_HISTORY_VEHICLES,
  historySectionsSrv: APIUrls.APIV3_GEORED_SERVICE_HISTORY_SECTIONS,
  vehicleMessagesSrv: APIUrls.APIV3_GEORED_SERVICE_VEHICLE_MESSAGES,
  predefinedMessage: APIUrls.APIV3_GEORED_MESSAGE_PREDEFINED,
  plannedMessage: APIUrls.APIV3_GEORED_MESSAGE_PLANNING,
  recurrenceDay: APIUrls.APIV3_GEORED_RECURRENCE_RECURRENCE_DAYS,
  recurrenceFrequency: APIUrls.APIV3_GEORED_RECURRENCE_RECURRENCE_FREQUENCIES,
  vehiclePlannedMessage: APIUrls.APIV3_GEORED_MESSAGE_VEHICLE_MESSAGE_PLANNING,
  clientHierarchy: APIUrls.APIV3_CLIENT_HIRERARCHIES,
  blackSpot: APIUrls.APIV3_GEORED_BLACK_SPOT_BACK_SPOTS,
  blackSpotCategory: APIUrls.APIV3_GEORED_BLACK_SPOT_CATEGORIES,
  blackSpotSpeedLimit: APIUrls.APIV3_GEORED_BLACK_SPOT_SPEED_LIMITS,
  geocodingReverseSrv: APIUrls.APIV3_CARTO_GEOCODING_REVERSE,
  geocodingSrv: APIUrls.APIV3_CARTO_GEOCODING,
  geocodingOSMProxySrv: APIUrls.APIV3_CARTO_GEOCODING_PROXY_OSM_NOMINATIM,
  blackSpotConstraint:
    APIUrls.APIV3_GEORED_BLACK_SPOT_BACK_SPOTS_CONSTRAINTS_TYPES,
  blackSpotImport: APIUrls.APIV3_GEORED_BLACK_SPOT_IMPORT_SERVICE,
  blackSpotValidityPeriod: APIUrls.APIV3_GEORED_BLACK_SPOT_VALIDITY_PERIODS,
  container: APIUrls.APIV3_GEORED_CONTAINER_CONTAINERS,
  containerUses: APIUrls.APIV3_GEORED_CONTAINER_USES,
  containerFlow: APIUrls.APIV3_GEORED_FLUX_MATERIALS,
  containerState: APIUrls.APIV3_GEORED_CONTAINER_STATES,
  binCollectionCurrentStates: APIUrls.APIV3_BIN_COLLECTION_CURRENT_STATES,
  serviceLiftBinCollection: APIUrls.APIV3_GEORED_SERVICE_LIFT_BIN_COLLECTION,
  dateVehicleExists: APIUrls.APIV3_GEORED_POSITIONS_DATE_VEHICLE_POSITION_EXIST,
  binTypes: APIUrls.APIV3_GEORED_BIN_TYPES,
  bins: APIUrls.APIV3_GEORED_BIN_BINS,
  binCollectionCurrentStatesIndicators:
    APIUrls.APIV3_BIN_COLLECTION_CURRENT_STATES_INDICATORS,
  binCollectionLastLiftCollections:
    APIUrls.APIV3_BIN_COLLECTION_SERVICE_LAST_LIFT_BIN_COLLECTION,
  binCollectionLiftCollections:
    APIUrls.APIV3_BIN_COLLECTION_SERVICE_BIN_COLLECTION,
  binCollections: APIUrls.APIV3_BIN_COLLECTIONS,
  eventAggregateEventBinCollections:
    APIUrls.APIV3_GEORED_EVENTS_AGGREGATE_EVENT_BIN_COLLECTION,
  tachographEvents: APIUrls.APIV3_TACHOGRAPH_EVENTS,
  publicAuthForgottenPassword: APIUrls.APIV3_PUBLIC_AUTH_FORGOTTEN_PASSWORD,
  georedUsers: APIUrls.APIV3_GEORED_USERS,
  vehicleHistoryToCsv: APIUrls.APIV3_GEORED_SERVICE_HISTORY_TO_CSV,
  searchQueryUsers: APIUrls.APIV3_SEARCH_USER_SEARCH_QUERY_USER,
  georedRoundExecutions: APIUrls.APIV3_GEORED_ROUND_EXECUTIONS,
  georedServiceVehicleAggregateGps:
    APIUrls.APIV3_GEORED_SERVICE_VEHICLE_AGGREGATE_GPS,
}
