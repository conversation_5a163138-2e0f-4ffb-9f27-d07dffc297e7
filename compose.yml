---
services:
  node:
    image: ${NODE_IMAGE}:${NODE_VERSION}
    build:
      context: .
      dockerfile: Dockerfile
      target: node_base
      args:
        - NODE_VERSION=${NODE_VERSION}
    user: 'node'
    working_dir: /home/<USER>/app
    environment:
      - NODE_ENV=production
      - TZ=Europe/Paris
      - VUE_APP_RELEASE_NAME=${RELEASE_NAME}
      - VUE_APP_RELEASE_TIMESTAMP=${RELEASE_TIMESTAMP}
      - SENTRY_WEBPACK_PLUGIN_DEBUG=${SENTRY_WEBPACK_PLUGIN_DEBUG}
    volumes:
      - ./:/home/<USER>/app
    env_file:
      - .env
    networks:
      - georedv3

  dev:
    image: ${NODE_IMAGE}:${NODE_VERSION}
    user: 'node'
    working_dir: /home/<USER>/app
    environment:
      - NODE_ENV=development
      - TZ=Europe/Paris
      - VUE_APP_RELEASE_NAME=${RELEASE_NAME}
      - VUE_APP_RELEASE_TIMESTAMP=${RELEASE_TIMESTAMP}
      - SENTRY_WEBPACK_PLUGIN_DEBUG=${SENTRY_WEBPACK_PLUGIN_DEBUG}
    volumes:
      - ./:/home/<USER>/app
    command: ['npm', 'run', 'serve', '--', '--mode=localhost']
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--spider", "http://localhost:8080"]
      start_period: 2s
      interval: 30s
      timeout: 2s
      retries: 5
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.georedv3.rule=Host(`georedv3.dev.localhost`)'
      - 'traefik.http.services.georedv3-service.loadbalancer.server.port=8080'
      - 'traefik.http.routers.georedv3-secure.rule=Host(`georedv3.dev.localhost`)'
      - 'traefik.http.routers.georedv3-secure.tls=true'
      - 'traefik.docker.network=traefik_proxy'
    networks:
      - georedv3
      - traefik_proxy

  e2e:
    #image: mcr.microsoft.com/playwright:focal
    build:
      context: .
      dockerfile: Dockerfile.e2e
    volumes:
      - ./node_modules:/app/node_modules
      - ./dist:/app/dist
    #  - ./:/home/<USER>/app
    #working_dir: /home/<USER>/app
    networks:
      - georedv3

networks:
  georedv3:
  traefik_proxy:
    external: true
