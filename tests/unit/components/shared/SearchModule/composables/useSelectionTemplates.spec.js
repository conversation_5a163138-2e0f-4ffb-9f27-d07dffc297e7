/**
 * @jest-environment jsdom
 * @fileoverview Test suite for useSelectionTemplates composable
 *
 * This test suite verifies the functionality of the selection templates composable,
 * which handles saving, loading, and applying vehicle selection templates.
 *
 * Test Coverage:
 *
 * 1. Storage Key Generation:
 *    - User and route-specific key generation
 *    - Edge cases (empty values, special characters)
 *
 * 2. Template Management:
 *    - Loading templates from API
 *    - Saving new templates
 *    - Replacing existing templates
 *    - Invalid template handling
 *
 * 3. Template Application:
 *    - Full selection application (vehicles, drivers, circuits, dates)
 *    - Partial selection handling
 *    - Empty selection arrays
 *    - Error scenarios
 *
 * 4. Template Deletion:
 *   - Deleting templates
 *   - Handling errors during deletion
 *
 * Implementation Notes:
 * - Vuex store for state management
 * - Event emission for UI updates
 * - Vue 2 compatibility
 */

import { useSelectionTemplates } from '@/components/shared/SearchModule/composables/useSelectionTemplates'
import * as useSelectionTemplatesComposable from '@/components/shared/SearchModule/composables/useSelectionTemplates'
import * as selectionTemplateService from '@/services/selection-template-service'
import store from '@/store'
import router from '@/router'
import mitt from '@/plugins/mitt.js'

jest.mock('@/store')
jest.mock('@/router')
jest.mock('@/plugins/mitt.js', () => ({
  emit: jest.fn(),
}))

describe('useSelectionTemplates', () => {
  let selectionTemplates

  beforeEach(() => {
    selectionTemplates = useSelectionTemplatesComposable.useSelectionTemplates()
  })

  describe('getStorageKey', () => {
    beforeEach(() => {
      // Reset all mocks before each test
      jest.clearAllMocks()

      // Setup default mock values
      store.getters = {
        'auth/loginNameClientIdEncoded': 'test-user-123',
      }

      router.currentRoute = {
        name: 'test-route',
      }
    })

    it('should generate correct storage key from user and route', () => {
      // Act
      const result = selectionTemplates.getStorageKey()

      // Assert
      expect(result).toBe('vehicle-selection-templates-test-user-123')
    })

    it('should handle empty user ID gracefully', () => {
      // Arrange
      store.getters['auth/loginNameClientIdEncoded'] = ''

      // Act
      const result = selectionTemplates.getStorageKey()

      // Assert
      expect(result).toBe('vehicle-selection-templates-')
    })

    it('should handle special characters in user ID', () => {
      // Arrange
      store.getters['auth/loginNameClientIdEncoded'] = 'user/with@special#chars'
      router.currentRoute.name = 'route/with@special#chars'

      // Act
      const result = selectionTemplates.getStorageKey()

      // Assert
      expect(result).toBe('vehicle-selection-templates-user/with@special#chars')
    })
  })

  describe('getTemplates', () => {
    let fetchUserSearchTemplatesSpy
    beforeEach(() => {
      // Reset all mocks before each test
      jest.clearAllMocks()

      // Setup default mock values
      store.getters = {
        'auth/loginNameClientIdEncoded': 'test-user-123',
      }

      router.currentRoute = {
        name: 'location_module',
      }

      // Spy on console methods
      jest.spyOn(console, 'debug').mockImplementation(() => {})
      jest.spyOn(console, 'error').mockImplementation(() => {})

      fetchUserSearchTemplatesSpy = jest.spyOn(
        selectionTemplateService,
        'fetchUserSearchTemplates'
      )
    })

    afterEach(() => {
      // Restore console methods
      console.debug.mockRestore()
      console.error.mockRestore()

      fetchUserSearchTemplatesSpy.mockRestore()
    })

    it('should return empty array when no templates exist', async () => {
      fetchUserSearchTemplatesSpy.mockResolvedValue([])

      const result = await selectionTemplates.getTemplates()

      // Verify the service method was called
      expect(fetchUserSearchTemplatesSpy).toHaveBeenCalled()

      // Assert
      expect(result).toEqual([])
    })

    it('should return parsed templates when they exist', async () => {
      // Arrange
      const mockTemplates = [
        {
          label: 'Template 1',
          data: { selectedVehiclesIds: [1, 2] },
          id: 1,
          moduleId: 123,
        },
        {
          label: 'Template 2',
          data: { selectedDriversIds: [3, 4] },
          id: 2,
          moduleId: 456,
        },
      ]

      fetchUserSearchTemplatesSpy.mockResolvedValue(mockTemplates)

      const result = await selectionTemplates.getTemplates()

      expect(result).toEqual(mockTemplates)
    })

    it('should filter out invalid templates', async () => {
      // Arrange
      const mockTemplates = [
        {
          label: 'Valid Template',
          data: { selectedVehiclesIds: [1, 2] },
          id: 1,
          moduleId: 123,
        },
        null,
        {
          label: 'Invalid Template',
          id: 2,
          moduleId: 456,
        },
        {
          label: 'Another Valid Template',
          data: { selectedDriversIds: [3, 4] },
          id: 3,
          moduleId: 789,
        },
      ]

      fetchUserSearchTemplatesSpy.mockResolvedValue(mockTemplates)

      // Act
      const result = await selectionTemplates.getTemplates()

      // Assert
      expect(result).toEqual([mockTemplates[0], mockTemplates[3]])
    })

    it('should handle errors gracefully', async () => {
      fetchUserSearchTemplatesSpy.mockRejectedValue(
        new Error('Mocked API Error')
      )

      const selectionTemplates = useSelectionTemplates()

      // Act
      const result = await selectionTemplates.getTemplates()

      // Assert
      expect(result).toEqual([])
      expect(console.error).toHaveBeenCalledWith(
        '[useSelectionTemplates] Error loading templates:',
        expect.any(Error)
      )
    })
  })

  describe('saveTemplate', () => {
    const MOCK_DATE = '2023-01-01T12:00:00.000Z'

    let createUserSearchSpy
    let updateUserSearchSpy

    beforeEach(() => {
      // Reset all mocks before each test
      jest.clearAllMocks()

      const mockTemplates = [
        {
          label: 'Template 1',
          data: { selectedVehiclesIds: [1, 2] },
          id: 1,
          moduleId: 123,
        },
        {
          label: 'Template 2',
          data: { selectedDriversIds: [3, 4] },
          id: 2,
          moduleId: 456,
        },
      ]

      // Setup default mock values
      store.getters = {
        'auth/loginNameClientIdEncoded': 'test-user-123',
      }

      store.getters['selection_templates/getTemplates'] = mockTemplates

      router.currentRoute = {
        name: 'test-route',
        meta: {
          searchTemplateFunctionId: 'test-function-id',
        },
      }

      // Spy on console methods
      jest.spyOn(console, 'debug').mockImplementation(() => {})
      jest.spyOn(console, 'error').mockImplementation(() => {})

      // Mock Date.now() to return a fixed timestamp
      const mockDate = new Date(MOCK_DATE)
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate)

      createUserSearchSpy = jest.spyOn(
        selectionTemplateService,
        'createUserSearch'
      )

      updateUserSearchSpy = jest.spyOn(
        selectionTemplateService,
        'updateUserSearch'
      )
    })

    afterEach(() => {
      // Restore console methods and Date
      console.debug.mockRestore()
      console.error.mockRestore()
      jest.restoreAllMocks()
    })

    it('should save a new template when it does not exist', async () => {
      // Arrange
      const templateName = 'New Template'
      const selection = {
        selectedVehiclesIds: [1, 2],
        selectedDriversIds: [3, 4],
      }
      createUserSearchSpy.mockResolvedValue({})

      // Act
      await selectionTemplates.saveTemplate(templateName, selection)

      expect(createUserSearchSpy).toHaveBeenCalledTimes(1)
      expect(createUserSearchSpy).toHaveBeenCalledWith({
        label: 'New Template',
        data: ['selectedVehiclesIds: [1,2]', 'selectedDriversIds: [3,4]'],
        functionId: 'test-function-id',
      })
    })

    it('should update existing template when it already exists', async () => {
      const templateName = 'Template 1'
      const selection = {
        selectedVehiclesIds: [11, 12],
      }

      updateUserSearchSpy.mockResolvedValue({})

      await selectionTemplates.saveTemplate(templateName, selection)

      expect(updateUserSearchSpy).toHaveBeenCalledTimes(1)
      expect(updateUserSearchSpy).toHaveBeenCalledWith(1, {
        label: 'Template 1',
        data: ['selectedVehiclesIds: [11,12]'],
        functionId: 'test-function-id',
      })
    })

    it('should throw error when no selection is provided', async () => {
      // Arrange
      const selectionTemplates = useSelectionTemplates()
      const templateName = 'Invalid Template'

      // Act & Assert
      await expect(
        selectionTemplates.saveTemplate(templateName, null)
      ).rejects.toThrow('No selection state provided')
    })

    it('should handle errors', async () => {
      const templateName = 'New Template'
      const selection = {
        selectedVehiclesIds: [1, 2],
        selectedDriversIds: [3, 4],
      }

      const mockedError = new Error('Mocked API Error')
      createUserSearchSpy.mockRejectedValue(mockedError)

      // Act
      await expect(
        selectionTemplates.saveTemplate(templateName, selection)
      ).rejects.toThrow(mockedError)

      // Assert
      expect(console.error).toHaveBeenCalledWith(
        'Error saving selection template:',
        expect.any(Error)
      )
    })
  })

  describe('applyTemplate', () => {
    beforeEach(() => {
      // Reset all mocks before each test
      jest.clearAllMocks()

      // Setup default mock values
      store.getters = {
        'auth/loginNameClientIdEncoded': 'test-user-123',
      }

      // Mock store mutations
      store.commit = jest.fn()

      router.currentRoute = {
        name: 'test-route',
      }

      // Spy on console methods
      jest.spyOn(console, 'debug').mockImplementation(() => {})
      jest.spyOn(console, 'error').mockImplementation(() => {})
    })

    afterEach(() => {
      // Restore console methods
      console.debug.mockRestore()
      console.error.mockRestore()
    })

    it('should apply template with all selection types', async () => {
      // Arrange
      const template = {
        label: 'Complete Template',
        data: {
          selectedVehiclesIds: [1, 2],
          selectedDriversIds: [3, 4],
          selectedCircuitsIds: [5, 6],
          selectedDateRanges: [{ start: '2023-01-01', end: '2023-01-31' }],
        },
      }
      // Act
      await selectionTemplates.applyTemplate(template)

      // Assert
      // Verify clear selection was called first
      expect(store.commit).toHaveBeenCalledWith('search_module/clearSelection')

      // Verify each vehicle ID was selected
      template.data.selectedVehiclesIds.forEach((id) => {
        expect(store.commit).toHaveBeenCalledWith(
          'search_module/selectVehicle',
          id
        )
      })

      // Verify each driver ID was selected
      template.data.selectedDriversIds.forEach((id) => {
        expect(store.commit).toHaveBeenCalledWith(
          'search_module/selectDriver',
          id
        )
      })

      // Verify each circuit ID was selected
      template.data.selectedCircuitsIds.forEach((id) => {
        expect(store.commit).toHaveBeenCalledWith(
          'search_module/selectCircuit',
          id
        )
      })

      // Verify date ranges were selected
      template.data.selectedDateRanges.forEach((range) => {
        expect(store.commit).toHaveBeenCalledWith(
          'search_module/selectDateRange',
          range
        )
      })

      // Verify refresh event was emitted
      expect(mitt.emit).toHaveBeenCalledWith('search_module/refresh')
    })

    it('should handle template with partial selection data', async () => {
      // Arrange
      const template = {
        label: 'Partial Template',
        data: {
          selectedVehiclesIds: [1, 2],
          // No drivers or circuits
          selectedDateRanges: [{ start: '2023-01-01', end: '2023-01-31' }],
        },
      }
      // Act
      await selectionTemplates.applyTemplate(template)

      // Assert
      expect(store.commit).toHaveBeenCalledWith('search_module/clearSelection')

      // Verify only vehicles and date ranges were selected
      template.data.selectedVehiclesIds.forEach((id) => {
        expect(store.commit).toHaveBeenCalledWith(
          'search_module/selectVehicle',
          id
        )
      })

      template.data.selectedDateRanges.forEach((range) => {
        expect(store.commit).toHaveBeenCalledWith(
          'search_module/selectDateRange',
          range
        )
      })

      // Verify no driver or circuit selections were made
      expect(store.commit).not.toHaveBeenCalledWith(
        expect.stringMatching(/selectDriver|selectCircuit/),
        expect.anything()
      )

      expect(mitt.emit).toHaveBeenCalledWith('search_module/refresh')
    })

    it('should throw error for invalid template', async () => {
      // Arrange
      const invalidTemplate = {
        label: 'Invalid Template',
        // Missing selection object
      }
      // Act & Assert
      await expect(
        selectionTemplates.applyTemplate(invalidTemplate)
      ).rejects.toThrow('Invalid template or missing selection')

      // Verify no mutations were called
      expect(store.commit).not.toHaveBeenCalled()
      expect(mitt.emit).not.toHaveBeenCalled()
    })

    it('should throw error for null template', async () => {
      // Arrange
      // Act & Assert
      await expect(selectionTemplates.applyTemplate(null)).rejects.toThrow(
        'Invalid template or missing selection'
      )

      expect(store.commit).not.toHaveBeenCalled()
      expect(mitt.emit).not.toHaveBeenCalled()
    })

    it('should handle empty arrays in selection', async () => {
      // Arrange
      const template = {
        label: 'Empty Template',
        data: {
          selectedVehiclesIds: [],
          selectedDriversIds: [],
          selectedCircuitsIds: [],
          selectedDateRanges: [],
        },
      }
      // Act
      await selectionTemplates.applyTemplate(template)

      // Assert
      expect(store.commit).toHaveBeenCalledWith('search_module/clearSelection')
      expect(store.commit).toHaveBeenCalledTimes(1) // Only clearSelection should be called
      expect(mitt.emit).toHaveBeenCalledWith('search_module/refresh')
    })

    it('should handle store mutation errors', async () => {
      // Arrange
      const template = {
        label: 'Error Template',
        data: {
          selectedVehiclesIds: [1],
        },
      }
      const error = new Error('Store mutation failed')
      store.commit.mockImplementation(() => {
        throw error
      })
      // Act & Assert
      await expect(selectionTemplates.applyTemplate(template)).rejects.toThrow(
        'Store mutation failed'
      )

      expect(console.error).toHaveBeenCalledWith(
        'Error applying selection template:',
        error
      )
    })
  })

  describe('deleteTemplate', () => {
    let deleteUserSearchSpy
    let deleteTemplate
    let fetchUserSearchTemplatesSpy

    beforeEach(() => {
      jest.clearAllMocks()

      // Mock getAllTemplates
      deleteUserSearchSpy = jest.spyOn(
        selectionTemplateService,
        'deleteUserSearch'
      )
      fetchUserSearchTemplatesSpy = jest.spyOn(
        selectionTemplateService,
        'fetchUserSearchTemplates'
      )
    })

    it('should delete the template and return updated templates', async () => {
      // Arrange
      const templateId = 'template-1'
      fetchUserSearchTemplatesSpy.mockReturnValue([
        { id: 'template-1', label: 'Template 1' },
        { id: 'template-2', label: 'Template 2' },
      ])
      const updatedTemplates = [{ id: 'template-2', label: 'Template 2' }]

      deleteUserSearchSpy.mockResolvedValue()

      // Act
      const result = await selectionTemplates.deleteTemplate(templateId)

      // Assert
      expect(deleteUserSearchSpy).toHaveBeenCalledWith(templateId)
      expect(result).toEqual(updatedTemplates)
    })

    it('should handle errors', async () => {
      const templateId = 'template-1'
      const error = new Error('Mocked API Error')

      const mockedError = new Error('Mocked API Error')
      fetchUserSearchTemplatesSpy.mockReturnValue([
        { id: 'template-1', label: 'Template 1' },
        { id: 'template-2', label: 'Template 2' },
      ])
      deleteUserSearchSpy.mockRejectedValue(mockedError)

      // Act
      await expect(
        selectionTemplates.deleteTemplate(templateId)
      ).rejects.toThrow(error)
    })
  })

  describe('getTemplatesFromLocalStorage', () => {
    let getTemplatesFromLocalStorage
    let getStorageKeySpy

    beforeEach(() => {
      const { getTemplatesFromLocalStorage: getTemplates, getStorageKey } =
        useSelectionTemplates()
      getTemplatesFromLocalStorage = getTemplates
      getStorageKeySpy = jest.spyOn({ getStorageKey }, 'getStorageKey')

      // Mock localStorage
      Object.defineProperty(global, 'localStorage', {
        value: {
          getItem: jest.fn(),
          setItem: jest.fn(),
          removeItem: jest.fn(),
          clear: jest.fn(),
        },
        writable: true,
      })

      jest.spyOn(console, 'debug').mockImplementation(() => {})
      jest.spyOn(console, 'error').mockImplementation(() => {})
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    it('should return an empty array when localStorage is empty', async () => {
      getStorageKeySpy.mockReturnValue('test-key')
      localStorage.getItem.mockReturnValue(null)

      const result = await getTemplatesFromLocalStorage()

      expect(result).toEqual([])
      expect(console.debug).toHaveBeenCalledWith(
        9,
        '[useSelectionTemplates] Raw templates from storage:',
        null
      )
    })

    it('should return parsed templates when localStorage contains valid JSON', async () => {
      getStorageKeySpy.mockReturnValue('test-key')
      const mockTemplates = [{ name: 'Template 1' }]
      localStorage.getItem.mockReturnValue(JSON.stringify(mockTemplates))

      const result = await getTemplatesFromLocalStorage()

      expect(result).toEqual(mockTemplates)
      expect(console.debug).toHaveBeenCalledWith(
        9,
        '[useSelectionTemplates] Final parsed templates:',
        mockTemplates
      )
    })

    it('should handle double-stringified JSON in localStorage', async () => {
      getStorageKeySpy.mockReturnValue('test-key')
      const mockTemplates = [{ name: 'Template 1' }]
      localStorage.getItem.mockReturnValue(
        JSON.stringify(JSON.stringify(mockTemplates))
      )

      const result = await getTemplatesFromLocalStorage()

      expect(result).toEqual(mockTemplates)
      expect(console.debug).toHaveBeenCalledWith(
        9,
        '[useSelectionTemplates] Second parse result:',
        mockTemplates
      )
    })

    it('should return an empty array and log an error for invalid JSON', async () => {
      getStorageKeySpy.mockReturnValue('test-key')
      localStorage.getItem.mockReturnValue('invalid-json')

      const result = await getTemplatesFromLocalStorage()

      expect(result).toEqual([])
      expect(console.error).toHaveBeenCalledWith(
        '[useSelectionTemplates] Error parsing templates:',
        expect.any(SyntaxError)
      )
    })
  })
})
