import { getVehicleChrono } from '@/services/chrono-service.js'
import chronoStore from '@/store/location_module/chrono.js'
import positionMarkersService from '@/services/position-markers-service.js'
jest.mock('@/services/chrono-service.js')

describe('getVehicleChrono', () => {
  // Create a mock store for the position markers service
  const mockStore = {
    getters: {
      'map_options/speedFilter': { value: null, condition: null },
    },
  }

  beforeEach(() => {
    // Set up the mock store for position markers service
    positionMarkersService._setStoreForTesting(mockStore)
    // Clear any existing positions
    positionMarkersService.clear()
  })

  afterEach(() => {
    // Clean up after each test
    positionMarkersService._resetStore()
  })

  it('calls getVehicleChrono with correct params', async () => {
    const params = {
      vehicleId: 123,
      date: '2022-01-01',
      historyEndDatetimeStamp: '2022-01-31',
      getVehicleChrono,
    }

    const rootGetters = {
      'simpliciti_map/vehiclePositionMarkers': [],
    }

    let payload = {
      vehicleId: params.vehicleId,
      startDate: params.date,
      endDate: new Date(params.historyEndDatetimeStamp),
    }
    getVehicleChrono.mockClear()

    await chronoStore.actions.getVehicleChrono(
      {
        commit: () => {},
        rootGetters: rootGetters,
      },
      params
    )
    expect(getVehicleChrono).toHaveBeenCalledTimes(1)
    expect(getVehicleChrono).toHaveBeenCalledWith(payload, 'v3')
  })
})
