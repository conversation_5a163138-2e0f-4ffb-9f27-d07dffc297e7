import { sortByDate, flattenArrayOfPairs } from '@/utils/array.js'

describe('sortByDate', () => {
  it('sorts an array of objects by a specified date property', () => {
    const arr = [
      { id: 1, createdAt: '2022-01-01T12:00:00.000Z' },
      { id: 2, createdAt: '2022-01-05T12:00:00.000Z' },
      { id: 3, createdAt: '2022-01-03T12:00:00.000Z' },
    ]

    const sortedArr = sortByDate(arr, 'createdAt')

    expect(sortedArr).toEqual([
      { id: 2, createdAt: '2022-01-05T12:00:00.000Z' },
      { id: 3, createdAt: '2022-01-03T12:00:00.000Z' },
      { id: 1, createdAt: '2022-01-01T12:00:00.000Z' },
    ])
  })

  // it('clones the original array when clone option is true', () => {
  //   const originalArr = [
  //     { id: 1, createdAt: '2022-01-01T12:00:00.000Z' },
  //     { id: 2, createdAt: '2022-01-05T12:00:00.000Z' },
  //     { id: 3, createdAt: '2022-01-03T12:00:00.000Z' },
  //   ]
  //
  //   const sortedArr = sortByDate(originalArr, 'createdAt', true)
  //
  //   expect(Object.is(originalArr, sortedArr)).toBe(false);
  // })

  it('throws an error if the date property is invalid', () => {
    const arr = [
      { id: 1, createdAt: ' invalid date ' },
      { id: 2, createdAt: '2022-01-05T12:00:00.000Z' },
    ]

    expect(() => sortByDate(arr, 'createdAt')).toThrowError()
  })

  it('sorts an array of Date objects directly', () => {
    const arr = [
      new Date('2022-01-01T12:00:00.000Z'),
      new Date('2022-01-05T12:00:00.000Z'),
      new Date('2022-01-03T12:00:00.000Z'),
    ]

    const sortedArr = sortByDate(arr, '', 'asc') // Sorting in ascending order

    expect(sortedArr).toEqual([
      new Date('2022-01-01T12:00:00.000Z'),
      new Date('2022-01-03T12:00:00.000Z'),
      new Date('2022-01-05T12:00:00.000Z'),
    ])
  })
})

describe('flattenArrayOfPairs', () => {
  it('should flatten a valid array of pairs', () => {
    const input = [
      [1, 2],
      [3, 4],
      [5, 6],
    ]
    const result = flattenArrayOfPairs(input)
    expect(result).toEqual([1, 2, 3, 4, 5, 6])
  })

  it('should skip invalid pairs', () => {
    const input = [[1, 2], [3], 'not-a-pair', [4, 5, 6]]
    const result = flattenArrayOfPairs(input)
    expect(result).toEqual([1, 2])
  })

  it('should return empty array if input is not an array', () => {
    const result = flattenArrayOfPairs(null)
    expect(result).toEqual([])
  })

  it('should return empty array if input is an empty array', () => {
    const result = flattenArrayOfPairs([])
    expect(result).toEqual([])
  })

  it('should log a warning if input is not an array', () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {})
    flattenArrayOfPairs('string')
    expect(consoleSpy).toHaveBeenCalledWith(
      'flattenArrayOfPairs: Expected an array of pairs, received:',
      'string'
    )
    consoleSpy.mockRestore()
  })
})
