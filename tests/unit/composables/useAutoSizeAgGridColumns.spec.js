import { ref } from 'vue'
import { useAutoSizeAgGridColumns } from '@/composables/useAutoSizeAgGridColumns'

describe('useAutoSizeAgGridColumns', () => {
  let mockGridApi
  let mockColumnApi
  let currentDomLayout

  beforeEach(() => {
    mockGridApi = ref({
      setDomLayout: jest.fn(),
    })

    mockColumnApi = ref({
      getAllGridColumns: jest.fn(() => [
        { getColId: () => 'col1' },
        { getColId: () => 'col2' },
      ]),
      autoSizeColumns: jest.fn(),
    })

    currentDomLayout = ref('normal')
  })

  it('should do nothing if columnApi or gridApi is missing', () => {
    const { autoSizeAllColumns } = useAutoSizeAgGridColumns(
      ref(null),
      ref(null),
      currentDomLayout
    )

    expect(() => autoSizeAllColumns()).not.toThrow()
  })

  it('should call autoSizeColumns with all column IDs', () => {
    const { autoSizeAllColumns } = useAutoSizeAgGridColumns(
      mockGridApi,
      mockColumnApi,
      currentDomLayout
    )

    autoSizeAllColumns()

    expect(mockColumnApi.value.getAllGridColumns).toHaveBeenCalled()
    expect(mockColumnApi.value.autoSizeColumns).toHaveBeenCalledWith(
      ['col1', 'col2'],
      false
    )
  })
})
