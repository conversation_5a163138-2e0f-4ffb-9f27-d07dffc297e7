jest.mock('@/store', () => ({
  __esModule: true,
  default: {
    getters: {
      'search_module/getCircuitCategories': [],
      'search_module/getCircuits': [],
    },
  },
}))

jest.mock('@/utils/dates', () => {
  const actual = jest.requireActual('@/utils/dates')

  return {
    ...actual,
    dateRangeToChunks: jest.fn(),
  }
})

import useOperationalStatisticsData from '@/composables/operationalStatistics/useOperationalStatisticsData'
import store from '@/store'
import moment from 'moment'
import { dateRangeToChunks } from '@/utils/dates'

describe('useOperationalStatisticsData', () => {
  let getSelectedCircuitIds
  let prepareApiParamsDateRange
  let prepareTasks
  let getDateChunks

  beforeAll(() => {
    const composable = useOperationalStatisticsData()
    getSelectedCircuitIds = composable.getSelectedCircuitIds
    prepareApiParamsDateRange = composable.prepareApiParamsDateRange
    prepareTasks = composable.prepareTasks
    getDateChunks = composable.getDateChunks
  })

  describe('getSelectedCircuitIds', () => {
    beforeEach(() => {
      store.getters['search_module/getCircuitCategories'] = []
      store.getters['search_module/getCircuits'] = []
    })

    it('returns empty array if activeTab is not 0', () => {
      const result = getSelectedCircuitIds(1, [{ id: 1 }], [{ id: 10 }])
      expect(result).toEqual([])
    })

    it('returns selected circuit ids if no categories are selected', () => {
      const result = getSelectedCircuitIds(0, [{ id: 1 }, { id: 2 }], [])
      expect(result).toEqual([1, 2])
    })

    it('includes circuits from selected categories', () => {
      const store = require('@/store').default
      store.getters['search_module/getCircuitCategories'] = [
        {
          id: 10,
          circuits: [{ id: 3 }, { id: 4 }],
        },
      ]

      const result = getSelectedCircuitIds(0, [{ id: 1 }], [{ id: 10 }])
      expect(result.sort()).toEqual([1, 3, 4].sort())
    })
  })

  describe('prepareApiParamsDateRange', () => {
    it('returns empty object if no dates are provided', () => {
      const result = prepareApiParamsDateRange({})
      expect(result).toEqual({})
    })

    it('returns only start date when endDate is missing', () => {
      const result = prepareApiParamsDateRange({
        startDate: '2023-06-01T10:00:00Z',
      })
      expect(result).toEqual({
        start: '2023-06-01T10:00:00',
      })
    })

    it('returns only end date when startDate is missing', () => {
      const result = prepareApiParamsDateRange({ endDate: '2023-06-01' })
      expect(result).toEqual({
        end: '2023-06-01T23:59:59',
      })
    })

    it('formats both start and end dates correctly', () => {
      const result = prepareApiParamsDateRange({
        startDate: '2023-06-01T08:15:30Z',
        endDate: '2023-06-01',
      })
      expect(result).toEqual({
        start: '2023-06-01T08:15:30',
        end: '2023-06-01T23:59:59',
      })
    })
  })

  describe('prepareTasks', () => {
    it('creates combinations of circuit and vehicle batches', () => {
      const circuits = Array.from({ length: 15 }, (_, i) => i + 1) // [1..15]
      const vehicles = Array.from({ length: 150 }, (_, i) => i + 101) // [101..250]
      const tasks = prepareTasks(circuits, vehicles, {})

      expect(tasks.length).toBe(4)

      expect(tasks[0]).toHaveProperty('roundId')
      expect(tasks[0]).toHaveProperty('vehicleId')

      expect(tasks[0].roundId.length).toBe(10)

      expect(tasks[2].roundId.length).toBe(5)

      expect(tasks[0].vehicleId.length).toBe(100)

      expect(tasks[1].vehicleId.length).toBe(50)
    })

    it('creates tasks with circuit batches only when no vehicles', () => {
      const circuits = Array.from({ length: 15 }, (_, i) => i + 1)
      const vehicles = []
      const tasks = prepareTasks(circuits, vehicles, {})

      expect(tasks.length).toBe(2)

      tasks.forEach((task) => {
        expect(task).toHaveProperty('roundId')
        expect(task).not.toHaveProperty('vehicleId')
      })
    })

    it('creates tasks with vehicle batches only when no circuits', () => {
      const circuits = []
      const vehicles = Array.from({ length: 150 }, (_, i) => i + 101)
      const tasks = prepareTasks(circuits, vehicles, {})

      expect(tasks.length).toBe(2)

      tasks.forEach((task) => {
        expect(task).toHaveProperty('vehicleId')
        expect(task).not.toHaveProperty('roundId')
      })
    })

    it('creates one empty task if no circuits or vehicles but date range is provided', () => {
      const tasks = prepareTasks([], [], { start: '2020-01-01' })

      expect(tasks.length).toBe(1)
      expect(tasks[0]).toEqual({})
    })

    it('returns empty array if no inputs at all', () => {
      const tasks = prepareTasks([], [], {})

      expect(tasks).toEqual([])
    })
  })

  describe('getDateChunks', () => {
    beforeEach(() => {
      dateRangeToChunks.mockClear()
    })

    it('returns chunks from dateRangeToChunks if startDate and endDate provided', () => {
      const dateRange = {
        startDate: '2023-01-01',
        endDate: '2023-01-15',
      }
      const chunkSize = 7

      const mockChunks = [
        { startDate: '2023-01-01', endDate: '2023-01-07' },
        { startDate: '2023-01-08', endDate: '2023-01-15' },
      ]
      dateRangeToChunks.mockReturnValue(mockChunks)

      const result = getDateChunks(dateRange, chunkSize)

      expect(dateRangeToChunks).toHaveBeenCalledWith(
        moment(dateRange.startDate),
        moment(dateRange.endDate),
        chunkSize
      )
      expect(result).toEqual(mockChunks)
    })

    it('returns [{ startDate: null, endDate: null }] if startDate or endDate is missing', () => {
      expect(
        getDateChunks({ startDate: null, endDate: '2023-01-10' }, 5)
      ).toEqual([{ startDate: null, endDate: null }])

      expect(
        getDateChunks({ startDate: '2023-01-01', endDate: null }, 5)
      ).toEqual([{ startDate: null, endDate: null }])

      expect(getDateChunks({}, 5)).toEqual([{ startDate: null, endDate: null }])
    })
  })
})
