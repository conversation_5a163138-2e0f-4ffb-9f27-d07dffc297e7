import { nextTick } from 'vue'
import store from '@/store'
import useOperationalStatisticsColumns, {
  arrayFieldFormatter,
} from '@/composables/operationalStatistics/useOperationalStatisticsColumns'

// Stub i18n translation function
jest.mock('@/i18n', () => ({
  __esModule: true,
  default: {
    t: (key) => `__${key}__`,
  },
}))

// Stub date utils
jest.mock('@/utils/dates', () => ({
  adjustDateWithTimezone: (v) => v,
  formatDatetime: (v) => `formatted-${v}`,
  formatTime: (v) => `formattedTime-${v}`,
}))

function findColumnByField(columnDefs, field) {
  return columnDefs
    .flatMap((group) => group.children || [])
    .find((col) => col.field === field)
}

describe('useOperationalStatisticsColumns', () => {
  beforeEach(() => {
    store.state.operationalStatistics.activeTab = 0
  })

  function findGroupById(columnDefs, groupId) {
    return columnDefs.find((group) => group.groupId === groupId)
  }

  it('returns expected column groups and columns for activeTab = 0', () => {
    store.state.operationalStatistics.activeTab = 0
    const { columnDefs } = useOperationalStatisticsColumns()

    // Check main groups count for tab 0
    expect(columnDefs.value.length).toBe(6)

    // Check generalInfos group
    const generalGroup = findGroupById(columnDefs.value, 'generalInfos_tab0')
    expect(generalGroup).toBeTruthy()
    expect(generalGroup.children.length).toBe(6)
    expect(generalGroup.children[0].field).toBe('executionStart')
    expect(generalGroup.children[0].colId).toBe(
      'generalInfos_executionStart_tab0'
    )

    // Check crewInfos group
    const crewGroup = findGroupById(columnDefs.value, 'crewInfos_tab0')
    expect(crewGroup).toBeTruthy()
    expect(crewGroup.children.length).toBe(4)

    const plannedDriverCol = crewGroup.children.find(
      (c) => c.colId === 'crewInfos_plannedDriver_tab0'
    )
    expect(plannedDriverCol).toBeTruthy()
    expect(typeof plannedDriverCol.valueGetter).toBe('function')

    const declaredDriverCol = crewGroup.children.find(
      (c) => c.colId === 'crewInfos_declaredDriver_tab0'
    )
    expect(declaredDriverCol).toBeTruthy()
    expect(typeof declaredDriverCol.valueGetter).toBe('function')

    const identifiedDriverCol = crewGroup.children.find(
      (c) => c.colId === 'crewInfos_identifiedDriver_tab0'
    )
    expect(identifiedDriverCol).toBeTruthy()
    expect(identifiedDriverCol.valueGetter).toBe('identificationDriver')

    const ripersCol = crewGroup.children.find(
      (c) => c.colId === 'crewInfos_ripers_tab0'
    )
    expect(ripersCol).toBeTruthy()
    expect(typeof ripersCol.valueGetter).toBe('function')

    //Check roundInfos columns
    const roundGroup = findGroupById(columnDefs.value, 'roundInfos_tab0')
    expect(roundGroup).toBeTruthy()
    expect(roundGroup.children.length).toBe(13)

    const realizationRateCol = roundGroup.children.find(
      (c) => c.colId === 'roundInfos_realizationRate_tab0'
    )
    expect(realizationRateCol).toBeTruthy()
    expect(realizationRateCol.field).toBe('realizationRate')
    expect(typeof realizationRateCol.valueFormatter).toBe('function')

    const stopNumberCol = roundGroup.children.find(
      (c) => c.colId === 'roundInfos_stopNumber_tab0'
    )
    expect(stopNumberCol).toBeTruthy()
    expect(stopNumberCol.field).toBe('stopNumber')
    expect(stopNumberCol.cellStyle).toEqual({ 'text-align': 'right' })

    // Check sensorsInfos group for tab 0
    const sensorsGroupTab0 = findGroupById(
      columnDefs.value,
      'sensorsInfos_tab0'
    )
    expect(sensorsGroupTab0).toBeTruthy()
    expect(sensorsGroupTab0.children.length).toBe(5)

    const totalLiftCol = sensorsGroupTab0.children.find(
      (c) => c.colId === 'sensorsInfos_totalLiftNumber_tab0'
    )
    expect(totalLiftCol).toBeTruthy()
    expect(totalLiftCol.field).toBe('liftNumber')
    expect(totalLiftCol.cellStyle).toEqual({ 'text-align': 'right' })

    const combinedLiftCol = sensorsGroupTab0.children.find(
      (c) => c.colId === 'sensorsInfos_combinedLiftNumber_tab0'
    )
    expect(combinedLiftCol).toBeTruthy()
    expect(combinedLiftCol.field).toBe('bothSideNumber')

    //Emptying/weighing
    const group = findGroupById(columnDefs.value, 'emptyingWeighingInfos_tab0')
    expect(group).toBeTruthy()
    expect(group.children.length).toBe(3)

    const emptyingNumberCol = group.children.find(
      (c) => c.colId === 'emptyingNumber_tab0'
    )
    expect(emptyingNumberCol).toBeTruthy()
    expect(emptyingNumberCol.field).toBe('emptyingNumber')
    expect(emptyingNumberCol.cellStyle).toEqual({ 'text-align': 'right' })

    const totalWeightDeclaredCol = group.children.find(
      (c) => c.colId === 'totalWeightDeclared_tab0'
    )
    expect(totalWeightDeclaredCol).toBeTruthy()
    expect(totalWeightDeclaredCol.field).toBe('fdrWeight')
    expect(totalWeightDeclaredCol.valueFormatter).toBeInstanceOf(Function)

    const totalWeightWeighedCol = group.children.find(
      (c) => c.colId === 'totalWeightWeighed_tab0'
    )
    expect(totalWeightWeighedCol).toBeTruthy()
    expect(totalWeightWeighedCol.field).toBe('weight')
  })

  it('returns expected column groups and columns for activeTab = 1', () => {
    store.state.operationalStatistics.activeTab = 1
    const { columnDefs } = useOperationalStatisticsColumns()

    expect(columnDefs.value.length).toBe(5)

    const generalGroup = findGroupById(columnDefs.value, 'generalInfos_tab1')
    expect(generalGroup).toBeTruthy()
    expect(generalGroup.children.length).toBe(3)
    expect(generalGroup.children[0].field).toBe('start')
    expect(generalGroup.children[0].colId).toBe(
      'generalInfos_executionStart_tab1'
    )

    // Sensors group for tab 1
    const sensorsGroupTab1 = findGroupById(
      columnDefs.value,
      'sensorsInfos_tab1'
    )
    expect(sensorsGroupTab1).toBeTruthy()
    expect(sensorsGroupTab1.children.length).toBe(5)

    const leftLiftCol = sensorsGroupTab1.children.find(
      (c) => c.colId === 'sensorsInfos_leftLiftNumber_tab1'
    )
    expect(leftLiftCol).toBeTruthy()
    expect(leftLiftCol.field).toBe('leftSideNumber')
    expect(leftLiftCol.cellStyle).toEqual({ 'text-align': 'right' })

    //Emptying/weighing
    const emptyingWeighingGroup = findGroupById(
      columnDefs.value,
      'emptyingWeighingInfos_tab1'
    )
    expect(emptyingWeighingGroup).toBeTruthy()
    expect(emptyingWeighingGroup.children.length).toBe(2)

    const emptyingNumberCol = emptyingWeighingGroup.children.find(
      (c) => c.colId === 'emptyingNumber_tab1'
    )
    expect(emptyingNumberCol).toBeTruthy()
    expect(emptyingNumberCol.field).toBe('emptyingNumber')

    const totalWeightWeighedCol = emptyingWeighingGroup.children.find(
      (c) => c.colId === 'totalWeightWeighed_tab1'
    )
    expect(totalWeightWeighedCol).toBeTruthy()
    expect(totalWeightWeighedCol.field).toBe('weight')

    // Use the actual function to get the expected route group
    const routeGroup = findGroupById(columnDefs.value, 'routeInfos_tab1')
    expect(routeGroup).toBeTruthy()
    expect(routeGroup.children.length).toBe(9)

    const routeCols = [
      { colId: 'routeTime_tab1', field: 'time' },
      { colId: 'routeDistance_tab1', field: 'distance' },
      { colId: 'activityTime_tab1', field: 'activiteTime' },
      { colId: 'activityDistance_tab1', field: 'activiteDistance' },
      { colId: 'breakTime_tab1', field: 'breakTime' },
      { colId: 'stopTime_tab1', field: 'stopTime' },
      { colId: 'reverseTime_tab1', field: 'reverseTime' },
      { colId: 'reverseDistance_tab1', field: 'reverseDistance' },
      { colId: 'consumption_tab1', field: 'consumption' },
    ]

    routeCols.forEach(({ colId, field }) => {
      const col = routeGroup.children.find((c) => c.colId === colId)
      expect(col).toBeTruthy()
      expect(col.field).toBe(field)
      expect(col.headerComponentParams.sortable).toBe(true)
      expect(col.headerComponentParams.hasTooltip).toBe(true)
      expect(typeof col.headerComponentParams.headerTooltip).toBe('string')
    })
  })

  it('returns expected anomaliesInfos group columns for activeTab', () => {
    const tab = store.state.operationalStatistics.activeTab
    const { columnDefs } = useOperationalStatisticsColumns()

    const group = columnDefs.value.find(
      (g) => g.groupId === `anomaliesInfos_tab${tab}`
    )
    expect(group).toBeTruthy()
    expect(group.children.length).toBe(1)

    const anomalyNumberCol = group.children[0]
    expect(anomalyNumberCol.colId).toBe(`anomalyNumber_tab${tab}`)
    expect(anomalyNumberCol.field).toBe('anomalyNumber')
    expect(anomalyNumberCol.cellStyle).toEqual({ 'text-align': 'right' })
    expect(anomalyNumberCol.headerComponentParams).toHaveProperty(
      'sortable',
      true
    )
  })
})

describe('arrayFieldFormatter', () => {
  it('joins trimmed strings with default separator', () => {
    const getter = arrayFieldFormatter('field')
    const params = { data: { field: [' a ', 'b', ' c '] } }
    expect(getter(params)).toBe('a,b,c')
  })

  it('returns empty string if field is missing or not an array', () => {
    expect(arrayFieldFormatter('missing')({ data: {} })).toBe('')
    expect(
      arrayFieldFormatter('notArray')({ data: { notArray: 'nope' } })
    ).toBe('')
  })

  it('joins strings with custom separator', () => {
    const getter = arrayFieldFormatter('field', ' | ')
    const params = { data: { field: ['x', 'y'] } }
    expect(getter(params)).toBe('x | y')
  })
})
