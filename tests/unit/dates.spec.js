import {
  getConsecutiveDays,
  formatDateInternal,
  getDaysDifferenceBetweenMinMaxDates,
  getFormattedDurationFromSeconds,
  checkDateRangeCollisions,
  dateRangeToChunks,
  formatTimeWithSeconds,
  formatSeconds,
  formatTime,
  getPastDatesUsingConsecutiveDays,
  isMomentToday,
  stringDatetimeToDate,
  getRelativeFormattedPastDate,
  filterValidDates,
  isIsoDate,
  hasTodayInRanges,
  setTime,
  sortDatesAscending,
  updateTimeIfBeforeOrAfter,
  getFormattedSelectedTimeRangeStringByDate,
  adjustDateWithTimezone,
  stripMillisecondsFromDate,
  normalizeDateToMidnight,
  isDateAfterDate,
  splitDateRangeByDay,
  doRangesOverlap,
} from '@/utils/dates'
import moment from 'moment'

const timeSeparator = ':'

describe('dateRangeToChunks', () => {
  it('splits a range into chunks of max size', () => {
    const result = dateRangeToChunks(
      moment('2022-01-01'),
      moment('2022-01-05'),
      2
    )
    expect(result[0].startDate.format('DD/MM/YYYY HH:mm')).toStrictEqual(
      '01/01/2022 00:00'
    )
    expect(result[0].endDate.format('DD/MM/YYYY HH:mm')).toStrictEqual(
      '02/01/2022 23:59'
    )
    expect(result[1].startDate.format('DD/MM/YYYY HH:mm')).toStrictEqual(
      '03/01/2022 00:00'
    )
    expect(result[1].endDate.format('DD/MM/YYYY HH:mm')).toStrictEqual(
      '04/01/2022 23:59'
    )
    expect(result[2].startDate.format('DD/MM/YYYY HH:mm')).toStrictEqual(
      '05/01/2022 00:00'
    )
    expect(result[2].endDate.format('DD/MM/YYYY HH:mm')).toStrictEqual(
      '05/01/2022 23:59'
    )
  })
})

describe('checkDateRangeCollisions', () => {
  test('should return false for non-colliding date ranges', () => {
    const ranges = [
      ['2023-01-01T10:00:00', '2023-02-01T18:00:00'],
      ['2023-03-01T08:00:00', '2023-04-01T16:00:00'],
      ['2023-05-01T12:00:00', '2023-06-01T20:00:00'],
    ]

    const result = checkDateRangeCollisions(ranges)

    expect(result).toBeFalsy()
  })

  test('should return true for colliding date ranges', () => {
    const ranges = [
      ['2023-01-01T10:00:00', '2023-02-01T18:00:00'],
      ['2023-01-15T14:00:00', '2023-02-15T22:00:00'],
      ['2023-02-01T12:00:00', '2023-03-01T20:00:00'],
    ]

    const result = checkDateRangeCollisions(ranges)

    expect(result).toBeTruthy()
  })

  test('should handle empty input', () => {
    const ranges = []

    const result = checkDateRangeCollisions(ranges)

    expect(result).toBeFalsy()
  })

  test('should detect collisions with time including minutes', () => {
    const ranges = [
      ['2023-01-01T08:30:00', '2023-01-01T10:00:00'],
      ['2023-01-01T09:45:00', '2023-01-01T11:00:00'],
      ['2023-01-01T10:30:00', '2023-01-01T12:00:00'],
    ]

    const result = checkDateRangeCollisions(ranges)

    expect(result).toBeTruthy()
  })
})

describe('formatDateInternal', () => {
  test('valid date should return a formatted string', () => {
    // Arrange
    const date = '2018-12-25 10:00'
    const options = {
      onlyTime: true,
      locale: 'fr',
      useFrenchTimeSeparator: true,
    }

    // Act
    const result = formatDateInternal(date, options)

    // Assert
    expect(result).toBe('10h00')
  })

  test('invalid date should return fallback value', () => {
    // Arrange
    const date = '2018-14-25 10:00'
    const options = {
      onlyTime: false,
      locale: 'fr',
      inputFormat: `YYYY-MM-DD HH[${timeSeparator}]mm`,
      fallbackValue: 'N/A',
    }

    // Act
    const result = formatDateInternal(date, options)

    // Assert
    expect(result).toBe('N/A')
  })
})

describe('getConsecutiveDays', () => {
  it('returns an array of consecutive days between two dates', () => {
    const startDate = new Date(2019, 0, 5)
    const endDate = new Date(2019, 0, 10)
    const result = getConsecutiveDays(startDate, endDate)

    expect(result.length).toBe(6)
    expect(result[0]).toEqual(startDate)
    expect(result[result.length - 1]).toEqual(endDate)
  })
})

describe('getDaysDifferenceBetweenMinMaxDates', () => {
  const arrayOfJsDates1 = [
    new Date('July 16, 2020'),
    new Date('December 15, 2021'),
  ]
  const arrayOfJsDates2 = [
    new Date('January 17, 2019'),
    new Date('April 20, 2018'),
  ]
  const arrayOfJsDates3 = [
    new Date('January 17, 2019'),
    new Date('January 20, 2019'),
  ]

  it('takes an array of Javascript dates and returns the difference in days between the min and max date', () => {
    expect(getDaysDifferenceBetweenMinMaxDates(arrayOfJsDates1)).toBe(518)
    expect(getDaysDifferenceBetweenMinMaxDates(arrayOfJsDates2)).toBe(273)
    expect(getDaysDifferenceBetweenMinMaxDates(arrayOfJsDates3)).toBe(3)
  })

  it('returns 0 if no dates are passed in', () => {
    expect(getDaysDifferenceBetweenMinMaxDates()).toBe(0)
  })
})

describe('getFormattedDurationFromSeconds', () => {
  it('returns the expected output given valid input', () => {
    expect(getFormattedDurationFromSeconds(30)).toEqual('0h00m30s')
  })
})

describe('formatTimeWithSeconds', () => {
  it('Should return seconds', () => {
    expect(formatTimeWithSeconds(53)).toStrictEqual('00:00:53')
  })
  it('Should return zeroes', () => {
    expect(formatTimeWithSeconds(0)).toStrictEqual('00:00:00')
  })
})

describe('formatTime', () => {
  it('Should support number as input', () => {
    expect(formatTime(4130)).toStrictEqual('01:08')
    expect(formatTime(4545)).toStrictEqual('01:15')
    expect(formatTime(25461)).toStrictEqual('07:04')
    expect(formatTime(3592)).toStrictEqual('00:59')
    expect(formatTime(8675)).toStrictEqual('02:24')
    expect(formatTime(34136)).toStrictEqual('09:28')
    expect(formatTime(67454)).toStrictEqual('18:44')
    expect(formatTime(33318)).toStrictEqual('09:15')
  })
})

describe('formatSeconds', () => {
  it('Should return seconds', () => {
    expect(formatSeconds(53)).toStrictEqual('00:00:53')
  })
  it('Should return zeroes', () => {
    expect(formatSeconds(0)).toStrictEqual('00:00:00')
  })
})

describe('getPastDatesUsingConsecutiveDays', () => {
  const fixedCurrentDate = moment('2024-11-28T00:00:00Z') // Fixed date for testing

  it('should return the correct array of consecutive dates for 1 day', () => {
    const result = getPastDatesUsingConsecutiveDays(1, fixedCurrentDate)
    expect(result).toEqual(['2024-11-27', '2024-11-28'])
  })

  it('should return the correct array of consecutive dates for 2 days', () => {
    const result = getPastDatesUsingConsecutiveDays(2, fixedCurrentDate)
    expect(result).toEqual(['2024-11-26', '2024-11-27', '2024-11-28'])
  })

  it('should return the correct array of consecutive dates for 3 days', () => {
    const result = getPastDatesUsingConsecutiveDays(3, fixedCurrentDate)
    expect(result).toEqual([
      '2024-11-25',
      '2024-11-26',
      '2024-11-27',
      '2024-11-28',
    ])
  })

  it('should return the correct array of consecutive dates for 7 days', () => {
    const result = getPastDatesUsingConsecutiveDays(7, fixedCurrentDate)
    expect(result).toEqual([
      '2024-11-21',
      '2024-11-22',
      '2024-11-23',
      '2024-11-24',
      '2024-11-25',
      '2024-11-26',
      '2024-11-27',
      '2024-11-28',
    ])
  })
})

describe('isMomentToday', () => {
  it('should return false if date is invalid', () => {
    const date = 'random string'

    const res = isMomentToday(date)

    expect(res).toEqual(false)
  })

  it('should return true if day is today', () => {
    const date = moment()

    const res = isMomentToday(date)

    expect(res).toEqual(true)
  })

  it('should return false if date is not today', () => {
    const date = '2023-01-01'

    const res = isMomentToday(date)

    expect(res).toEqual(false)
  })
})

describe('stringDatetimeToDate', () => {
  it('should convert a valid datetime string to a Date object', () => {
    const input = '2024-02-26 15:30:45'
    const expectedDate = moment(input, 'YYYY[-]MM[-]DD HH:mm:ss').toDate()

    const result = stringDatetimeToDate(input)

    expect(result).toBeInstanceOf(Date)
    expect(result.getTime()).toBe(expectedDate.getTime()) // Compare timestamps
  })

  it('should return an Invalid Date object for an invalid input', () => {
    const input = 'invalid-date'
    const result = stringDatetimeToDate(input)

    expect(result.toString()).toBe('Invalid Date')
  })

  it('should handle empty input gracefully', () => {
    const result = stringDatetimeToDate('')

    expect(result.toString()).toBe('Invalid Date')
  })
})

describe('getRelativeFormattedPastDate', () => {
  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should return a formatted past date for valid "days" unit', () => {
    const result = getRelativeFormattedPastDate('days', 5)

    const expectedDate = moment()
      .subtract(5, 'days')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')

    expect(result).toBe(expectedDate)
  })

  it('should return a formatted past date for valid "months" unit', () => {
    const result = getRelativeFormattedPastDate('months', 2)

    const expectedDate = moment()
      .subtract(2, 'months')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')

    expect(result).toBe(expectedDate)
  })

  it('should throw an error for invalid "unit"', () => {
    expect(() => {
      getRelativeFormattedPastDate('invalid_unit', 5)
    }).toThrow(
      'Invalid unit provided. Accepted units are: days, weeks, months, years'
    )
  })

  it('should handle negative number correctly (e.g., -1 day)', () => {
    const result = getRelativeFormattedPastDate('days', -1)

    const expectedDate = moment()
      .subtract(-1, 'days')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')

    expect(result).toBe(expectedDate)
  })

  it('should handle zero as number', () => {
    const result = getRelativeFormattedPastDate('days', 0)

    const expectedDate = moment()
      .subtract(0, 'days')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')

    expect(result).toBe(expectedDate)
  })
})

describe('filterValidDates', () => {
  it('should filter out invalid dates', () => {
    const input = [
      '2021-12-01', // valid date string
      'invalid-date', // invalid date string
      new Date('2021-12-01'), // valid date object
      new Date('invalid'), // invalid date object
      null, // invalid value
    ]

    const expected = ['2021-12-01', new Date('2021-12-01')]

    const result = filterValidDates(input)

    expect(result).toEqual(expected)
  })

  it('should return an empty array when no valid dates are present', () => {
    const input = ['invalid-date', null]
    const result = filterValidDates(input)

    expect(result).toEqual([])
  })

  it('should handle an empty array correctly', () => {
    const input = []
    const result = filterValidDates(input)

    expect(result).toEqual([])
  })

  it('should handle a valid date object correctly', () => {
    const validDate = new Date('2021-12-01')
    const input = [validDate]
    const result = filterValidDates(input)

    expect(result).toEqual([validDate])
  })

  it('should filter out invalid dates', () => {
    const invalidDate = new Date('invalid')
    const input = [invalidDate]
    const result = filterValidDates(input)

    expect(result).toEqual([])
  })

  it('should handle date strings in various formats', () => {
    const input = ['2021-12-01', '12/01/2021', '2021-12-32']

    const result = filterValidDates(input)

    // We expect only valid dates to pass through
    expect(result).toEqual(['2021-12-01', '12/01/2021'])
  })
})

describe('isIsoDate', () => {
  it('should return true for valid ISO date strings', () => {
    expect(isIsoDate('2023-10-01T12:00:00Z')).toBe(true)
    expect(isIsoDate('2023-10-01')).toBe(true)
    expect(isIsoDate('2023-10-01T12:00:00+02:00')).toBe(true)
  })

  it('should return false for invalid date strings', () => {
    expect(isIsoDate('2023-13-01')).toBe(false) // Invalid month
    expect(isIsoDate('2023-10-32')).toBe(false) // Invalid day
    expect(isIsoDate('2023-10-01T25:00:00Z')).toBe(false) // Invalid hour
  })

  it('should return false for non-date strings', () => {
    expect(isIsoDate('not-a-date')).toBe(false)
    expect(isIsoDate('')).toBe(false)
    expect(isIsoDate(null)).toBe(false)
    expect(isIsoDate(undefined)).toBe(false)
    expect(isIsoDate(12345)).toBe(false)
  })
})

describe('hasTodayInRanges', () => {
  const today = moment()
  const yesterday = moment().subtract(1, 'day')
  const tomorrow = moment().add(1, 'day')

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('returns false if input is not an array', () => {
    expect(hasTodayInRanges(null)).toBe(false)
    expect(hasTodayInRanges(undefined)).toBe(false)
    expect(hasTodayInRanges({})).toBe(false)
  })

  it('returns true if today matches the startDate of a range', () => {
    const ranges = [[today.clone(), tomorrow.clone()]]
    expect(hasTodayInRanges(ranges)).toBe(true)
  })

  it('returns true if today matches the endDate of a range', () => {
    const ranges = [[yesterday.clone(), today.clone()]]
    expect(hasTodayInRanges(ranges)).toBe(true)
  })

  it('returns false if today is not in any start or end date', () => {
    const ranges = [[yesterday.clone(), yesterday.clone()]]
    expect(hasTodayInRanges(ranges)).toBe(false)
  })

  it('returns false and logs warning if startDate or endDate is missing', () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {})
    const ranges = [
      [null, tomorrow.clone()],
      [today.clone(), null],
    ]
    expect(hasTodayInRanges(ranges)).toBe(false)
    expect(consoleSpy).toHaveBeenCalledWith(
      'missing startDate or endDate in range'
    )
  })

  it('handles mix of valid and invalid ranges', () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {})
    const ranges = [
      [null, tomorrow.clone()],
      [yesterday.clone(), today.clone()],
    ]
    expect(hasTodayInRanges(ranges)).toBe(true)
    expect(consoleSpy).toHaveBeenCalledTimes(1)
  })
})

describe('setTime', () => {
  it('should correctly set the time when valid', () => {
    const m = moment('2025-04-28T00:00:00') // Start of the day
    const time = '15:30'

    const result = setTime(m, time)
    expect(result.format('HH:mm')).toBe('15:30') // Check if time is set correctly
  })

  it('should handle invalid time format', () => {
    const m = moment('2025-04-28T00:00:00')
    const time = '15:30:45' // Invalid format

    const result = setTime(m, time)
    expect(result.isSame(m)).toBe(true) // The result should be the same as the input moment
  })

  it('should handle invalid hour or minute values', () => {
    const m = moment('2025-04-28T00:00:00')

    let invalidTime = '25:30' // Invalid hour
    let result = setTime(m, invalidTime)
    expect(result.isSame(m)).toBe(true) // The result should be the same as the input moment

    invalidTime = '15:70' // Invalid minute
    result = setTime(m, invalidTime)
    expect(result.isSame(m)).toBe(true) // The result should be the same as the input moment
  })

  it('should reset seconds to 0', () => {
    const m = moment('2025-04-28T15:30:45')
    const time = '15:30'

    const result = setTime(m, time)
    expect(result.format('HH:mm:ss')).toBe('15:30:00') // Seconds should be reset to 0
  })

  it('should handle edge cases like start of the day (00:00)', () => {
    const m = moment('2025-04-28T00:00:00')
    const time = '00:00'

    const result = setTime(m, time)
    expect(result.format('HH:mm')).toBe('00:00') // Should handle the start of the day correctly
  })

  it('should handle edge cases like end of the day (23:59)', () => {
    const m = moment('2025-04-28T23:59:59')
    const time = '23:59'

    const result = setTime(m, time)
    expect(result.format('HH:mm')).toBe('23:59') // Should handle the end of the day correctly
  })
})

describe('sortDatesAscending', () => {
  it('should correctly sort valid dates in ascending order', () => {
    const dates = [
      '2025-04-28T10:00:00',
      '2025-04-25T10:00:00',
      '2025-04-27T10:00:00',
    ]

    const sortedDates = sortDatesAscending(dates)
    expect(sortedDates[0]).toBe('2025-04-25T10:00:00')
    expect(sortedDates[1]).toBe('2025-04-27T10:00:00')
    expect(sortedDates[2]).toBe('2025-04-28T10:00:00')
  })

  it('should filter out invalid dates and sort the valid ones', () => {
    const dates = [
      '2025-04-28T10:00:00',
      'invalid-date',
      '2025-04-25T10:00:00',
      'invalid-date-2',
      '2025-04-27T10:00:00',
    ]

    const sortedDates = sortDatesAscending(dates)
    expect(sortedDates[0]).toBe('2025-04-25T10:00:00')
    expect(sortedDates[1]).toBe('2025-04-27T10:00:00')
    expect(sortedDates[2]).toBe('2025-04-28T10:00:00')
  })

  it('should throw an error if the input is not an array', () => {
    const invalidInput = '2025-04-28T10:00:00' // Not an array
    expect(() => sortDatesAscending(invalidInput)).toThrow(
      'Input must be an array of dates.'
    )
  })

  it('should throw an error if no valid dates are provided', () => {
    const invalidDates = ['invalid-date', 'another-invalid-date']
    expect(() => sortDatesAscending(invalidDates)).toThrow(
      'No valid dates found.'
    )
  })

  it('should handle an empty array gracefully', () => {
    const emptyArray = []
    expect(() => sortDatesAscending(emptyArray)).toThrow(
      'No valid dates found.'
    )
  })

  it('should handle an array with one valid date', () => {
    const singleDate = ['2025-04-28T10:00:00']
    const sortedDates = sortDatesAscending(singleDate)
    expect(sortedDates.length).toBe(1)
    expect(sortedDates[0]).toBe('2025-04-28T10:00:00')
  })

  it('should handle an array with one invalid date', () => {
    const singleInvalidDate = ['invalid-date']
    expect(() => sortDatesAscending(singleInvalidDate)).toThrow(
      'No valid dates found.'
    )
  })

  it('should handle the edge case of an already sorted array', () => {
    const sortedDates = [
      '2025-04-25T10:00:00',
      '2025-04-27T10:00:00',
      '2025-04-28T10:00:00',
    ]

    const result = sortDatesAscending(sortedDates)
    expect(result).toEqual(sortedDates) // No change, since it's already sorted
  })
})

describe('updateTimeIfBeforeOrAfter', () => {
  const baseDate = '2025-01-20'

  it('updates time if "to" is before original and direction is "before"', () => {
    const original = `${baseDate} 14:30:00`
    const result = updateTimeIfBeforeOrAfter(original, '12:00', 'before')
    expect(result).toBe(`${baseDate} 12:00:00`)
  })

  it('does not update if "to" is after original and direction is "before"', () => {
    const original = `${baseDate} 14:30:00`
    const result = updateTimeIfBeforeOrAfter(original, '15:00', 'before')
    expect(result).toBe(original)
  })

  it('updates time if "to" is after original and direction is "after"', () => {
    const original = `${baseDate} 10:00:00`
    const result = updateTimeIfBeforeOrAfter(original, '11:45', 'after')
    expect(result).toBe(`${baseDate} 11:45:00`)
  })

  it('does not update if "to" is before original and direction is "after"', () => {
    const original = `${baseDate} 14:00:00`
    const result = updateTimeIfBeforeOrAfter(original, '12:00', 'after')
    expect(result).toBe(original)
  })

  it('returns original if datetimeStr is invalid', () => {
    const invalidDatetime = 'not-a-date'
    const result = updateTimeIfBeforeOrAfter(invalidDatetime, '12:00', 'before')
    expect(result).toBe(invalidDatetime)
  })

  it('returns original if timeStr is invalid', () => {
    const original = `${baseDate} 14:00:00`
    const result = updateTimeIfBeforeOrAfter(original, 'invalid', 'before')
    expect(result).toBe(original)
  })

  it('returns original if direction is invalid', () => {
    const original = `${baseDate} 14:00:00`
    const result = updateTimeIfBeforeOrAfter(original, '12:00', 'invalid')
    expect(result).toBe(original)
  })
})

describe('getFormattedSelectedTimeRangeStringByDate', () => {
  beforeEach(() => {
    jest.restoreAllMocks()
  })

  it('returns empty string for invalid date', () => {
    jest.spyOn({ isIsoDate }, 'isIsoDate').mockReturnValue(false)

    const result = getFormattedSelectedTimeRangeStringByDate('invalid-date')

    expect(result).toBe('')
  })

  it('returns empty string for invalid time range', () => {
    jest.spyOn({ isIsoDate }, 'isIsoDate').mockReturnValue(true)

    const fakeSearchService = {
      getRangeTimeSelected: jest.fn().mockReturnValue(null),
    }

    const result = getFormattedSelectedTimeRangeStringByDate(
      '2023-05-20T12:00:00Z',
      fakeSearchService
    )

    expect(result).toBe('')
  })

  it('returns formatted time range when valid', () => {
    jest.spyOn({ isIsoDate }, 'isIsoDate').mockReturnValue(true)
    jest
      .spyOn({ formatTime }, 'formatTime')
      .mockImplementation((time) => new Date(time).toISOString().substr(11, 5))

    const fakeSearchService = {
      getRangeTimeSelected: jest.fn().mockReturnValue({
        start: adjustDateWithTimezone('2023-05-20T10:00:00Z'),
        end: adjustDateWithTimezone('2023-05-20T11:00:00Z'),
      }),
    }

    const result = getFormattedSelectedTimeRangeStringByDate(
      '2023-05-20T12:00:00Z',
      fakeSearchService
    )

    expect(result).toBe('10:00 - 11:00')
  })
})

describe('stripMillisecondsFromDate - throwing version', () => {
  it('returns the same date with milliseconds set to 0 for valid Date input', () => {
    const originalDate = new Date('2025-05-28T12:34:56.789Z')
    const result = stripMillisecondsFromDate(originalDate)

    expect(result).toBeInstanceOf(Date)
    expect(result.getTime()).toBe(
      new Date('2025-05-28T12:34:56.000Z').getTime()
    )
  })

  it('returns date with milliseconds stripped for valid date string input', () => {
    const dateString = '2025-05-28T12:34:56.789Z'
    const result = stripMillisecondsFromDate(dateString)

    expect(result).toBeInstanceOf(Date)
    expect(result.getMilliseconds()).toBe(0)
  })

  it('returns date with milliseconds stripped for valid moment input', () => {
    const momentDate = moment('2025-05-28T12:34:56.789Z')
    const result = stripMillisecondsFromDate(momentDate)

    expect(result).toBeInstanceOf(Date)
    expect(result.getMilliseconds()).toBe(0)
  })

  it('throws error if date input is null or undefined', () => {
    expect(() => stripMillisecondsFromDate(null)).toThrow(
      'Date input is required'
    )
    expect(() => stripMillisecondsFromDate(undefined)).toThrow(
      'Date input is required'
    )
  })

  it('throws error if date input is invalid', () => {
    expect(() => stripMillisecondsFromDate('not-a-date')).toThrow(
      'Invalid date input'
    )
  })
})

describe('normalizeDateToMidnight', () => {
  it('returns midnight for a valid date string', () => {
    const input = '2025-06-10T15:30:00Z'
    const result = normalizeDateToMidnight(input)
    const expected = moment(input).startOf('day').toDate()

    expect(result.toISOString()).toBe(expected.toISOString())
  })

  it('returns midnight for a Date object input', () => {
    const input = new Date('2025-06-10T23:59:59Z')
    const result = normalizeDateToMidnight(input)
    const expected = moment(input).startOf('day').toDate()

    expect(result.toISOString()).toBe(expected.toISOString())
  })

  it('throws if input is null or undefined', () => {
    expect(() => normalizeDateToMidnight(null)).toThrow(
      'Date input is required'
    )
    expect(() => normalizeDateToMidnight(undefined)).toThrow(
      'Date input is required'
    )
  })

  it('throws if input is an invalid date string', () => {
    expect(() => normalizeDateToMidnight('invalid-date')).toThrow(
      'Invalid date input'
    )
  })

  it('works with moment object input directly', () => {
    const input = moment('2025-06-10T12:34:56')
    const result = normalizeDateToMidnight(input)
    const expected = input.clone().startOf('day').toDate()

    expect(result.toISOString()).toBe(expected.toISOString())
  })
})

describe('isDateAfterDate', () => {
  let warnSpy

  beforeEach(() => {
    warnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    warnSpy.mockRestore()
  })

  it('returns false and warns if either date is missing', () => {
    expect(isDateAfterDate(null, '10:00')).toBe(false)
    expect(isDateAfterDate('10:00', null)).toBe(false)
    expect(isDateAfterDate(undefined, '10:00')).toBe(false)
    expect(warnSpy).toHaveBeenCalledTimes(3)
    expect(warnSpy).toHaveBeenCalledWith(
      'Both dateA and dateB must be provided'
    )
  })

  it('returns true if dateA is after dateB', () => {
    expect(isDateAfterDate('11:00', '10:00')).toBe(true)
    expect(warnSpy).not.toHaveBeenCalled()
  })

  it('returns false if dateA is before dateB', () => {
    expect(isDateAfterDate('09:00', '10:00')).toBe(false)
    expect(warnSpy).not.toHaveBeenCalled()
  })

  it('returns false if dateA equals dateB', () => {
    expect(isDateAfterDate('10:00', '10:00')).toBe(false)
    expect(warnSpy).not.toHaveBeenCalled()
  })

  it('works with custom format (e.g., YYYY-MM-DD)', () => {
    expect(isDateAfterDate('2025-06-12', '2025-06-11', 'YYYY-MM-DD')).toBe(true)
    expect(isDateAfterDate('2025-06-10', '2025-06-11', 'YYYY-MM-DD')).toBe(
      false
    )
    expect(warnSpy).not.toHaveBeenCalled()
  })
})

describe('splitDateRangeByDay', () => {
  const format = 'YYYY-MM-DDTHH:mm:ss'

  it('should split a range across two days correctly', () => {
    const start = '2025-07-27T20:00:00'
    const end = '2025-07-28T08:00:00'

    const result = splitDateRangeByDay(start, end, format)

    expect(result).toEqual([
      ['2025-07-27T20:00:00', '2025-07-27T23:59:59'],
      ['2025-07-28T00:00:00', '2025-07-28T08:00:00'],
    ])
  })

  it('should return a single range when start and end are on the same day', () => {
    const start = '2025-07-28T10:00:00'
    const end = '2025-07-28T18:00:00'

    const result = splitDateRangeByDay(start, end, format)

    expect(result).toEqual([['2025-07-28T10:00:00', '2025-07-28T18:00:00']])
  })

  it('should return empty array if start and end are the same', () => {
    const ts = '2025-07-28T10:00:00'
    const result = splitDateRangeByDay(ts, ts, format)
    expect(result).toEqual([])
  })

  it('should handle a full day range', () => {
    const start = '2025-07-28T00:00:00'
    const end = '2025-07-28T23:59:59'

    const result = splitDateRangeByDay(start, end, format)

    expect(result).toEqual([['2025-07-28T00:00:00', '2025-07-28T23:59:59']])
  })

  it('should return empty array if start is after end', () => {
    const start = '2025-07-29T10:00:00'
    const end = '2025-07-28T10:00:00'

    const result = splitDateRangeByDay(start, end, format)
    expect(result).toEqual([])
  })

  it('should return empty array for invalid date strings', () => {
    const result1 = splitDateRangeByDay(
      'invalid-date',
      '2025-07-28T10:00:00',
      format
    )
    expect(result1).toEqual([])

    const result2 = splitDateRangeByDay(
      '2025-07-28T10:00:00',
      'not-a-date',
      format
    )
    expect(result2).toEqual([])
  })

  it('should return empty array for null or undefined input', () => {
    expect(splitDateRangeByDay(null, '2025-07-28T10:00:00', format)).toEqual([])
    expect(splitDateRangeByDay('2025-07-28T10:00:00', null, format)).toEqual([])
    expect(splitDateRangeByDay(undefined, undefined, format)).toEqual([])
  })

  it('should handle a multi-day span of 3 days', () => {
    const start = '2025-07-27T10:00:00'
    const end = '2025-07-29T05:00:00'

    const result = splitDateRangeByDay(start, end, format)

    expect(result).toEqual([
      ['2025-07-27T10:00:00', '2025-07-27T23:59:59'],
      ['2025-07-28T00:00:00', '2025-07-28T23:59:59'],
      ['2025-07-29T00:00:00', '2025-07-29T05:00:00'],
    ])
  })
})

describe('doRangesOverlap', () => {
  it('returns true for full overlap', () => {
    expect(
      doRangesOverlap(
        '2025-07-22T00:00:00',
        '2025-07-23T23:59:59',
        '2025-07-22T12:00:00',
        '2025-07-23T10:00:00'
      )
    ).toBe(true)
  })

  it('returns true for partial overlap at start', () => {
    expect(
      doRangesOverlap(
        '2025-07-23T00:00:00',
        '2025-07-23T23:59:59',
        '2025-07-22T18:00:00',
        '2025-07-23T10:00:00'
      )
    ).toBe(true)
  })

  it('returns true for partial overlap at end', () => {
    expect(
      doRangesOverlap(
        '2025-07-23T00:00:00',
        '2025-07-23T12:00:00',
        '2025-07-23T11:00:00',
        '2025-07-24T10:00:00'
      )
    ).toBe(true)
  })

  it('returns true when ranges touch exactly at the edges', () => {
    expect(
      doRangesOverlap(
        '2025-07-23T00:00:00',
        '2025-07-23T10:00:00',
        '2025-07-23T10:00:00',
        '2025-07-23T12:00:00'
      )
    ).toBe(true)
  })

  it('returns false for non-overlapping ranges', () => {
    expect(
      doRangesOverlap(
        '2025-07-22T00:00:00',
        '2025-07-22T10:00:00',
        '2025-07-22T11:00:00',
        '2025-07-22T12:00:00'
      )
    ).toBe(false)
  })

  it('returns false for completely invalid date inputs', () => {
    expect(doRangesOverlap('invalid', 'also invalid', 'nope', 'yep')).toBe(
      false
    )
  })

  it('returns false when one range has end before start', () => {
    expect(
      doRangesOverlap(
        '2025-07-23T10:00:00',
        '2025-07-23T08:00:00', // invalid range
        '2025-07-23T07:00:00',
        '2025-07-23T09:00:00'
      )
    ).toBe(false)
  })
})
