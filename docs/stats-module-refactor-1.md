# StatsModule Refactoring Plan (Phase 1: Table Focus)

This document outlines a refactoring plan for the `StatsModule.vue` component, with an initial focus on extracting the AG Grid table and related logic into a separate, reusable component.

## Current State

The `StatsModule.vue` component is a large single-file component with the following responsibilities:

*   **State Management:** Interacts with Vuex for managing filters, data, and UI state.
*   **Data Fetching:** Uses composables to fetch and pool data from the API.
*   **Filtering:** Renders and manages complex filtering options for the stats data.
*   **Table Display:** Utilizes `ag-grid-vue` to display the statistical data, including complex column definitions and value getters.
*   **Data Exporting:** Provides functionality to export the grid data to CSV and Excel formats.

This monolithic structure makes the component difficult to maintain, test, and reason about.

## Refactoring Goals

*   Improve component organization and separation of concerns.
*   Increase reusability of the table component.
*   Simplify the main `StatsModule.vue` component.
*   Make the code easier to understand and maintain.

## Refactoring Plan

### Step 1: Create a New Directory

Create a new directory to house the refactored `StatsModule` components: `src/components/stats-module/`.

### Step 2: Extract the Stats Table Component

1.  **Create a new component:** `src/components/stats-module/StatsTable.vue`
2.  **Move the `ag-grid-vue` template:** Transfer the `<ag-grid-vue>` component and its container from `StatsModule.vue` to `StatsTable.vue`.
3.  **Props:** The new component will accept the following props:
    *   `rowData`: An array of data to be displayed in the table.
    *   `columnDefs`: An array of column definitions for the grid.
    *   `loading`: A boolean to indicate when data is being loaded.
    *   `export-table-config`: An object with the configuration for the export buttons
4.  **Events:** The component will emit the following events:
    *   `grid-ready`: When the AG Grid instance is ready.
    *   `export`: When the user clicks on an export button.
5.  **Logic:** Move the following logic from `StatsModule.vue` to `StatsTable.vue`:
    *   `onGridReady` function.
    *   `defaultColDef` ref.
    *   `statusBarConfig` computed property.
    *   Export button logic (`exportCSV`, `exportExcel`).

### Step 3: Extract the Filters Component

1.  **Create a new component:** `src/components/stats-module/StatsFilters.vue`
2.  **Move the filter template:** Transfer the filter-related HTML from the `#menu` slot in `StatsModule.vue` to `StatsFilters.vue`.
3.  **Props:** The component will accept the following props:
    *   `circuits`: An array of available circuits.
    *   `circuit-categories`: An array of available circuit categories.
    *   `vehicles`: An array of available vehicles.
4.  **v-model:** The component will use `v-model` to manage the selected filters.
5.  **Logic:** Move the following logic from `StatsModule.vue` to `StatsFilters.vue`:
    *   `selectedCircuits`, `selectedCircuitCategories`, `selectedVehicles` refs.
    *   `selectAllCircuits`, `deselectAllCircuits`, etc. methods.

### Step 4: Refactor `StatsModule.vue`

1.  **Template:**
    *   Replace the old filter and table sections with the new components: `<StatsFilters ... />` and `<StatsTable ... />`.
    *   Pass the required props to the new components and listen for their events.
2.  **Script:**
    *   Import the new components.
    *   Remove the logic that was moved to the new components.
    *   The main component will now be responsible for:
        *   Fetching data from the API (using the existing composables).
        *   Managing the overall state of the module.
        *   Passing data and configuration to the child components.

## Future Work (Phase 2)

*   Extract the action buttons (e.g., "Clear Cache") into a separate `StatsActions.vue` component.
*   Further break down the `StatsFilters.vue` component if it remains too complex.
*   Refactor the Vuex store module (`operationalStatistics`) to better align with the new component structure.
