# Stats Module – Data Export Feature (CSV & Excel)

## 1. Goal
Enable users to **export the exact data currently shown in the Stats module grids** (Circuit view & Operation view) to **CSV** and **Excel (XLSX)**, using the already-cached local dataset so no extra API calls are performed.

## 2. User Experience
1. A tiny toolbar (same visual style as `ExportToolbar.vue`) appears **inside the AG Grid footer area** (bottom-right, next to the pagination controls).
2. Two icon buttons are displayed:
   * **CSV** – exports to comma-separated file.
   * **Excel** – exports to `.xlsx` (using `write-excel-file`).
3. While exporting, the existing global `$loader.showWhile()` overlay is shown so the user cannot trigger multiple exports.

## 3. High-level Approach (Revised)

Analysis of the codebase revealed an existing composable, `src/composables/useOperationalStatisticsExporter.js`, which already handles CSV exports for this module. We will **reuse and extend** this composable instead of creating a new one from scratch.

| Layer | Responsibility | File(s) to Modify/Create |
|-------|----------------|-------------|
| **Composable** | **Extend** `useOperationalStatisticsExporter` to support **Excel** format. Modify it to accept an AG Grid API reference to extract data directly from the grid, instead of `localStorage`. | `src/composables/useOperationalStatisticsExporter.js` |
| **UI Component** | Create a new, reusable footer toolbar for AG Grid with CSV/Excel buttons. It will invoke the extended composable. | `src/components/shared/AgGrid/GridExportFooter.vue` (New) |

This approach promotes code reuse and reduces the amount of new code needed.

## 4. Detailed Design
### 4.1 Composable – Extending `useOperationalStatisticsExporter.js`

The existing composable will be refactored to:
1.  Accept the AG Grid `api` and `columnApi` as arguments.
2.  Extract data directly from the grid, making it the single source of truth.
3.  Add a new `exportDataToExcel` function.
4.  Rely on the existing, robust `exportService` for file generation.

```js
// src/composables/useOperationalStatisticsExporter.js (Refactored)
import { exportDatatable } from '@/services/export-service';

export default function useOperationalStatisticsExporter() {

  const exportGridData = ({ gridApi, columnApi, tableCode, format }) => {
    const { rows, columns } = extractGridData(gridApi, columnApi);

    if (!rows || rows.length === 0) {
      // Show notification: No data to export
      return;
    }

    exportDatatable({
      data: rows,
      columns: columns,
      tableCode: tableCode,
      format: format, // 'csv' or 'xlsx'
    });
  };

  return {
    exportGridData,
  };
}

// This helper function can live inside the composable or be moved to a new service
function extractGridData(gridApi, columnApi) {
    const columns = columnApi.getAllDisplayedColumns().map(col => ({
        headerName: col.getColDef().headerName,
        field: col.getColDef().field
    }));

    const rows = [];
    gridApi.forEachNodeAfterFilterAndSort(node => {
        rows.push(node.data);
    });

    return { rows, columns: columns.map(c => c.headerName) };
}
```

### 4.2 Service Layer – No New Service Needed

The data extraction logic (`extractGridData`) will be placed inside the refactored composable for simplicity. The actual file generation is already handled by the existing `export-service.js`, so **no new service file is required.** This simplifies the plan significantly.

### 4.3 UI – `GridExportFooter.vue`
Props:
```ts
props: {
  params: { type: Object, required: true }
}
```
* Renders two `<em>` icons (same classes as `ExportToolbar.vue`) or small `<ButtonWrapper>` for consistency.
* Click handlers call composable with provided refs.
* Component is **framework-agnostic** so it can be reused elsewhere.

### 4.4 Integrating into Stats Grids
The AG Grid documentation confirms the best practice is to use a custom **Status Panel Component** registered with the grid's `statusBar`. This avoids adding extra markup outside the grid and keeps the layout clean.

1.  **Register the component globally:**
    For AG Grid to find our Vue component by name, it must be registered globally.
    ```js
    // in src/main.js
    import GridExportFooter from '@/components/shared/AgGrid/GridExportFooter.vue'
    app.component('GridExportFooter', GridExportFooter)
    ```

2.  **Configure the `statusBar` in `StatsModule.vue`:**
    Update the grid options to include the `statusBar` configuration. The `gridApi` and `columnApi` will be passed automatically as component props by AG Grid.
    ```js
    // in StatsModule.vue script setup
    const gridOptions = {
      // ... other options
      statusBar: {
        statusPanels: [
          {
            statusPanel: 'agTotalAndFilteredRowCountComponent',
            align: 'left',
          },
          {
            statusPanel: 'GridExportFooter',
            align: 'right',
            statusPanelParams: {
              tableCode: 'statsCircuit' // or 'statsOperation'
            }
          }
        ]
      },
      // ... other options
    }
    ```
    The `tableCode` will be passed via `statusPanelParams`. The `GridExportFooter` component will receive `params.api`, `params.columnApi`, and `params.tableCode`.

3.  **Update `GridExportFooter.vue` to accept `params`:**
    The component will be initialized by AG Grid, so its props will be available under a `params` object.
    ```js
    // in GridExportFooter.vue
    props: {
        params: { type: Object, required: true }
    }
    // Access via: props.params.api, props.params.columnApi, props.params.tableCode
    ```

### 4.5 i18n
Add two new keys under `operationalStatistics.exports.*` for tooltips and maybe filenames.

## 5. Minimal Changes to Existing Files
* **`StatsModule.vue`** – add refs + `<GridExportFooter>` component import; that’s it.
* **No alterations** to existing `ExportToolbar.vue`; logic is shared via the new composable/service.

## 6. Edge Cases & Validation
* If `rows.length === 0` show a toast: *“Aucun résultat à exporter.”* and abort export.
* File names follow existing convention in `exportService` (`tableCode-<DDMMYYYY>-<HHmm>.csv|xlsx`).
* Values containing HTML (icons) should be converted to a display string (e.g. ✓/✗) – rely on `valueFormatter` already applied to the column; fallback to raw data.

## 7. Testing Checklist
- [ ] Export from Circuit view with several pages → file content matches visible rows & columns.
- [ ] Export after filtering/searching → respects current dataset.
- [ ] Export when no data → toast & no file.
- [ ] Large dataset (>10k rows) still exports quickly (<3s) and file size reasonable.

## 8. Future Improvements
* Add **PDF export** reusing `usePdfExport` composable.
* Allow user to choose **visible columns vs all columns** via a dropdown.
* Remember last export format in **localStorage**.

---
*Author: Front-end team*
*Created: 24-06-2025*
